// 简单测试 removeCookie 函数
function removeCookie(headers, name, domain) {
  // 通过设置过期时间为过去的时间来删除cookie
  const expiredDate = new Date(0).toUTCString(); // 1970-01-01
  const cookieValue = `${name}=; Domain=${domain}; Path=/; Expires=${expiredDate}; HttpOnly`;
  headers.append('Set-Cookie', cookieValue);
}

// 模拟 Headers 类
class MockHeaders {
  constructor() {
    this.headers = {};
  }
  
  append(name, value) {
    if (!this.headers[name]) {
      this.headers[name] = [];
    }
    this.headers[name].push(value);
  }
  
  get(name) {
    return this.headers[name] ? this.headers[name][0] : null;
  }
  
  getAll(name) {
    return this.headers[name] || [];
  }
}

// 测试用例
console.log('测试 removeCookie 函数：\n');

const testCases = [
  {
    name: '__atom_acess_params__',
    domain: 'fliggy.com',
    description: '删除 atom 访问参数 cookie'
  },
  {
    name: '__atom_miniapp_env__',
    domain: 'fliggy.com',
    description: '删除 atom 小程序环境 cookie'
  },
  {
    name: 'test_cookie',
    domain: 'example.com',
    description: '删除测试 cookie'
  }
];

testCases.forEach((testCase, index) => {
  const headers = new MockHeaders();
  
  console.log(`测试 ${index + 1}: ${testCase.description}`);
  console.log(`Cookie 名称: ${testCase.name}`);
  console.log(`域名: ${testCase.domain}`);
  
  // 调用 removeCookie 函数
  removeCookie(headers, testCase.name, testCase.domain);
  
  // 检查结果
  const setCookieHeaders = headers.getAll('Set-Cookie');
  console.log(`生成的 Set-Cookie 头: ${setCookieHeaders[0]}`);
  
  // 验证格式
  const expectedPattern = new RegExp(`^${testCase.name}=; Domain=${testCase.domain}; Path=/; Expires=.*; HttpOnly$`);
  const isValid = expectedPattern.test(setCookieHeaders[0]);
  
  console.log(`格式验证: ${isValid ? '✅ 通过' : '❌ 失败'}`);
  console.log('---');
});

// 验证过期时间是否正确设置为1970年
const headers = new MockHeaders();
removeCookie(headers, 'test', 'example.com');
const setCookieValue = headers.get('Set-Cookie');
const expiresMatch = setCookieValue.match(/Expires=([^;]+)/);
if (expiresMatch) {
  const expiresDate = new Date(expiresMatch[1]);
  console.log(`过期时间验证:`);
  console.log(`设置的过期时间: ${expiresMatch[1]}`);
  console.log(`解析后的时间: ${expiresDate.toISOString()}`);
  console.log(`是否为1970年: ${expiresDate.getFullYear() === 1970 ? '✅ 正确' : '❌ 错误'}`);
}
