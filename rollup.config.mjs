import { nodeResolve } from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
import replace from '@rollup/plugin-replace';

const DATE = new Date();

export default {
  input: 'src/index.ts',
  output: {
    file: 'build/index.js',
    format: 'cjs',
    strict: false
  },
  plugins: [
    typescript(),
    nodeResolve(),
    commonjs(),
    replace({
      preventAssignment: true,
      'process.env.BUILD_DATE': `${DATE.getFullYear().toString().slice(-2)}${('0' + (DATE.getMonth() + 1).toString()).slice(-2)}${('0' + DATE.getDate().toString()).slice(-2)}`,
      'process.env.BUILD_TIME': `"${DATE.getFullYear()}/${DATE.getMonth() + 1}/${DATE.getDate()} ${DATE.getHours()}:${DATE.getMinutes()}:${DATE.getSeconds()}"`,
    })
  ],
};
