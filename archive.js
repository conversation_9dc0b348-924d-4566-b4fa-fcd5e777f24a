
/**
 * 检查是否手机
 * https://developer.chrome.com/multidevice/user-agent
 * @param {String} ua
 */
function checkIfMobile(ua) {
  if (!ua) {
    return false;
  }
  ua = ua.toLowerCase();
  // Android 手机
  if (ua.includes('android') && ua.includes('mobile')) {
    return true;
  }
  if (!MATCH_UA.some(match => ua.includes(match))) {
    return false;
  }
  // https://yuque.antfin-inc.com/velocity_cross-end-web/docs/wiki_app-ua
  if (ua.includes('-pd')) {
    return false;
  }
  return true;
}


