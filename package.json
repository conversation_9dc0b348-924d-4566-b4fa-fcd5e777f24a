{"name": "", "version": "2.35.21", "description": "", "scripts": {"build": "rollup -c", "test": "jest"}, "devDependencies": {"@ali/eslint-config-fliggy": "^1.0.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-typescript": "^11.1.6", "@types/ali-oss": "^6.16.11", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "babel-eslint": "^8.2.5", "babel-jest": "^24.8.0", "eslint": "^6.8.0", "eslint-plugin-babel": "^5.1.0", "eslint-plugin-jsx-plus": "^0.1.0", "eslint-plugin-react": "^7.10.0", "jest": "^24.8.0", "rollup": "^4.14.3", "tslib": "^2.6.2", "typescript": "^5.4.5"}, "repository": "**************************:trip/fliggy-common-er.git"}