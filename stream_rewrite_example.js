// 方法1：缓冲所有内容后统一改写
async function rewriteStreamResponse1(response) {
  const reader = response.clone().body.getReader();
  const decoder = new TextDecoder();
  let fullContent = '';
  
  try {
    // 读取所有流内容
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      if (value) {
        const chunk = decoder.decode(value, { stream: true });
        fullContent += chunk;
      }
    }
    
    // 在最后改写HTML
    const modifiedContent = fullContent.replace(
      '</body>',
      '<script>console.log("Added at the end!");</script></body>'
    );
    
    return new Response(modifiedContent, {
      headers: response.headers,
      status: response.status
    });
    
  } finally {
    reader.releaseLock();
  }
}

// 方法2：流式处理，检测结束标签并插入
async function rewriteStreamResponse2(response) {
  const reader = response.clone().body.getReader();
  const decoder = new TextDecoder();
  const encoder = new TextEncoder();
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();
  
  let buffer = '';
  const endTag = '</body>';
  const insertScript = '<script>console.log("Inserted before body end!");</script>';
  let scriptInserted = false;
  
  const processStream = async () => {
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          // 流结束，如果还没插入脚本，直接在末尾添加
          if (!scriptInserted && buffer) {
            const finalContent = buffer + insertScript;
            await writer.write(encoder.encode(finalContent));
          } else if (buffer) {
            await writer.write(encoder.encode(buffer));
          }
          break;
        }
        
        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          
          if (!scriptInserted) {
            const endTagIndex = buffer.indexOf(endTag);
            if (endTagIndex > -1) {
              // 找到结束标签，插入脚本
              const beforeTag = buffer.slice(0, endTagIndex);
              const afterTag = buffer.slice(endTagIndex);
              const modifiedContent = beforeTag + insertScript + afterTag;
              
              await writer.write(encoder.encode(modifiedContent));
              scriptInserted = true;
              buffer = '';
            } else {
              // 未找到结束标签，保留可能被切割的部分
              if (buffer.length > endTag.length * 2) {
                const writeLength = buffer.length - endTag.length;
                const toWrite = buffer.slice(0, writeLength);
                await writer.write(encoder.encode(toWrite));
                buffer = buffer.slice(writeLength);
              }
            }
          } else {
            // 脚本已插入，直接转发
            await writer.write(value);
          }
        }
      }
    } finally {
      reader.releaseLock();
      await writer.close();
    }
  };
  
  processStream();
  
  return new Response(readable, {
    headers: response.headers,
    status: response.status
  });
}

// 方法3：使用 HTMLStream (如果环境支持)
function rewriteStreamResponse3(response) {
  const htmlStream = new HTMLStream(response.body, [
    [
      'body',
      {
        element(element) {
          // 在 body 结束前插入脚本
          element.append('<script>console.log("Added via HTMLStream!");</script>', {
            html: true
          });
        }
      }
    ]
  ]);
  
  return new Response(htmlStream, {
    headers: response.headers,
    status: response.status
  });
}

// 测试用的流式响应生成器
function createStreamResponse() {
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();
  
  const writeData = async () => {
    try {
      await writer.write(new TextEncoder().encode('<html><head><title>Test</title></head><body>'));
      await new Promise(resolve => setTimeout(resolve, 50));
      
      await writer.write(new TextEncoder().encode('<h1>Hello World</h1>'));
      await new Promise(resolve => setTimeout(resolve, 30));
      
      await writer.write(new TextEncoder().encode('<p>This is streaming content</p>'));
      await new Promise(resolve => setTimeout(resolve, 20));
      
      await writer.write(new TextEncoder().encode('</body></html>'));
      await writer.close();
    } catch (err) {
      console.error('写入出错:', err);
    }
  };
  
  writeData();
  
  return new Response(readable, {
    headers: { 'content-type': 'text/html; charset=utf-8' },
    status: 200
  });
}

// 使用示例
async function testRewrite() {
  const originalResponse = createStreamResponse();
  
  console.log('=== 测试方法1：缓冲改写 ===');
  const rewritten1 = await rewriteStreamResponse1(originalResponse);
  const content1 = await rewritten1.text();
  console.log('改写后内容1:', content1);
  
  console.log('\n=== 测试方法2：流式改写 ===');
  const originalResponse2 = createStreamResponse();
  const rewritten2 = await rewriteStreamResponse2(originalResponse2);
  const content2 = await rewritten2.text();
  console.log('改写后内容2:', content2);
}

// 导出函数供使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    rewriteStreamResponse1,
    rewriteStreamResponse2,
    rewriteStreamResponse3,
    createStreamResponse,
    testRewrite
  };
}
