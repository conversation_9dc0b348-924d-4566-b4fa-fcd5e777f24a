{"env": {"es6": true, "node": true}, "globals": {"cache": true, "EdgeKV": true, "TransformStream": true, "HTMLStream": true, "FetchEvent": "readonly", "process": true}, "rules": {"semi": "off", "react/react-in-jsx-scope": "off", "react/no-unknown-property": "off", "space-before-function-paren": "off", "no-unused-expressions": "off"}, "root": true, "parser": "babel-es<PERSON>", "parserOptions": {"ecmaVersion": 9, "sourceType": "module"}, "extends": ["@ali/eslint-config-fliggy", "plugin:jsx-plus/recommended"], "plugins": ["jsx-plus"]}