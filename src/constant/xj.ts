// ssr源站域名
export const SSR_ORIGIN_HOST = 'phecda-ssr.m.fliggy.com';
// 预发ssr源站域名
export const SSR_PRE_ORIGIN_HOST = 'phecda-ssr.wapa.fliggy.com';
// CSR CDN fliggy域名
export const CSR_FLIGGY_HOST = 'f.m.fliggy.com';
// CSR CDN fliggy预发域名
export const CSR_FLIGGY_PRE_HOST = 'f.wapa.fliggy.com';
// CSR CDN taobao域名
export const CSR_TAOBAO_HOST = 'outfliggys.m.taobao.com';
// CSR CDN taobao预发域名
export const CSR_TAOBAO_PRE_HOST = 'outfliggys.wapa.taobao.com';
// CSR源站域名
export const CSR_ORIGIN_HOST = 'phecda.m.fliggy.com';
// CSR预发源站域名
export const CSR_PRE_ORIGIN_HOST = 'phecda.wapa.fliggy.com';
// 兜底重定向地址
export const DEFAULT_REDIRECT_URL = 'https://market.m.taobao.com/app/trip/rx-home/pages/home';

//璇玑路由匹配
export const XUANJI_PATH_REG = /^\/(xj|xj-csr)\/(app|page)\/[^/]+\/[^/]+\/[^/]+\/[^/]+$/;
// js map 匹配
export const JS_MAP_REG = /https?:\/\/[^\s]*\.js\.map$/;
//璇玑路径分割
export const XUANJI_PATH_SPLIT_REG = /\/xj\/page\/([^\/]+)\/([^\/]+)\/([^\/]+)\/([^\/?]+)/;

// CDN缓存时长，3分钟，180s
export const CDN_CACHE_AGE = 180;
// 设置客户端缓存时长，1分钟，60s
export const CLIENT_CACHE_AGE = 60;
// CDN获取超时时间 50ms
export const CACHE_TIMEOUT = 50;
// SSR 缓存时长，2分钟，120s
export const SSR_CACHE_AGE = 120;

//CDN缓存
export const CDN_CACHE_HOST = 'https://xjcdn.fliggy.com/release/cache-page';
export const PRE_CDN_CACHE_HOST = 'https://xjcdn.fliggy.com/prepub/cache-page';
export const CDN_CACHE_PATH_REG = /^\/[^\/]+(\/.*)$/;
export const CDN_KV_CONFIG_KEY = 'cdn-cache-config';

//URC
export const URC_KV_CONFIG_KEY = 'urc-config';

//统一路由
export const ROUTER_KV_CONFIG_KEY = 'router-config';
export const PRE_CROWD_URL = 'https://phecda.wapa.fliggy.com/crowdMatch';
export const CROWD_URL = 'https://phecda.m.fliggy.com/crowdMatch';