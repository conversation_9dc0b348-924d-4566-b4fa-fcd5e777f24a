// 降级线上域名
export const RAX_ORIGIN_HOST = 'market.m.taobao.com';
// 降级预发域名
export const PRE_RAX_ORIGIN_HOST = 'market.wapa.taobao.com';
// 源站线上域名
export const RAX_SSR_HOST = 'fliggyrax.taobao.com';
// 源站预发域名
export const PRE_RAX_SSR_HOST = 'pre-fliggyrax.wapa.taobao.com';

// 新-源站线上域名
export const NEW_RAX_SSR_HOST = 'raxssr.m.fliggy.com';
// 新-源站预发域名
export const NEW_PRE_RAX_SSR_HOST = 'pre-raxssr.wapa.fliggy.com';

// TODO:VITE临时使用赠送域名

// V: vite的源站-线上域名
export const VITE_SSR_HOST = 'vite-ssr.fn.alibaba-inc.com'
// V: vite的源站-预发域名
export const VITE_PRE_SSR_HOST = 'vite-ssr.pre-fn.alibaba-inc.com'

// 微信域名
export const WX_HOST = [
  'proxy-er.feizhu.com',
  'proxy-er.fzwxxcx.com',
  'proxy-er.fzwxxcx.cn',
  'pre-proxy-er.feizhu.com',
  'pre-proxy-er.fzwxxcx.com',
  'pre-proxy-er.fzwxxcx.cn'
]

// 火车票高德域名
export const GD_HOST = [
  'front-traffic-fliggy-er.amap.com',
  'pre-front-traffic-fliggy-er.amap.com'
]

// V: vite也走此名单，路径前加/vite/
// 静态SSR页面白名单
export const RAX_CACHE_PAGE_LIST = {
  // "/app/trip/rx-trip-ticket/pages/detail": {
  //   key: ['poiId'],
  //   cacheDuration: 14400,
  //   requiredKey: 'poiId',
  //   kvName: 'static-config'
  // }
  "/app/trip/h5-lazada-pc-fliggy-home-ssr/pages/home/<USER>": {
    key: [],
    cacheDuration: 14400,
    kvName: 'static-config'
  },
  // "/app/trip/rx-channels-2023/pages/main": {
  //   key: [],
  //   cacheDuration: 14400,
  //   kvName: 'static-config',
  //   requiredKey: 'sb_redirect_auto',
  //   excludeKey: []
  // },
  "/app/trip/rx-channels-2023/pages/around": {
    key: [],
    cacheDuration: 1440,
    kvName: 'static-config',
    requiredKey: 'sb_redirect_auto',
    excludeKey: []
  },
  "/app/trip/rx-channels-2023/pages/domestic": {
    key: [],
    cacheDuration: 1440,
    kvName: 'static-config',
    requiredKey: 'sb_redirect_auto',
    excludeKey: []
  },
  "/app/trip/rx-channels-2023/pages/abroad": {
    key: [],
    cacheDuration: 1440,
    kvName: 'static-config',
    requiredKey: 'sb_redirect_auto',
    excludeKey: []
  }
};

// CDN缓存相关
export const CDN_PAGE_HOST = 'https://cdnoss.fliggy.com/pages/';
export const PRE_CDN_PAGE_HOST = 'https://cdnoss.fliggy.com/pre/pages/';

// CDN缓存页面配置
export const CDN_CACHE_PAGE = {
  // "/app/trip/rx-dest-2024/pages/detail": {
  //   cdnKey: ['destId','from']
  // }
}

// CDN缓存页面配置
export const PRE_CDN_CACHE_PAGE = {
  // "/app/trip/rx-dest-2024/pages/detail": {
  //   cdnKey: ['destId','from']
  // }
}

// 边缘预加载黑名单
export const RAX_CACHE_BLACK_LIST = {
  "/app/trip/rx-miniapp-home/pages/home": true
};


// V: 沿用此名单，但是需要注意Vite相关的路径前面需要加/vite/app/xxx
// 预加载参数白名单
export const RAX_CACHE_PATH_QUERY_WHITE = {
  "/app/trip/rx-search-all/pages/home": ["disableNav"],
  "/app/trip/rx-vehicle-order-detail/pages/detail": ["orderId"],
  "/app/trip/rx-travel-order-detail/pages/detail": ["orderId"],
  "/app/trip/rx-hotel-order-detail/pages/index": ["orderId"],
  "/app/trip/rx-train-main/pages/order-detail": ["orderId"],
  "/app/trip/rx-vehicle-order-detail/pages/home": ["orderId"],
  "/app/trip/rx-pocket/pages/index": ["_fz_from_wv","currentSelectTab"],
  "/app/trip/rx-mini-my-home/pages/home": ["_fz_from_wv","disableNav"],
  "/app/trip/rx-mileage-center/pages/home": ["disableNav"],
  "/app/trip/rx-mileage2024/pages/home": ["disableNav"],
  "/app/trip/rx-journey-ssr/pages/home": ["_fz_from_wv","disableNav"],
  "/app/trip/rx-coupon-package/pages/list": ["disableNav"],
  "/app/trip/rx-search-all/pages/list": ["keyword","callSource","nav","conditions","latitude","longitude","callTrace"]
}

// seo页面列表
export const SEO_PATH_LIST = [
  '/app/trip/h5-lazada-weather-seo/pages/home/<USER>',
  '/app/trip/h5-lazada-weather-seo/pages/detail/index.html',
  '/app/trip/h5-lazada-weather-seo/pages/notfound/index.html',
  '/app/trip/rx-weather-seo-mobile/pages/home',
  '/app/trip/rx-weather-seo-mobile/pages/detail',
  '/app/trip/rx-weather-seo-mobile/pages/notfound',
  '/app/trip/rx-weather-seo-mobile/pages/sitemap',
  '/app/trip/h5-lazada-pc-fliggy-home-ssr/pages/home/<USER>',
  '/app/trip/h5-lazada-pc-fliggy-home-ssr/pages/sitemap/index.html',
  '/app/trip/h5-lazada-pc-fliggy-home-ssr/pages/test/index.html',
  '/app/trip/rx-content-seo/pages/detail',
];

// 手淘PHA白名单
export const TAOBAO_PHA_WHITE_LIST = [
  "2215073891875",
  "2213195372323",
  "2212505824856",
  "2212616903381"
]



// 流式相关参数
export const HOLDER_START_REG = /\<div\s+id=['"]ssr-er-holder['"]/g;
export const ROOT_BEFORE_START_REG = /\<div\s+id=['"]root_before['"]/g;
export const ROOT_START_REG = /\<div\s+id=['"]root['"]/g;
export const SECOND_STREAM_END = /\<div\s+id=['"]__SECOND_STREAM_END__['"].*?>\<\/div\>/g
export const SERVER_START_REG = /\<script\s+data-from=['"]server['"]/g;

// 流式内置配置
export const STREAM_CONFIG = {
  "rx-train-main_train-detail": true
  // "rx-train-main_train-detail": {"cacheKey":[],"template":"%3Cdiv%20id%3D%22ssr-er-holder%22%3E%3Cdiv%20class%3D%22ssr-skelenton-background%22%3E%3Cdiv%20class%3D%22ssr-skelenton-content%22%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card%22%3E%3Cdiv%20class%3D%22ssr-skelenton-content-row%22%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-content-col%22%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-mid%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-line%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-right%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-content-row%20ssr-skelenton-train-card-next%22%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-next-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-mid%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-next-right%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-train-card-details%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seats%20ssr-skelenton-content-row%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-item%20ssr-skelenton-content-col%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-head%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-desc%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-item%20ssr-skelenton-content-col%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-head%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-desc%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-item%20ssr-skelenton-content-col%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-head%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-seat-desc%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-item%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-col%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-mid%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-right%22%3E%E8%AE%A2%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-item%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-col%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-mid%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-right%22%3E%E8%AE%A2%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-item%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-col%22%3E%3Cdiv%20class%3D%22ssr-skelenton-product-left%22%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-mid%22%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3Cdiv%20class%3D%22ssr-skelenton-product-right%22%3E%E8%AE%A2%3C%2Fdiv%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3C%2Fdiv%3E%3C%2Fdiv%3E","version":"2.31.1", "updateTime": 1718611496000}
}

// 强行命中流式(删除=不命中，2=二段，3=三段)
export const STREAM_FORCE_MAP = {
  "/app/trip/rx-train-main/pages/order-detail": 2,
  // "/app/trip/rx-hotel-listing/pages/home": 3,
  "/app/trip/rx-hotel-detail/pages/detail": 3
}

// 强制关闭流式
export const STREAM_FORCE_CLOSE_ARR = []

// 流式返回二屏内容
export const STREAM_PAGE_LIST = [
  '/app/trip/rx-miniapp-home/pages/home',
  '/app/trip/rx-journey-ssr/pages/home',
  '/app/trip/rx-journey-new/pages/home',
];

// ssr本地开发时的dev页面路径
export const SSR_LOCAL_PAGE = '/app/trip/ssr-page/pages/home';
