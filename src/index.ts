import { reduceHeaders, addGzipHeader, simpleParseUrl, isWechat } from './utils';
import { SlsLog, checkIsPre } from './utils/rax';
import { WX_HOST, RAX_CACHE_BLACK_LIST } from './constant/rax';
import { MINI_WEBVIEW_AUTH_MAP } from './constant/miniprogram';
import handleXjRequest from './handler/xj';
import handleRaxRequest from './handler/rax';
import handleViteRequest from './handler/vite';
import handleSidRequest from './handler/sid';
import handleCsrRequest from './handler/csr';
import { handleTbRequest, handleTbRouterRequest } from './handler/tb';
import handleDetailRequest from './handler/detail';
import handleManifestRequest from './handler/manifest';
import handleSwRequest from './handler/sw';
import handlePrefetchRequest from './handler/prefetch';
import handleTbMiniappHomeRequest from './handler/tbMiniappHome';
import { handleRequest as handleCommonRequest } from './common';
import { streamRenderConfig as vacationStreamConfig } from './handler/detail/const';
import { handleMiniProgramRequest } from './handler/miniprogram';

/**
 * Add the necessary event listener
 * @param {Event} fetch event, {Function} async function
 */
addEventListener('fetch', (event: FetchEvent) => {
  event.respondWith(handleRequest(event));
});

async function handleRequest (event: FetchEvent) {
  const { request } = event;
  const startTime = Date.now();
  // 删减一下多余的headers，防止超过8K导致fetch报错
  reduceHeaders(request.headers);
  // 打入口日志

  let reqUrl = null;
  try {
    reqUrl = new URL(request.url);
  } catch (err) {
    // reqUrl = new URL('https://outfliggys.m.taobao.com/app/trip/rx-er-error-page/pages/home');
    // 链接超长g了 https://aliyuque.antfin.com/trip_plat/pu6xpg/tds6kbq3n4i7y22y
    // 非线上环境
    const isPre = checkIsPre(request.url, false);

    if (isPre) {
      return err.message;
    }

    let moreMsg = ''
    try {
      // 商详开始定制，给手淘跪一个
      const safeSimpleURL = simpleParseUrl(request.url);
      if (vacationStreamConfig[safeSimpleURL.pathname] || safeSimpleURL.query._use_vstream) {
        return await handleDetailRequest({ event, startTime });
      }
    } catch (err2) {
      // 万一g了呢
      moreMsg = 'detailfail';
    }
    // 随机裁剪...
    // 缩短链接再来一次orz
    const nextUrl = request.url.length > 3000 ? request.url.slice(0, 3000) : request.url.slice(0, Math.floor(request.url.length * 0.75));
    return Response.redirect(`${nextUrl}&_er_error=urltoolong${moreMsg ? `_${moreMsg}` : ''}`);
  }

  try {
    /**
       * 新版本chrome（>= 122.0.6261.69），Accept-Encoding 会增加 zstd 压缩，而 ER 目前不支持 zstd 会导致解压失败。
       * https://chromestatus.com/feature/6186023867908096
       *
       * 解决办法是：先重写 Accept-Encoding，去掉zstd压缩，等630后 ER 会兼容，再去掉这段逻辑。
       * */
    addGzipHeader(request.headers);

    // 微信套壳域名新增headers标识
    // if (WX_HOST.includes(reqUrl.hostname)) {
    //   request.headers.set('from-wx-er', 'true');
    // } else if (request.headers.get('wx-proxy-host')) {
    //   // request.headers.set('from-er', 'true');
    // }
    let oriUrl = request.url;
    if (request.headers.get('wx-proxy-host')) {
      oriUrl = oriUrl.replace(reqUrl.hostname, request.headers.get('wx-proxy-host'));
    }
    // 安全拦截需要，出滑块后重定向到哪，追加客户端的禁止预加载参数
    request.headers.set('originurl', `${oriUrl}${oriUrl.indexOf('?') !== -1 ? '&' : '?'}_fli_no_cache=true`);

    // 对referer的参数进行裁剪，只保留path部分，删掉后面的query参数，减少header过长带来的影响
    const referer = request.headers.get('referer');
    if (referer) {
      request.headers.set('referer', referer.slice(0, referer.indexOf("?")))
    }
  }catch (err) {
    if (request.url.indexOf('_debug_er') > -1) {
      return err.message;
    }
  }

  const verifyText = MINI_WEBVIEW_AUTH_MAP[reqUrl.pathname];
  const isXj = reqUrl.pathname.startsWith('/xj/') || reqUrl.pathname.startsWith('/xj-csr/');

  const isVacationStream = vacationStreamConfig[reqUrl.pathname] || request.url.indexOf('_use_vstream=')>-1;
  const isPre = checkIsPre(reqUrl, true);
  // 度假流式逻辑线上走自定义，预发切分支等任务走通用的
  const vactionStreamOnline = !isPre && isVacationStream;

  // 小程序链路（预请求、商详、搭建页走其他逻辑）
  if (!verifyText && !vactionStreamOnline && !isXj && WX_HOST.includes(reqUrl.hostname) && !reqUrl.searchParams.has('_pressr')) {
    return await handleMiniProgramRequest(event);
  }

  if (isXj) {
    // 璇玑搭建链路
    return await handleXjRequest(event);
  } else if (reqUrl.pathname.startsWith('/app/') && reqUrl.pathname.includes('fmanifest.json')) {
    // 源码manifest链路
    return await handleManifestRequest(event);
  } else if (reqUrl.pathname.startsWith('/app/') && reqUrl.pathname.includes('sw.js')) {
    // 源码service worker链路
    return await handleSwRequest(event);
  } else if (isVacationStream) {
    // 宝贝详情逻辑
    return await handleDetailRequest({ event, startTime });
  } else if (reqUrl.searchParams.has('_new_stream') &&
    (reqUrl.pathname === '/app/trip/rx-miniapp-home/pages/home' || reqUrl.pathname === '/app/trip/rx-miniapp-home-overseas/pages/home')
  ) {
    // 淘端首页
    return await handleTbMiniappHomeRequest(event);
  } else if (reqUrl.pathname.startsWith('/app/')) {
    // 源码新链路
    const { searchParams } = reqUrl;
    if (searchParams.has('_pressr')) {
      if (RAX_CACHE_BLACK_LIST[reqUrl.pathname]) {
        return 'console.log("pressr closed")';
      } else if (!searchParams.has('_sw_prefetch')) {
        event.waitUntil(handleRaxRequest(event));
        return 'console.log("pressr success")';
      }
    }
    return await handleRaxRequest(event);
  } else if (reqUrl.pathname.startsWith('/prefetch/')) {
    // 源码批量预加载链路
    event.waitUntil(handlePrefetchRequest(event));
    return 'console.log("pressr success")';
  } else if (reqUrl.pathname.startsWith('/updateSid/')) {
    // sid更新专用 by @紫期
    return await handleSidRequest(event);
  } else if (reqUrl.pathname.startsWith('/csr/')) {
    // ssr同域名降级链路
    return await handleCsrRequest(event);
  } else if (reqUrl.pathname.startsWith('/tb/')) {
    // 淘宝相关
    return await handleTbRouterRequest(event);
  } else if (reqUrl.pathname.startsWith('/tb_miniapp_link')) {
    // 淘宝流量海关
    return await handleTbRequest(event);
  } else if (verifyText) {
    // 验证文件
    return verifyText;
  } else if (reqUrl.pathname.startsWith('/vite/')) {
    return await handleViteRequest(event);
  }
  // 老链路
  return await handleCommonRequest(event);
}
