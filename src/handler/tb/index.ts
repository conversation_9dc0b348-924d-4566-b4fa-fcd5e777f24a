import { getHost } from '../../utils/rax';

// 流量海关uniapp页
const MINIAPP_LINK = 'https://market.m.taobao.com/app/trip/rx-tb-uniapp-router/pages/mini-link?uniapp_id=1011089&uniapp_page=miniLink';
// 流量海关纯H5页
const MINIAPP_LINK_H5 = 'https://market.m.taobao.com/app/trip/rx-tb-uniapp-router/pages/mini-link';
// uniapp套壳页
const UNIAPP_TAOKE_URL = 'https://market.m.taobao.com/app/trip/rx-tb-uniapp-router/pages/home?uniapp_id=1011089&uniapp_page=router&openType=replace';
// 淘端tab页uniapp地址
const TAB_PAGE_URL_MAP = {
  home: 'https://outfliggys.m.taobao.com/app/trip/rx-miniapp-home/pages/home?disableNav=YES&titleBarHidden=2&_use_stream=1&showUniAppTab=true&uniapp_id=1011089&uniapp_page=home&ttid=12mtb0000155',
  lottery: 'https://outfliggys.m.taobao.com/app/trip/rx-mileage2024/pages/home?disableNav=YES&titleBarHidden=2&fpt=fak(6579080)&showUniAppTab=true&_fz_cli_cache_key=lottery2024&uniapp_id=1011089&uniapp_page=lottery',
  journey: 'https://outfliggys.m.taobao.com/app/trip/rx-journey-ssr/pages/home?disableNav=YES&titleBarHidden=2&_use_stream=1&_fli_online=true&showUniAppTab=true&fpt=fak(6579080)&uniapp_id=1011089&uniapp_page=journey',
  pocket: 'https://outfliggys.m.taobao.com/app/trip/rx-pocket/pages/index?disableNav=YES&titleBarHidden=2&currentSelectTab=collect&fpt=fak(6579080)&showUniAppTab=true&page_env=taobao_tab&uniapp_id=1011089&uniapp_page=pocket',
  user: 'https://outfliggys.m.taobao.com/app/trip/rx-mini-my-home/pages/home?disableNav=YES&titleBarHidden=2&showUniAppTab=true&fpt=fak(6579080)&uniapp_id=1011089&uniapp_page=user',
  item_detail: 'https://outfliggys.m.taobao.com/app/trip/rx-travel-detail/pages/index?uniapp_id=1011089&uniapp_page=item_detail',
};
// 淘端tab页纯H5地址
const TAB_H5_URL_MAP = {
  home: 'https://outfliggys.m.taobao.com/app/trip/rx-miniapp-home/pages/home?disableNav=YES&titleBarHidden=2&_use_stream=1&showUniAppTab=true&ttid=12mtb0000155&isdowngrade=true',
  lottery: 'https://outfliggys.m.taobao.com/app/trip/rx-mileage2024/pages/home?disableNav=YES&titleBarHidden=2&fpt=fak(6579080)&showUniAppTab=true&_fz_cli_cache_key=lottery2024&isdowngrade=true',
  journey: 'https://outfliggys.m.taobao.com/app/trip/rx-journey-ssr/pages/home?disableNav=YES&titleBarHidden=2&_use_stream=1&_fli_online=true&showUniAppTab=true&fpt=fak(6579080)&_fz_cli_cache_key=journey&isdowngrade=true',
  pocket: 'https://outfliggys.m.taobao.com/app/trip/rx-pocket/pages/index?disableNav=YES&titleBarHidden=2&currentSelectTab=collect&fpt=fak(6579080)&showUniAppTab=true&page_env=taobao_tab&isdowngrade=true',
  user: 'https://outfliggys.m.taobao.com/app/trip/rx-mini-my-home/pages/home?disableNav=YES&titleBarHidden=2&showUniAppTab=true&fpt=fak(6579080)&_fz_cli_cache_key=user&isdowngrade=true',
  item_detail: 'https://outfliggys.m.taobao.com/app/trip/rx-travel-detail/pages/index',
};

export async function handleTbRequest(event: FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const { searchParams } = reqUrl;
    const searchString = '?' + searchParams.toString();
    const ssrOrigin = new URL(`https://${getHost(reqUrl)}`);
    ssrOrigin.pathname = reqUrl.pathname;
    ssrOrigin.search = searchString;
    // 路径映射
    const reqPath = ssrOrigin.toString();
  
    return fetch(reqPath, {
      headers: request.headers,
      decompress: 'manual'
    });
  } catch (e) {
    throw new Error(e.message);
  }
}

export async function handleTbRouterRequest(event: FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const { pathname, searchParams } = reqUrl;
    searchParams.delete('_ariver_appid');
    const pathKey = (pathname.split('/') || [])[2] || '';

    // 纯H5降级页
    if (pathKey === 'downgrade') {
      const phaActivePageKey = searchParams.get('pha_active_page_key');
      const phaJumpUrl = searchParams.get('pha_jump_url');
      const phaQuery = searchParams.get('query');
      const flowCustomChannel = searchParams.get('flowCustomChannel');

      let redirectUrl = '';
      // 流量海关页
      if (flowCustomChannel && phaQuery) {
        redirectUrl = `${MINIAPP_LINK}&${searchParams.toString()}`;  
      } else if (phaActivePageKey) {
        // 底部 tab 页
        const tabUrl = TAB_PAGE_URL_MAP[phaActivePageKey];
        redirectUrl = `${tabUrl}&${searchParams.toString()}`;
      } else if (phaJumpUrl) {
        // 套壳页
        redirectUrl = `${UNIAPP_TAOKE_URL}&${searchParams.toString()}`;
      } else {
        // 首页
        redirectUrl = `${TAB_PAGE_URL_MAP.home}&${searchParams.toString()}`;
      }
  
      return Response.redirect(redirectUrl);
    } else if (pathKey === 'uniappdowngrade') {
      // uniapp降级页
      const phaActivePageKey = searchParams.get('pha_active_page_key');
      const phaJumpUrl = searchParams.get('pha_jump_url');
      const phaQuery = searchParams.get('query');
      const flowCustomChannel = searchParams.get('flowCustomChannel');

      let redirectUrl = '';
      // 流量海关页
      if (flowCustomChannel && phaQuery) {
        redirectUrl = `${MINIAPP_LINK_H5}?${searchParams.toString()}`;  
      } else if (phaActivePageKey) {
        // 底部 tab 页
        const tabUrl = TAB_H5_URL_MAP[phaActivePageKey];
        redirectUrl = `${tabUrl}${tabUrl.indexOf('?') > -1 ? '&' : '?'}${searchParams.toString()}`;
      } else if (phaJumpUrl) {
        // 套壳页
        const targetUrl = decodeURIComponent(decodeURIComponent(phaJumpUrl));
        searchParams.delete('pha_jump_url');
        redirectUrl = `${targetUrl}${targetUrl.indexOf('?') > -1 ? '&' : '?'}${searchParams.toString()}`;
      } else {
        // 首页
        redirectUrl = `${TAB_H5_URL_MAP.home}&${searchParams.toString()}`;
      }
  
      return Response.redirect(redirectUrl);
    } else if (pathKey === 'jump') {
      // 只给套壳页
      const phaJumpUrl = searchParams.get('pha_jump_url') || searchParams.get('jump_url');
      const targetUrl = decodeURIComponent(decodeURIComponent(phaJumpUrl));
      searchParams.delete('pha_jump_url');
      searchParams.delete('jump_url');

      const redirectUrl = `${targetUrl}${targetUrl.indexOf('?') > -1 ? '&' : '?'}${searchParams.toString()}`;
      
      return Response.redirect(redirectUrl);
    }
    
    return Response.redirect(TAB_PAGE_URL_MAP.home);
  } catch {
    return Response.redirect(TAB_PAGE_URL_MAP.home);
  }
}