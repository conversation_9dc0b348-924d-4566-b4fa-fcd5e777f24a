import { checkIsPre } from "../../utils/rax";

export default async function handleManifestRequest(event: FetchEvent) {
  const { request } = event;
  const reqUrl = new URL(request.url);
  const isPre = checkIsPre(reqUrl);
  const manifestUrl = `https://market.${isPre ? 'wapa' : 'm'}.taobao.com${reqUrl.pathname}`;
  const manifestRes = await fetch(manifestUrl, 
    {
      headers: request.headers,
      cdnProxy: true,
      decompress: 'manual'
    }
  ); 
  return manifestRes;
}