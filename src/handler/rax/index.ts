import { SlsLog, checkRaxRedirectUrl } from '../../utils/rax';
import { processTBSeachParams } from '../../utils';
import searchHandle from '../../processer/rax/searchHandle';
import pathCheckProcesser from '../../processer/rax/pathCheck';
import { checkForceCache } from '../../processer/rax/forceCache';
import { checkStaticCache } from '../../processer/rax/staticCache';
import { checkEdgeCache } from '../../processer/rax/edgeCache';
import { checkCdnCache } from '../../processer/rax/cdnCache';
import mainReqProcesser from '../../processer/rax/mainReq';
import downGradeProcesser from '../../processer/rax/downGrade';
import deleteCache from '../../processer/rax/deleteCache';

export default async function handleRaxRequest(event: FetchEvent) {
  try {
    // 提前清空内存的timelog数据
    SlsLog.clearTimeLog();
    // 入口打点开始时间
    SlsLog.setTimeLog({
      timeStamp: {
        er_start: Date.now()
      }
    });

    const { request } = event;
    const reqUrl = new URL(request.url);

    // 打入口日志
    SlsLog.sendLog({
      event,
      reqUrl,
      logConfig: {
        logData: {
          timeStamp: { 
            er_entry: true,
            er_start: Date.now(),
            erRequestUrl: request.url,
          }
        }
      }
    })

    // 测试链路
    const searchHandleRes = await searchHandle(request);
    if (searchHandleRes) {
      return searchHandleRes;
    }

    // 获取userid 
    const headerCookie = request.headers.get('cookie') || '';
    const userIdReg = headerCookie.match(/unb=(\d+);/);
    const cookieUserId = userIdReg && userIdReg.length > 1 ? userIdReg[1] : '';
    SlsLog.setTimeLog({
      url: reqUrl.toString(),
      userId: cookieUserId || ''
    });
    // 安全账号扫描降级
    if (cookieUserId === '*************') {
      throw new Error('test account downgrade');
    }

    // 处理手淘首猜链接带的utparam
    reqUrl.search = processTBSeachParams(reqUrl.search);

    // 路径规则检测
    await pathCheckProcesser(event);

    if (reqUrl.search.includes('_deletePre')) {
      return await deleteCache(event);
    }

    // 链接中携带参数_fli_cli_pre_key点击预加载，跳过缓存逻辑
    // if (reqUrl.search.includes('_fz_cli_cache_key')) {
    //   return await mainReqProcesser(event);
    // }

    // 协商缓存检测
    const forceCacheRes = await checkForceCache(event);
    if (forceCacheRes) {
      return forceCacheRes;
    }

    // 静态SSR逻辑
    const staticCacheRes = await checkStaticCache(event);
    if (staticCacheRes) {
      return staticCacheRes;
    }

    // CDN缓存逻辑
    const cdnCacheRes = await checkCdnCache(event);
    if (cdnCacheRes) {
      return cdnCacheRes;
    }

    // 边缘预加载读逻辑
    const edgeCacheRes = await checkEdgeCache(event);
    if (edgeCacheRes) {
      return edgeCacheRes;
    }

    // 回源实时链路
    return await mainReqProcesser(event);

  } catch (e) {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const isDebug = reqUrl.searchParams.get('_debug_er');
    if (isDebug) {
      return e.message + '\n' + e.stack;
    }
    return await downGradeProcesser(event, e);
  }
}