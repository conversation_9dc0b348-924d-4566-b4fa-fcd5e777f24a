import { DEFAULT_REDIRECT_URL } from '../../constant/xj';
import { handleTBSeach, handleFillter, handleManifest, handlePreview, getContext, handleRouter } from '../../processer/xj';
import { handleCSR } from './csr';
import { handleSSR } from './ssr';
import { logger } from '../../utils/xj';

/**
 * 璇玑搭建链路
*/
export default async function handleXjRequest(event: FetchEvent) {
  const { request } = event;
  let reqUrl = new URL(request.url); //构造请求实例
  const context = getContext({ reqUrl, request }); //构造上下文

  try {
    //处理统一路由
    const routerRes = await handleRouter({ reqUrl, context, request, event });
    if (routerRes) return routerRes;
    //处理URC 
    const manifestRes = await handleManifest({ reqUrl, context });
    if (manifestRes) return manifestRes;
    //处理预览请求
    const previewRes = await handlePreview({ context, request });
    if (previewRes) return previewRes;
    //路由过滤
    const filterRes = await handleFillter({ reqUrl, context });
    if (filterRes) return filterRes;
    //处理淘宝首猜参数
    handleTBSeach({ reqUrl, request });
    //CSR链路
    if (context.isCSR) return await handleCSR(event, context);
    //SSR链路
    return await handleSSR(event, context);
  } catch (e) {
    await logger.record({
      logKey: 'catchError',
      logName: '整体捕获错误',
      content: { tracker: e.message },
      context
    });
    return Response.redirect(DEFAULT_REDIRECT_URL);
  }
}
