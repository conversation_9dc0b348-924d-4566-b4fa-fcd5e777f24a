import { checkEdgeCache, addEdgeCache, deleteEdgeCache } from '../../processer/xj/edgeCache';
import { checkCdnCache } from '../../processer/xj/cdnCache';
import { removeHtmlCacheHeaders, getRedirectUrl } from '../../utils';
import { logger } from '../../utils/xj';
import { loadMiniprogramDependencies, handleMiniProgramResponse } from '../miniprogram';
import { SSR_ORIGIN_HOST, SSR_PRE_ORIGIN_HOST, CSR_FLIGGY_HOST, CSR_FLIGGY_PRE_HOST, CSR_TAOBAO_HOST, CSR_TAOBAO_PRE_HOST } from '../../constant/xj';

/**
 * SSR链路
*/
export const handleSSR = async (event, context) => {
  const { reqUrl, isPre, isTaobao } = context;

  //回源链接
  let csrURL;
  if (isTaobao) {
    csrURL = new URL(`https://${isPre ? CSR_TAOBAO_PRE_HOST : CSR_TAOBAO_HOST}`);
    csrURL.pathname = reqUrl.pathname.replace(/^\/xj\//, '/xj-csr/');
    csrURL.search = reqUrl.search;
  } else {
    csrURL = new URL(`https://${isPre ? CSR_FLIGGY_PRE_HOST : CSR_FLIGGY_HOST}`);
    csrURL.pathname = reqUrl.pathname;
    csrURL.search = reqUrl.search;
  }

  /**
   * url裁剪
   * doc：https://aliyuque.antfin.com/remtwr/mabeu7/plweo3z8offkr5dq
  */
  if (csrURL?.search?.includes('s_share_url')) {
    csrURL?.searchParams?.delete('s_share_url');
  }

  try {
    //CDN缓存逻辑
    const cdnCacheRes = await checkCdnCache({ context, event });
    if (cdnCacheRes) return cdnCacheRes;

    //边缘预加载逻辑
    const edgeCacheRes = await checkEdgeCache({ context, event });
    if (edgeCacheRes) return edgeCacheRes;

    //回源逻辑
    return await originProcesser({ event, context, csrURL });
  } catch (e) {
    await logger.record({
      logKey: 'SSRCatchError',
      logName: 'SSR链路捕获错误',
      content: { tracker: e.message },
      context
    });
    return Response.redirect(csrURL.toString());
  }
}


/**
 * 回源逻辑
*/
const originProcesser = async ({ event, context, csrURL }) => {
  const { request } = event;
  const { reqUrl, isPre } = context;
  const { pathname, search } = reqUrl;
  const beginTime = new Date().getTime();

  //构造源站链接
  const originUrl = new URL(`https://${isPre ? SSR_PRE_ORIGIN_HOST : SSR_ORIGIN_HOST}`);
  originUrl.pathname = pathname;
  originUrl.search = search;

  //回源任务
  let [originRes, wechatRes] = await Promise.all([
    fetch(originUrl.toString(), { headers: request.headers }),
    loadMiniprogramDependencies(event, beginTime)
  ]);

  //微信ER直连
  const wxScript = wechatRes?.htmlString;
  if (wxScript) {
    // let htmlText = await originRes.clone().text();
    // htmlText = htmlText.replace('</head>', `${wxScript}</head>`);
    // originRes = new Response(htmlText, {
    //   headers: originRes.headers,
    //   status: originRes.status
    // });
    return await handleMiniProgramResponse(event, originRes, wechatRes, wechatRes.sls);
  }

  //清除缓存
  if (search.includes('phecda_delete_cache')) {
    await deleteEdgeCache({ event, context }); //清除边缘预加载缓存
  }

  //调试模式强制回源
  if (search.includes('phecda_') || isPre) {
    return originRes;
  }

  //命中缓存立即返回
  if (originRes.headers.get('x-er-hit-cache-interval')) {
    return originRes;
  }

  //处理重定向
  const redirectUrl = getRedirectUrl(originRes);
  if (redirectUrl) return Response.redirect(redirectUrl);

  //回源失败
  if (!originRes.ok) return Response.redirect(csrURL.toString());

  //删掉源站缓存控制头
  removeHtmlCacheHeaders(originRes.headers);

  //写入缓存
  addEdgeCache({ originRes, context, event });

  originRes.headers.set('x-er-hit-cache', 'false');
  return originRes;
}