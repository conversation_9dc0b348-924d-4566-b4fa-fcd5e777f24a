import { removeErCacheHeaders, removeHtmlCacheHeaders, getRedirectUrl } from '../../utils';
import { logger, getCacheUrl } from '../../utils/xj';
import { CSR_FLIGGY_HOST, CSR_ORIGIN_HOST, CSR_PRE_ORIGIN_HOST, CACHE_TIMEOUT, DEFAULT_REDIRECT_URL, CDN_CACHE_AGE, CLIENT_CACHE_AGE } from '../../constant/xj';
import { loadMiniprogramDependencies, handleMiniProgramResponse } from '../miniprogram';

/**
 * CSR链路
*/
export const handleCSR = async (event: FetchEvent, context) => {
  const { request } = event;
  const { reqUrl, isPre, terminal } = context;
  const { pathname, searchParams, search } = reqUrl;

  //构造源站链接
  const originUrl = new URL(`https://${isPre ? CSR_PRE_ORIGIN_HOST : CSR_ORIGIN_HOST}`);
  originUrl.pathname = pathname;
  originUrl.search = search;

  const cacheUrl = getCacheUrl(CSR_FLIGGY_HOST, pathname, searchParams, terminal); //缓存key

  try {
    //清除缓存
    if (search.includes('phecda_delete_cache')) {
      await cache.delete(cacheUrl);
    }

    //调试模式强制回源
    if (search.includes('phecda_') || isPre) {
      return await fetchOrigin({ request, originUrl: originUrl.toString(), event });
    }

    //强缓存逻辑
    const cacheRes = await checkCache(cacheUrl);
    if (cacheRes) return cacheRes;

    //回源逻辑
    return await originProcesser({ originUrl, cacheUrl, request, context, event });
  } catch (e) {
    await logger.record({
      logKey: 'csrCatchError',
      logName: 'CSR链路捕获错误',
      context,
      content: { tracker: e.message },
    });
    return Response.redirect(DEFAULT_REDIRECT_URL);
  }
};

/**
 * 读取强缓存
*/
const checkCache = async (cacheUrl: string): Promise<Response | false> => {
  const cachePromise = new Promise<Response>(async (resolve) => {
    const res = await cache.get(cacheUrl);
    resolve(res);
  })
  const timeoutPromise = new Promise<false>((resolve) => {
    setTimeout(() => resolve(false), CACHE_TIMEOUT);
  });
  const cacheRes = await Promise.race([cachePromise, timeoutPromise]);
  return cacheRes;
}

/**
 * 请求回源
 */
const fetchOrigin = async ({ request, originUrl, event }): Promise<Response> => {
  const beginTime = new Date().getTime();

  //回源任务
  let [originRes, wechatRes] = await Promise.all([
    fetch(originUrl, { headers: request.headers }),
    loadMiniprogramDependencies(event, beginTime)
  ]);

  //微信ER直连
  const wxScript = wechatRes?.htmlString;
  if (wxScript) {
    // let htmlText = await originRes.clone().text();
    // htmlText = htmlText.replace('</head>', `${wxScript}</head>`);
    // originRes = new Response(htmlText, {
    //   headers: originRes.headers,
    //   status: originRes.status
    // });
    return await handleMiniProgramResponse(event, originRes, wechatRes, wechatRes.sls);
  }

  return originRes;
}

/**
 * 回源逻辑
*/
const originProcesser = async ({ cacheUrl, request, originUrl, context, event }) => {
  const originRes = await fetchOrigin({ request, originUrl: originUrl.toString(), event });

  //处理重定向
  const redirectUrl = getRedirectUrl(originRes);
  if (redirectUrl) return Response.redirect(redirectUrl);

  //回源失败
  if (!originRes.ok) {
    await logger.record({
      logKey: 'csrOriginError',
      logName: 'CSR回源失败',
      context
    });
    return Response.redirect(DEFAULT_REDIRECT_URL);
  }

  //写入强缓存
  removeHtmlCacheHeaders(originRes.headers);
  originRes.headers.set('cache-control', `s-maxage=${CDN_CACHE_AGE}, max-age=${CLIENT_CACHE_AGE}`);
  const pressrCache = originRes.clone();
  removeErCacheHeaders(pressrCache.headers);
  pressrCache.headers.set('x-cache-time', `${Date.now()}`);
  await cache.put(cacheUrl, pressrCache);
  return originRes;
}
