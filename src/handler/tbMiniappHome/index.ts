import searchHandle from '../../processer/rax/searchHandle';
import pathCheckProcesser from '../../processer/rax/pathCheck';
import mainReqProcesser from '../../processer/rax/mainReq';
import downGradeProcesser from '../../processer/rax/downGrade';
import useStreamRender from './processer/stream/useStreamRender';
import { SlsLog } from './utils/slsLog';
import { checkIsPre } from '../../utils/rax/url';
import { processTBSeachParams } from '../../utils';
import { NEW_RAX_SSR_HOST, NEW_PRE_RAX_SSR_HOST } from '../../constant/rax';

export default async function handleTbMiniappHomeRequest(event: FetchEvent) {
  try {
    // 入口打点开始时间
    SlsLog.setTimeLog({
      timeStamp: {
        er_start: Date.now()
      }
    });

    const { request } = event;
    const reqUrl = new URL(request.url);

    // 打入口日志
    SlsLog.sendLog({
      event,
      reqUrl,
      logConfig: {
        logData: {
          timeStamp: { 
            er_entry: true,
            er_start: Date.now(),
            erRequestUrl: request.url,
          }
        }
      }
    })

    // 测试链路
    const searchHandleRes = await searchHandle(request);
    if (searchHandleRes) {
      return searchHandleRes;
    }

    // 获取userid 
    const headerCookie = request.headers.get('cookie') || '';
    const userIdReg = headerCookie.match(/unb=(\d+);/);
    const cookieUserId = userIdReg && userIdReg.length > 1 ? userIdReg[1] : '';
    SlsLog.setTimeLog({
      url: reqUrl.toString(),
      userId: cookieUserId || ''
    });
    // 安全账号扫描降级
    if (cookieUserId === '*************') {
      throw new Error('test account downgrade');
    }

    // 处理手淘首猜链接带的utparam
    reqUrl.search = processTBSeachParams(reqUrl.search);

    // 路径规则检测
    await pathCheckProcesser(event);

    // 流式
    const isStream = reqUrl.searchParams && reqUrl.searchParams.has('_use_stream');
    const isPre = checkIsPre(reqUrl);
    const oriHost = isPre ? NEW_PRE_RAX_SSR_HOST : NEW_RAX_SSR_HOST;
    const ssrOrigin = new URL(`https://${oriHost}`);
    ssrOrigin.pathname = reqUrl.pathname;
    ssrOrigin.search = '?' + reqUrl.searchParams.toString();
    // 请求源站的【minihome】函数的url
    let ssrUrl = ssrOrigin.toString();
    ssrUrl = ssrUrl.replace(ssrOrigin.pathname, `/minihome${reqUrl.pathname}`);
    // csr的url
    ssrOrigin.host = isPre ? 'market.wapa.taobao.com' : 'market.m.taobao.com';
    const raxUrl = ssrOrigin.toString();

    if (isStream) {
      const result = await useStreamRender(event, {
        reqUrl,
        ssrUrl,
        raxUrl,
        request,
        isPre,
        timeLog: SlsLog.getTimeLog(),
      });
      if (result !== false) {
        return result;
      }
    }

    // 非流式
    return await mainReqProcesser(event);
  } catch (e) {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const isDebug = reqUrl.searchParams.get('_debug_er');
    if (isDebug) {
      return e.message + '\n' + e.stack;
    }
    return await downGradeProcesser(event, e);
  }
}