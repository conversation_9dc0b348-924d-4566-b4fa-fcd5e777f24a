const hotelHtml = `<div class="rax-view-v2 home-main-kingkong-search-item home-main-kingkong-search-item-active"><div class="rax-view-v2 skeleton-tims-hotel-search-container"><div class="rax-view-v2 tab-bar"><div class="rax-view-v2 current-tab-wraper-0" style="transform:translateX(0%);"><div class="rax-view-v2 tab-item-bottom-line"></div></div><div class="rax-view-v2"><div class="rax-view-v2 tab-item tab-item-selected">国内</div></div><div class="rax-view-v2"><div class="rax-view-v2 tab-item">国际</div></div><div class="rax-view-v2"><div class="rax-view-v2 tab-item">酒店套餐</div></div><div class="rax-view-v2"><div class="rax-view-v2 tab-item">民宿</div></div></div><div class="rax-view-v2 tims-hotel-search-content"><div class="rax-view-v2 hotelsearch-flex-cell hotelsearch-container-cell"><div class="rax-view-v2 __tracker r_500 hotelsearch-body-search-cell hotelsearch-flex-cell hotelsearch-jiantou hotelsearch-s-24 hotelsearch-height-big hotelsearch-body-location-detail r1bb"><span class="rax-text-v2 hotel-search-location-text">&nbsp;</span></div><div class="rax-view-v2 __tracker undefined"><div class="rax-view-v2 hotelsearch-body-my-location hotelsearch-height-big"><div class="rax-view-v2 hotelsearch-icon-location"></div><span class="rax-text-v2 location-text">我的位置</span></div></div></div><div class="rax-view-v2 date-picker-container"><div class="rax-view-v2 __tracker date-picker-content hotelsearch-jiantou"><div class="rax-view-v2 tims-left"><div class="rax-view-v2 check-date"><div class="rax-view-v2 date-week"><span class="rax-text-v2 date-value text-month">{{currentMonth}}</span><span class="rax-text-v2 date-value date-value-text">月</span><span class="rax-text-v2 date-value text-day">{{currentDay}}</span><span class="rax-text-v2 date-value date-value-text">日</span><span class="rax-text-v2 week-value">今天</span></div></div><div class="rax-view-v2 nights-wrap"><div class="rax-view-v2 line"></div><span class="rax-text-v2 nights">1晚</span></div><div class="rax-view-v2 check-date"><div class="rax-view-v2 date-week"><span class="rax-text-v2 date-value text-month">{{tomorrowMonth}}</span><span class="rax-text-v2 date-value date-value-text">月</span><span class="rax-text-v2 date-value text-day">{{tomorrowDay}}</span><span class="rax-text-v2 date-value date-value-text">日</span><span class="rax-text-v2 week-value">明天</span></div></div></div></div></div><div class="rax-view-v2 keywords-selector hotelsearch-jiantou"><div class="rax-view-v2 __tracker undefined" style="align-items:start;justify-content:flex-end;width:66.6667vw;"><span class="rax-text-v2 keywords-value">酒店/地标/关键词</span></div></div><div class="rax-view-v2 __tracker tims-search-btn-container"><div class="rax-view-v2 tims-search-btn" style="background-color:#ffe003;"><span class="rax-text-v2" style="color:#333;">搜索酒店</span></div><div class="rax-view-v2 tims-hotel-tips"><div class="rax-view-v2 tip-icon"></div></div></div></div></div></div>`;

const flight = `<div class="rax-view-v2 home-main-kingkong-search-item home-main-kingkong-search-item-active"><div class="rax-view-v2 traffic-search-warpper"><div class="rax-view-v2 traffic-search-show"><div class="rax-view-v2 common-search-body flight-search-body undefined-flight-search-body"><div class="rax-view-v2 flightsearch-search-wrapper pray-gray"><div class="rax-view-v2 flightsearch-wrapper"><div class="rax-view-v2 traffic-tabbar-wrapper"><div class="rax-view-v2 traffic-tabbar"><div class="rax-view-v2 tabbar-indicator-wrapper" style="width:25%;transform:translateX(0%);"><div class="rax-view-v2 tabbar-indicator"></div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="0" class="rax-view-v2 traffic-tabbar-item-text traffic-tabbar-item-text-current"><div class="rax-view-v2">单程</div></div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="1" class="rax-view-v2 traffic-tabbar-item-text"><div class="rax-view-v2">往返</div></div><div class="rax-view-v2 traffic-tabbar-item-tag">更优惠</div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="2" class="rax-view-v2 traffic-tabbar-item-text"><div class="rax-view-v2">多程</div></div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="3" class="rax-view-v2 traffic-tabbar-item-text"><div class="rax-view-v2">特价</div></div></div></div></div><div class="rax-view-v2 flight-search-od-exchange-wrap flightsearch-row flightsearch-row-height"><div class="rax-view-v2 __tracker flight-search-od-city"><div class="rax-view-v2 area-name">北京</div></div><div class="rax-view-v2 __tracker flight-toggle-icon"><div class="rax-view-v2 od-exchange-icon-ring"></div><div class="rax-view-v2 fixed-flight-icon"></div></div><div class="rax-view-v2 __tracker flight-search-od-city flight-search-od-city-right"><div class="rax-view-v2 area-name">{{locationCity}}</div></div></div><div class="rax-view-v2 flight-view-divider" style="margin:0 4vw;"></div><div class="rax-view-v2 __tracker flight-date flightsearch-row flightsearch-row-height"><div class="rax-view-v2 flightsearch-row-height flightsearch-day-wrapper"><div class="rax-view-v2 trafficx-year-date-wrap"><div class="rax-view-v2 trafficx-year-date-wrap"><div class="rax-view-v2 year-date year-date-font">{{tomorrowMonth}}</div><div class="rax-view-v2 year-date">月</div><div class="rax-view-v2 year-date year-date-font">{{tomorrowDay}}</div><div class="rax-view-v2 year-date">日</div></div><div class="rax-view-v2 trafficx-year-date-week">周五</div></div></div><div class="rax-view-v2 flight-view-divider"></div></div><div class="rax-view-v2 flightsearch-row select-box-wrapper hide show"><div class="rax-view-v2 flight-cabin"><div class="rax-view-v2 flight-cabin-box"><div class="rax-view-v2 flight-cabin-box-dom-left"><div class="rax-view-v2 __tracker flight-cabin-box-dom-item flight-cabin-box-dom-item-selected">全部舱位</div><div class="rax-view-v2 __tracker flight-cabin-box-dom-item">公务/头等舱</div></div><div class="rax-view-v2"><div class="rax-view-v2 hide show"><div class="rax-view-v2 flightHomePassenger-wrapper"><div class="rax-view-v2 __tracker flight-home-passenger"><div class="rax-view-v2 flight-home-passenger-right"><img class="flight-home-passenger-right-icon" src="https://gw.alicdn.com/imgextra/i3/O1CN017l3Vh81pd0fnLWXob_!!6000000005382-2-tps-45-45.png"><div class="rax-view-v2 flight-home-passenger-right-text">选择乘机人 快速找低价</div><span class="rax-text-v2 flight-home-passenger-right-image icon-font-style" style="font-family:fliggy-home-iconfont;"></span></div></div></div></div></div></div></div></div><div class="rax-view-v2 trafficx-search-button-wrapper"><div class="rax-view-v2 trafficx-search-button" style="color:#333;background:#ffdd00;">搜索机票</div></div><div class="rax-view-v2 flight-home-searchhistory-wrapper flight-home-searchnohistory-wrapper"></div><div class="rax-view-v2 flight-brand-container" style="margin-top:0vw;margin-bottom:3.2vw;"><div class="rax-view-v2"><div class="rax-view-v2 __tracker trafficx-brand-wrapper"><img class="trafficx-brand-logo" mode="widthFix" src="https://gw.alicdn.com/imgextra/i3/O1CN01sQgKbf1ShpePuqty7_!!6000000002279-2-tps-1816-124.png" style="width:60.5333vw;height:4.2667vw;"></div></div></div></div></div><div class="rax-view-v2"></div></div></div></div></div>`;

const trainHtml = `<div class="rax-view-v2 home-main-kingkong-search-item home-main-kingkong-search-item-active"><div class="rax-view-v2 pad"><div class="rax-view-v2 traffic-search-warpper"><div class="rax-view-v2 traffic-search-tabbar"><div class="rax-view-v2 traffic-tabbar-wrapper"><div class="rax-view-v2 traffic-tabbar"><div class="rax-view-v2 tabbar-indicator-wrapper" style="width:25%;transform:translateX(0%);"><div class="rax-view-v2 tabbar-indicator"></div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="0" class="rax-view-v2 traffic-tabbar-item-text traffic-tabbar-item-text-current"><div class="rax-view-v2">火车单程</div></div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="1" class="rax-view-v2 traffic-tabbar-item-text"><div class="rax-view-v2">火车往返</div></div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="2" class="rax-view-v2 traffic-tabbar-item-text"><div class="rax-view-v2">汽车票</div></div></div><div class="rax-view-v2 __tracker traffic-tabbar-item"><div data-index="3" class="rax-view-v2 traffic-tabbar-item-text"><div class="rax-view-v2">跨城·景区</div></div></div></div></div></div><div class="rax-view-v2 traffic-search-show"><div class="rax-view-v2"><div class="rax-view-v2 train-home-page-wrapper"><div class="rax-view-v2"><div class="rax-view-v2 train-home"><div class="rax-view-v2 train-home-main-card"><div class="rax-view-v2 train-search-box"><div class="rax-view-v2 train-path"><div class="rax-view-v2 __tracker train-path-departure train-path-place"><div class="rax-view-v2 train-path-val">北京</div></div><div class="rax-view-v2 __tracker train-path-swap"><img class="train-path-swap-icon" src="https://gw.alicdn.com/imgextra/i4/O1CN01LCN1qG1hNsKq7femw_!!6000000004266-2-tps-66-46.png" style="width:7.0667vw;height:4.9333vw;"><img class="train-path-swap-ring" src="https://gw.alicdn.com/imgextra/i2/O1CN01Ss4UnI1MQz5mEm5vw_!!6000000001430-2-tps-101-105.png" style="width:10.6667vw;height:10.6667vw;"></div><div class="rax-view-v2 __tracker train-path-arrive train-path-place"><div class="rax-view-v2 train-path-val">上海</div></div></div><div class="rax-view-v2 rxpi-train-city-picker-root"></div><div class="rax-view-v2 train-home-date"><div class="rax-view-v2 __tracker date-selector"></div></div><div class="rax-view-v2 train-checklist"><div class="rax-view-v2 __tracker checklist-wrapper" style="width:29.3333vw;before:[object Object];"><div class="rax-view-v2" style="content:'';position:absolute;left:-5.8667vw;top:50%;transform:translateY(-50%);width:6.4vw;height:8.2667vw;"></div><div class="rax-view-v2 radio-wrapper"></div><span class="rax-text-v2 checklist-text">学生票</span></div><div class="rax-view-v2 __tracker checklist-wrapper" style="width:36.8vw;justify-content:flex-end;after:[object Object];"><div class="rax-view-v2 radio-wrapper"></div><span class="rax-text-v2 checklist-text">只看高铁动车</span><div class="rax-view-v2" style="content:'';position:absolute;right:-5.8667vw;top:50%;transform:translateY(-50%);width:6.4vw;height:8.2667vw;z-index:1;"></div></div></div><div class="rax-view-v2 train-submit"><div class="rax-view-v2 __tracker button">搜索火车票</div></div><div class="rax-view-v2 train-relieved-banner"><div class="rax-view-v2"><div class="rax-view-v2 __tracker trafficx-brand-wrapper"><img class="trafficx-brand-logo" mode="widthFix" src="https://gw.alicdn.com/imgextra/i4/O1CN01FklJYH1DRbNXtMN5G_!!6000000000213-2-tps-2122-124.png" style="width:70.6667vw;height:4.1333vw;"></div></div></div></div></div></div></div></div></div></div><div class="rax-view-v2 traffic-search-hide"><div class="rax-view-v2 bus-combination-search other-bus-combination-search taobao-traffic-bus-combination-search bus-main-contianer"><div class="rax-view-v2 bus-combination-search-card"><div class="rax-view-v2 bus-combination-search-card-search bus-combination-search-card-search-show"><div class="rax-view-v2 __tracker bus-search-container"><div class="rax-view-v2 bus-search"><div class="rax-view-v2 bus-search-panel"><div class="rax-view-v2 bus-search-panel-city" style="padding-top:0vw;"><div class="rax-view-v2 __tracker bus-search-panel-city-item"><div class="rax-view-v2 bus-search-panel-city-value"><div class="rax-view-v2 bus-search-panel-city-value-text bus-search-panel-city-value-placeholder">选择出发</div></div></div><div class="rax-view-v2 __tracker bus-search-panel-city-switch"><div class="rax-view-v2 bus-search-panel-city-switch-circle"></div></div><div class="rax-view-v2 __tracker bus-search-panel-city-item bus-search-panel-city-item-arr"><div class="rax-view-v2 bus-search-panel-city-value bus-search-panel-city-value-arr"><div class="rax-view-v2 bus-search-panel-city-value-text bus-search-panel-city-value-placeholder">选择到达</div></div></div></div><div class="rax-view-v2 __tracker undefined"><div class="rax-view-v2 bus-search-panel-date"><div class="rax-view-v2 trafficx-year-date-wrap"><div class="rax-view-v2 trafficx-year-date-wrap"><div class="rax-view-v2 year-date year-date-font">7</div><div class="rax-view-v2 year-date">月</div><div class="rax-view-v2 year-date year-date-font">2</div><div class="rax-view-v2 year-date">日</div></div><div class="rax-view-v2 trafficx-year-date-week">今天</div></div></div></div><div class="rax-view-v2 __tracker undefined"><div class="rax-view-v2 trafficx-search-button-wrapper"><div class="rax-view-v2 trafficx-search-button" style="color:#333;background:#ffdd00;">搜索汽车票</div></div></div></div></div></div></div><div class="rax-view-v2 bus-combination-search-card-search"><div class="rax-view-v2 __tracker bus-search-container"><div class="rax-view-v2 bus-search"><div class="rax-view-v2 bus-search-panel"><div class="rax-view-v2 bus-search-panel-city" style="padding-top:0vw;"><div class="rax-view-v2 __tracker bus-search-panel-city-item"><div class="rax-view-v2 bus-search-panel-city-type"><span class="rax-text-v2 bus-search-panel-city-type-text">城市</span></div><div class="rax-view-v2 bus-search-panel-city-value"><div class="rax-view-v2 bus-search-panel-city-value-text bus-search-panel-city-value-small bus-search-panel-city-value-placeholder">选择出发</div></div></div><div class="rax-view-v2 __tracker bus-search-panel-city-switch"><div class="rax-view-v2 bus-search-panel-city-switch-circle"></div></div><div class="rax-view-v2 __tracker bus-search-panel-city-item bus-search-panel-city-item-arr"><div class="rax-view-v2 bus-search-panel-city-type"><span class="rax-text-v2 bus-search-panel-city-type-text">城市</span></div><div class="rax-view-v2 bus-search-panel-city-value bus-search-panel-city-value-arr"><div class="rax-view-v2 bus-search-panel-city-value-text bus-search-panel-city-value-small bus-search-panel-city-value-placeholder">选择到达</div></div></div></div><div class="rax-view-v2 __tracker undefined"><div class="rax-view-v2 bus-search-panel-date"><div class="rax-view-v2 trafficx-year-date-wrap"><div class="rax-view-v2 trafficx-year-date-wrap"><div class="rax-view-v2 year-date year-date-font">7</div><div class="rax-view-v2 year-date">月</div><div class="rax-view-v2 year-date year-date-font">2</div><div class="rax-view-v2 year-date">日</div></div><div class="rax-view-v2 trafficx-year-date-week">今天</div></div></div></div><div class="rax-view-v2 __tracker undefined"><div class="rax-view-v2 trafficx-search-button-wrapper"><div class="rax-view-v2 trafficx-search-button" style="color:#333;background:#ffdd00;">搜索班次</div></div></div></div></div></div></div></div></div></div></div></div></div>`;

const rentalCarHtml = `<div class="rax-view-v2 home-main-kingkong-search-item home-main-kingkong-search-item-active"><div class="rax-view-v2 rent-car-skeleton-box"><div class="rax-view-v2 rent-car-skeleton-switch"><div class="rax-view-v2 rent-car-switch-skeleton-item switch-one-item"></div><div class="rax-view-v2 rent-car-switch-skeleton-item"></div></div><div class="rax-view-v2 rent-car-skeleton-od-search"><div class="rax-view-v2 rent-car-skeleton-city"></div><div class="rax-view-v2 rent-car-skeleton-address"></div><div class="rax-view-v2 rent-car-skeleton-radio"></div></div><div class="rax-view-v2 rent-car-skeleton-od-search"><div class="rax-view-v2 rent-car-skeleton-city"></div><div class="rax-view-v2 rent-car-skeleton-address"></div><div class="rax-view-v2 rent-car-skeleton-radio"></div></div><div class="rax-view-v2 rent-car-skeleton-swiper"></div><div class="rax-view-v2 rent-car-skeleton-button"></div></div></div>`;

const ticketHtml = `<div class="rax-view-v2 home-main-kingkong-search-item home-main-kingkong-search-item-active"><div id="ticket-top-module" class="rax-view-v2"><div class="rax-view-v2 TicketSearchCard2mini-wrapper"><div class="rax-view-v2 minit__tabs"><div class="rax-view-v2 __tracker minit__tabs--item" style="font-weight:500;"><div class="rax-view-v2 minit__tabs--item-text">景点门票</div></div><div class="rax-view-v2 __tracker minit__tabs--item" style="font-weight:400;"><div class="rax-view-v2 minit__tabs--item-text">一日游</div></div><div class="rax-view-v2 __tracker minit__tabs--item" style="font-weight:400;"><div class="rax-view-v2 minit__tabs--item-text">多日游</div></div><div class="rax-view-v2 minit__tabs--slider" style="left:0vw;"><div class="rax-view-v2 minit__tabs--slider-inner"></div></div></div><div class="rax-view-v2 minit__sc"><div class="rax-view-v2 __tracker undefined"><div class="rax-view-v2 minit__cs"><div class="rax-view-v2 __tracker tsc2mini__cs"><div class="rax-view-v2 tsc2mini__cs-name" style="font-size:5.6vw;"></div><span class="rax-text-v2 tsc2mini__cs-icon" style="font-family:fliggy-home-iconfont;"></span></div><div class="rax-view-v2 minit__cs-search"><div class="rax-view-v2 minit-cs-ts"><div class="rax-view-v2 __tracker minit-cs-ts__text">景点名称/关键词</div></div></div></div><div class="rax-view-v2 __tracker minit__scb"><div class="rax-view-v2 minit__scb--text">搜索</div></div></div></div></div></div></div>`;

const overseasHtml = `<div class="rax-view-v2 home-main-kingkong-search-item home-main-kingkong-search-item-active"><div class="rax-view-v2 OverseasSearchCard2mini-wrapper"><div class="rax-view-v2 osc2mini__tabs"><div class="rax-view-v2 __tracker osc2mini__tabs--item" style="font-weight:500;width:25%;">签证</div><div class="rax-view-v2 __tracker osc2mini__tabs--item" style="font-weight:400;width:25%;">电话卡</div><div class="rax-view-v2 __tracker osc2mini__tabs--item" style="font-weight:400;width:25%;">流量包</div><div class="rax-view-v2 __tracker osc2mini__tabs--item" style="font-weight:400;width:25%;">随身WiFi</div><div class="rax-view-v2 osc2mini__tabs--slider" style="width:25%;left:0%;"><div class="rax-view-v2 osc2mini__tabs--slider-inner"></div></div></div><div class="rax-view-v2 osc2mini__sc"><div class="rax-view-v2 __tracker undefined"><div class="rax-view-v2 osc2mini__visa"><div class="rax-view-v2 osc2mini--header"><div class="rax-view-v2 __tracker osc2mini__cp"><div class="rax-view-v2 osc2mini__cp--text" style="font-size:5.6vw;">选择国家</div><span class="rax-text-v2" style="color:#919499;line-height:1;font-family:fliggy-home-iconfont;"></span></div></div></div><div class="rax-view-v2 osc2mini__button"><div class="rax-view-v2 osc2mini__button--button"><div class="rax-view-v2 __tracker osc2mini__button--jump">办理签证</div></div></div></div></div></div></div>`

export  const bizCSS = `.skeleton-tims-hotel-search-container { display: flex; background: rgb(255, 255, 255); border-top-left-radius: 3.2vw; border-top-right-radius: 3.2vw; }

.skeleton-tims-hotel-search-container .tab-bar { background-color: rgb(255, 255, 255); position: relative; display: flex; flex-direction: row; border-top-left-radius: 3.2vw; border-top-right-radius: 3.2vw; }

.skeleton-tims-hotel-search-container .tab-item-bottom-line { position: absolute; left: 50%; bottom: 0.4vw; display: block; transform: translateX(-50%); width: 7.2vw; height: 0.8vw; border-radius: 0.4vw; background-color: rgb(15, 19, 26); }

.skeleton-tims-hotel-search-container .tab-bar .current-tab-wraper { position: absolute; height: 12.1333vw; background: rgb(255, 255, 255); border-top-left-radius: 3.2vw; border-top-right-radius: 3.2vw; transition: all 0.3s ease 0s; transform: translateX(0px); left: 0px; width: 23.4667vw; z-index: 10; }

.skeleton-tims-hotel-search-container .tab-bar .current-tab-wraper .search-tabbar-indicator2 { display: inline-block; width: 5.6vw; position: absolute; height: 0.8vw; border-radius: 0.8vw; background-color: rgb(255, 219, 0); left: 50%; transform: translateX(-50%); bottom: 0px; margin-bottom: 0px; }

.skeleton-tims-hotel-search-container .tab-bar .current-tab-wraper-0 { position: absolute; height: 12.1333vw; background: rgb(255, 255, 255); border-top-left-radius: 3.2vw; border-top-right-radius: 3.2vw; transition: all 0.3s ease 0s; transform: translateX(0px); left: 0px; width: 23.4667vw; z-index: 10; }

.skeleton-tims-hotel-search-container .tab-bar .current-tab-wraper-0 .search-tabbar-indicator2 { display: inline-block; width: 5.6vw; position: absolute; height: 0.8vw; border-radius: 0.8vw; background-color: rgb(255, 219, 0); left: 50%; transform: translateX(-50%); bottom: 0px; margin-bottom: 0px; }

.skeleton-tims-hotel-search-container .tab-bar .tab-item { position: relative; display: flex; width: 25vw; height: 12vw; flex-direction: row; justify-content: center; align-items: center; z-index: 10; padding: 0px; font-size: 4.26667vw; color: rgb(15, 19, 26); font-weight: 400; }

.skeleton-tims-hotel-search-container .tab-bar .tab-item-selected { font-size: 4.26667vw; font-weight: 700; }

.skeleton-tims-hotel-search-container .tab-bar .tab-item .tabbar-item-sub-cornor { position: absolute; top: 0.26667vw; right: -4.66667vw; font-size: 2.66667vw; background: rgb(255, 80, 0); color: rgb(255, 255, 255); border-radius: 2.13333vw; padding: 0.53333vw 1.06667vw; max-width: 16vw; line-height: 3.73333vw; }

.skeleton-tims-hotel-search-container .tab-bar .tab-item .tabbar-item-sub-cornor::after { content: ""; width: 0px; height: 0px; position: absolute; left: 20%; bottom: -1.2vw; border-color: rgb(255, 80, 0) transparent transparent rgb(255, 80, 0); border-style: solid; border-width: 0.8vw 1.2vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content { background: rgb(255, 255, 255); border-radius: 0px 0px 3.2vw 3.2vw; box-shadow: rgba(0, 0, 0, 0.1) 0px 0.26667vw 4.8vw 0px; }

.tims-hotel-search-container .tims-hotel-search-content { margin-bottom: 0px; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-container { background: rgb(255, 255, 255); margin-bottom: 3.2vw; border-radius: 0px 0px 3.2vw 3.2vw; box-shadow: rgba(0, 0, 0, 0.1) 0px 0.26667vw 4.8vw 0px; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-flex-cell { position: relative; display: flex; align-items: center; flex-direction: row; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-line { position: relative; width: 80.8vw; margin: 0px 6.4vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-line::before { content: ""; position: absolute; inset: 0px auto auto 0px; width: 100%; height: 1px; background-color: rgb(235, 237, 242); display: block; box-sizing: border-box; z-index: 1; transform: scaleY(0.5); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-top-line { position: relative; width: 100%; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-top-line::before { content: ""; position: absolute; inset: 0px auto auto 0px; width: 100%; height: 1px; background-color: rgb(235, 237, 240); display: block; box-sizing: border-box; z-index: 1; transform: scaleY(0.5); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-container-cell { height: 53px; margin: 0px 4.8vw; box-sizing: border-box; border-bottom: 0.5px solid rgb(235, 237, 240); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-search-cell { min-height: 13.3333vw; font-size: 4.8vw; color: rgb(92, 95, 102); position: relative; box-sizing: border-box; flex: 1 1 0%; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-search-cell.placehold { color: rgb(171, 174, 179); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-height-big { min-height: 13.3333vw; font-size: 6.4vw; color: rgb(31, 37, 51); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-jiantou { justify-content: space-between; position: relative; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-jiantou::after { display: inline-block; width: 3.2vw; height: 3.2vw; content: ""; text-align: right; background-size: contain; background-position: 50% center; background-repeat: no-repeat; background-image: url("https://img.alicdn.com/imgextra/i3/O1CN01wnCf9v1FocVt5eBlN_!!6000000000534-2-tps-11-20.png"); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-my-location { font-size: 2.93333vw; display: flex; flex-direction: column; align-items: center; justify-content: center; color: rgb(0, 162, 255); margin-left: 9.06667vw; margin-right: 1.6vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-location-detail { font-size: 5.6vw; line-height: 1.2; word-break: break-all; font-weight: 700; color: rgb(15, 19, 26); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .detail-font { width: auto; line-height: 1.2; word-break: break-all; padding-right: 12px; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .detail-font-1 { font-size: 3.46667vw; line-height: 1.2; word-break: break-all; padding-right: 12px; font-weight: 400; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .hotelsearch-icon-location { margin-bottom: 0.8vw; background-image: url("https://gw.alicdn.com/imgextra/i3/O1CN013FVQxi1qBokomYoRF_!!6000000005458-2-tps-84-80.png"); background-size: contain; background-position: 50% center; width: 5.33333vw; height: 5.33333vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .location-text { font-size: 2.93333vw; color: rgb(0, 141, 255); font-weight: 400; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container { display: flex; flex-direction: column; justify-content: space-between; align-items: stretch; border-bottom: 0.5px solid rgb(242, 243, 245); margin-left: 4.8vw; margin-right: 4.8vw; padding-top: 3.73333vw; padding-bottom: 2.4vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container { display: flex; flex-direction: column; justify-content: center; width: 84vw; height: 13.6vw; border-radius: 1.6vw; margin-top: -1.06667vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-bottom { display: flex; flex-direction: row; justify-content: start; align-items: center; width: 84vw; height: 12vw; border-radius: 1.6vw; background-color: rgb(31, 39, 127); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-top { width: 2.4vw; height: 1.6vw; background-image: url("https://gw.alicdn.com/imgextra/i2/O1CN01EnCQKR1CCC93FbeHs_!!6000000000044-2-tps-36-24.png"); background-size: 100% 100%; margin-left: 8.8vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-bottom-left { width: 4vw; height: 4.26667vw; background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN014FXX6X1zJtyu0umIL_!!6000000006694-2-tps-60-64.png"); background-size: 100% 100%; margin-left: 2.4vw; margin-right: 2.4vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-bottom-right { width: 72.8vw; display: flex; flex-flow: row wrap; justify-content: start; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-normal-tip-text { font-size: 2.93333vw; color: rgb(255, 255, 255); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-highlight-tip-text { font-size: 2.93333vw; color: rgb(255, 227, 51); }

.skeleton-tims-hotel-search-container .hotel-search-international-tip-container { display: flex; flex-flow: row wrap; justify-content: start; position: relative; font-size: 2.93333vw; color: rgb(255, 255, 255); background-color: rgb(31, 39, 127); border-radius: 1.6vw; padding: 2.13333vw 2.4vw; margin-bottom: -1.06667vw; width: fit-content; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content { width: 100%; display: flex; flex-direction: row; align-items: center; justify-content: space-between; padding-bottom: 1.6vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left { display: flex; flex-direction: row; justify-content: start; align-items: flex-end; font-size: 0px; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-tips { font-size: 2.93333vw; color: rgb(255, 115, 0); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week { display: flex; flex-direction: row; align-items: flex-end; justify-content: center; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .text-day, .skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .text-month { font-family: AlibabaSans102-Bd; font-size: 6.4vw; line-height: 1; display: inline-block; vertical-align: text-bottom; color: rgb(15, 19, 26); font-weight: 400; position: relative; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week .date-value { font-size: 6.4vw; line-height: 1; font-weight: 400; color: rgb(15, 19, 26); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week .week-value { font-size: 3.73333vw; line-height: 1; color: rgb(145, 148, 153); margin-left: 0.8vw; margin-bottom: 0.53333vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-value-text { position: relative; top: -0.53333vw; color: rgb(15, 19, 26); font-weight: 700; font-family: PingFangSC-Regular; font-size: 3.73333vw !important; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .ci-co-outline { width: 4.4vw; height: 0.8vw; background-color: rgb(235, 237, 242); margin-left: 1.6vw; margin-right: 1.6vw; margin-bottom: 2.66667vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .nights-wrap { position: relative; padding-left: 1.06667vw; padding-right: 1.06667vw; text-align: center; margin: 0px 2.4vw 0.53333vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .nights-wrap .line { position: absolute; width: 100%; height: 0.26667vw; background-color: rgb(15, 19, 26); top: 50%; margin-top: -0.13333vw; left: 0px; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .nights-wrap .nights { position: relative; height: 4.8vw; line-height: 4.26667vw; font-size: 3.2vw; color: rgb(15, 19, 26); border: 0.26667vw solid rgb(15, 19, 26); border-radius: 4.8vw; padding-left: 2.13333vw; padding-right: 2.13333vw; background-color: rgb(255, 255, 255); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-right .total-text { font-size: 3.73333vw; color: rgb(153, 153, 153); line-height: 4.8vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector { display: flex; flex-direction: row; justify-content: space-between; align-items: center; margin-left: 4.8vw; margin-right: 4.8vw; padding-top: 3.46667vw; padding-bottom: 3.46667vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value { font-size: 4vw; max-width: 70.6667vw; margin-bottom: 0.26667vw; color: rgb(171, 174, 179); overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-img { max-width: 46.6667vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-num { max-width: 33.3333vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value-selected { color: rgb(31, 37, 51); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value-flex { display: flex; flex-direction: row; justify-content: start; align-items: center; width: 8vw; height: 8vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .hotelsearch-body-billion { height: 5.33333vw; width: 27.3333vw; display: flex; align-items: center; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .hotelsearch-body-billion-img { height: 10.2667vw; width: 27.3333vw; margin-right: 1.06667vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .hotelsearch-body-clear-filter { width: 3.2vw; height: 3.2vw; margin-right: 1.6vw; text-align: center; background: url("https://gw.alicdn.com/tfs/TB1pwe8H3TqK1RjSZPhXXXfOFXa-48-48.png") 50% center / contain no-repeat rgb(255, 255, 255); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .keywords-selector .search-people-number { font-size: 3.46667vw; color: rgb(92, 95, 102); padding-left: 6.66667vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .tims-search-btn { width: 89.3333vw; height: 11.2vw; border-radius: 5.86667vw; display: flex; flex-direction: row; justify-content: center; align-items: center; font-size: 4.53333vw; color: rgb(51, 51, 51); font-weight: 700; margin-bottom: 2.4vw; position: relative; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips { display: flex; flex-direction: row; justify-content: center; padding-bottom: 5.33333vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips .tip-icon { width: 60vw; height: 4.26667vw; background: url("https://gw.alicdn.com/imgextra/i2/O1CN01JUWP9m1HE6As9oSlS_!!6000000000725-2-tps-900-64.png") 50% center / contain no-repeat; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips .tip-text { display: flex; flex-direction: row; justify-content: start; align-items: center; margin-left: 1.6vw; }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips .tip-text .normal-text { font-size: 2.93333vw; color: rgb(92, 95, 102); }

.skeleton-tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips .tip-text .highlight-text { font-size: 2.93333vw; color: rgb(0, 191, 175); font-weight: 700; margin-left: 0.26667vw; margin-right: 0.26667vw; }

.skeleton-tims-hotel-search-container .hotelsearch-body-loading { font-size: 3.73333vw; color: rgb(170, 174, 178); line-height: 4.26667vw; font-weight: 400; }

.skeleton-tims-hotel-search-container ._tims_location_text { font-size: 3.2vw; font-weight: 700; color: rgb(51, 51, 51); }

.skeleton-tims-hotel-search-container .r_500 { width: 53.3333vw; }

.skeleton-tims-hotel-search-container .tims-search-tips { position: absolute; right: 0px; background-image: url("https://gw.alicdn.com/imgextra/i2/O1CN01WTBjEo29djN7UoDuD_!!6000000008091-2-tps-234-87.png"); background-size: 100% 100%; width: 31.2vw; height: 11.6vw; display: flex; flex-direction: column; justify-content: center; align-items: center; padding-left: 2.66667vw; }

.skeleton-tims-hotel-search-container .tims-search-tips-value { font-size: 2.66667vw; line-height: 2.66667vw; color: rgb(255, 255, 255); text-align: center; margin-bottom: 0.8vw; margin-left: 9.86667vw; }

.skeleton-tims-hotel-search-container .tims-search-tips-price-container { display: flex; flex-direction: row; justify-content: start; align-items: center; margin-left: 9.86667vw; }

.skeleton-tims-hotel-search-container .tims-search-tips-price-prefix, .skeleton-tims-hotel-search-container .tims-search-tips-price-value { font-size: 4.26667vw; line-height: 4.26667vw; color: rgb(255, 255, 255); text-align: center; }

.skeleton-tims-hotel-search-container .keywords-benefit-img { height: 5.6vw; }

.skeleton-tims-hotel-search-container .tims-search-bubble-pic { height: 8vw; position: absolute; right: 0px; top: -4vw; }

.skeleton-tims-hotel-search-container .tims-search-btn-container { display: flex; flex-direction: column; justify-content: center; align-items: center; }

.home-main-kingkong .home-main-kingkong-search-item { display: none; padding-top: 1.6vw; }

.home-main-kingkong .home-main-kingkong-search-item-active { display: flex; }

.tims-hotel-search-container .tims-hotel-search-content .new-location { position: relative; border-bottom: none; }

.tims-hotel-search-container .tims-hotel-search-content .new-location .hotelsearch-body-location-detail { font-size: 6.4vw; line-height: 6.93333vw; word-break: break-all; font-weight: 700; color: rgb(15, 19, 26); }

.tims-hotel-search-container .tims-hotel-search-content .new-location .hotelsearch-body-search-cell { min-height: 13.3333vw; font-size: 4.8vw; position: relative; box-sizing: border-box; }

.tims-hotel-search-container .tims-hotel-search-content .new-location .hotelsearch-flex-cell { flex: 0 0 auto; padding-right: 3.2vw; }

.tims-hotel-search-container .tims-hotel-search-content .new-location .hotelsearch-flex-cell > div { max-width: 24vw; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

.tims-hotel-search-container .tims-hotel-search-content .new-location .hotel-search-right-arrow::after { margin-left: 0.8vw; color: rgb(145, 148, 153); }

.tims-hotel-search-container .tims-hotel-search-content .new-location .keywords-scroll-wrap { flex: 1 1 0%; }

.tims-hotel-search-container .tims-hotel-search-content .new-location .keywords-scroll-wrap .keywords-suggest-shades, .tims-hotel-search-container .tims-hotel-search-content .new-location .keywords-scroll-wrap .keywords-suggest-shades-placeholder { height: 6.13333vw; width: 60vw; overflow: hidden; margin: 2.8vw 0px 0px; }

.tims-hotel-search-container .tims-hotel-search-content .new-location .keywords-scroll-wrap .keywords-skeleton-item { width: 16.4vw; margin-right: 0.8vw; }

.tims-hotel-search-container .tims-hotel-search-content .new-location .hotelsearch-body-my-location { position: absolute; top: 50%; transform: translateY(-50%); right: -9.33333vw; width: 16vw; height: 6.13333vw; background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgb(255, 255, 255) 26.25%); }

.tims-hotel-search-container .tims-hotel-search-content { background: rgb(255, 255, 255); padding-top: 0px; margin-bottom: 2.4vw; border-radius: 0px 0px 4vw 4vw; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-container { background: rgb(255, 255, 255); margin-bottom: 3.2vw; border-radius: 0px 0px 3.2vw 3.2vw; box-shadow: rgba(0, 0, 0, 0.1) 0px 0.26667vw 4.8vw 0px; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-flex-cell { position: relative; display: flex; align-items: center; flex-direction: row; flex: 1 1 0%; }

.tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-line { position: relative; width: 80.8vw; margin: 0px 6.4vw; }

.tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-line::before { content: ""; position: absolute; inset: 0px auto auto 0px; width: 100%; height: 1px; background-color: rgb(235, 237, 242); display: block; box-sizing: border-box; z-index: 1; transform: scaleY(0.5); }

.tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-top-line { position: relative; width: 100%; }

.tims-hotel-search-container .tims-hotel-search-content .ma-hotel-search-top-line::before { content: ""; position: absolute; inset: 0px auto auto 0px; width: 100%; height: 1px; background-color: rgb(235, 237, 240); display: block; box-sizing: border-box; z-index: 1; transform: scaleY(0.5); }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-container-cell { margin: 0px 4vw; box-sizing: border-box; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-search-cell { min-height: 13.3333vw; font-size: 4.8vw; color: rgb(92, 95, 102); position: relative; box-sizing: border-box; flex: 1 1 0%; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-search-cell.placehold { color: rgb(171, 174, 179); }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-height-big { min-height: 15.7333vw; font-size: 4.8vw; color: rgb(31, 37, 51); }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-jiantou { justify-content: space-between; position: relative; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-jiantou::after { display: inline-block; content: ""; text-align: right; background-size: contain; background-position: 50% center; background-repeat: no-repeat; background-image: url("https://gw.alicdn.com/tfs/TB1xXcOa5rpK1RjSZFhXXXSdXXa-11-20.png"); width: 3.2vw; height: 3.2vw; margin-left: 1.6vw; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-s-24::after { width: 3.2vw; height: 3.2vw; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-my-location { font-size: 2.93333vw; display: flex; flex-direction: column; align-items: center; justify-content: center; color: rgb(0, 162, 255); margin-left: 9.06667vw; margin-right: 4vw; height: 9.6vw; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-body-location-detail { font-size: 5.6vw; line-height: 7.73333vw; word-break: break-all; font-weight: 700; color: rgb(15, 19, 26); justify-content: space-between; }

.tims-hotel-search-container .tims-hotel-search-content .detail-font { font-size: 4vw; line-height: 1.2; word-break: break-all; padding-right: 12px; font-weight: 700; }

.tims-hotel-search-container .tims-hotel-search-content .hotelsearch-icon-location { margin-bottom: 0.8vw; color: rgb(105, 105, 251); background-size: contain; background-position: 50% center; font-size: 4.8vw; }

.tims-hotel-search-container .tims-hotel-search-content .location-text { font-size: 2.93333vw; color: rgb(0, 162, 255); font-weight: 400; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container { position: relative; display: flex; flex-direction: column; justify-content: start; align-items: flex-start; margin-left: 4vw; margin-right: 4vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container { display: flex; flex-direction: column; justify-content: center; width: 84vw; height: 13.6vw; border-radius: 1.6vw; margin-bottom: 2.13333vw; margin-top: -1.06667vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-bottom { display: flex; flex-direction: row; justify-content: start; align-items: center; width: 84vw; height: 12vw; border-radius: 1.6vw; background-color: rgb(31, 39, 127); }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-top { width: 2.4vw; height: 1.6vw; background-image: url("https://gw.alicdn.com/imgextra/i2/O1CN01EnCQKR1CCC93FbeHs_!!6000000000044-2-tps-36-24.png"); background-size: 100% 100%; margin-left: 8.8vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-bottom-left { width: 4vw; height: 4.26667vw; background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN014FXX6X1zJtyu0umIL_!!6000000006694-2-tps-60-64.png"); background-size: 100% 100%; margin-left: 2.4vw; margin-right: 2.4vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-tip-container-bottom-right { width: 72.8vw; display: flex; flex-flow: row wrap; justify-content: start; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-normal-tip-text { font-size: 2.93333vw; color: rgb(255, 255, 255); }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-container .hotel-search-highlight-tip-text { font-size: 2.93333vw; color: rgb(255, 227, 51); }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content { width: 100%; display: flex; flex-direction: row; align-items: center; justify-content: space-between; height: 6.4vw; box-sizing: content-box; margin-top: 4vw; padding-bottom: 4vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left { display: flex; flex-direction: row; justify-content: start; align-items: flex-end; font-size: 0px; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-tips { font-size: 2.93333vw; color: rgb(145, 148, 153); margin-bottom: 0.8vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week { display: flex; flex-direction: row; align-items: flex-end; justify-content: center; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week .date-value { font-size: 6.4vw; line-height: 1; font-weight: 400; color: rgb(15, 19, 26); }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week .week-value { font-size: 2.66667vw; line-height: 1; color: rgb(145, 148, 153); text-align: left; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week .domestic-week-value { font-size: 3.73333vw; line-height: 1; margin-bottom: 0.26667vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .date-week .week-value-local { margin-top: 0.26667vw; margin-bottom: 0.26667vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .ci-co-outline { width: 3.2vw; height: 0.4vw; border-radius: 0.2vw; background-color: rgb(235, 237, 242); margin-left: 3.2vw; margin-right: 3.2vw; margin-bottom: 2.4vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .text-day, .tims-hotel-search-container .tims-hotel-search-content .date-picker-content .text-month { font-family: AlibabaSans102-Bd; font-size: 6.4vw; line-height: 1; display: inline-block; vertical-align: text-bottom; color: rgb(15, 19, 26); font-weight: 400; position: relative; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .date-value-text { position: relative; top: -0.26667vw; color: rgb(15, 19, 26); font-weight: 700; font-family: PingFangSC-Regular; font-size: 3.73333vw !important; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .nights-wrap { position: relative; padding-left: 1.06667vw; padding-right: 1.06667vw; text-align: center; margin: 0px 2.4vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .nights-wrap .line { position: absolute; width: 100%; height: 0.26667vw; background-color: rgb(15, 19, 26); top: 50%; margin-top: -0.13333vw; left: 0px; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-left .nights-wrap .nights { position: relative; line-height: 4.8vw; font-size: 3.2vw; font-weight: 500; color: rgb(15, 19, 26); border: 0.26667vw solid rgb(15, 19, 26); border-radius: 4.8vw; padding-left: 1.6vw; padding-right: 1.6vw; background-color: rgb(255, 255, 255); font-family: PingFangSC-Regular; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-right { margin-bottom: 0px !important; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-right .total-text { font-size: 2.93333vw; color: rgb(92, 95, 102); line-height: 4.8vw; }

.date-picker-container .hotel-search-international-tip-container { position: relative; font-size: 2.66667vw; font-weight: 400; color: rgb(255, 255, 255); background-color: rgb(31, 39, 127); border-radius: 0.8vw; padding-left: 2.13333vw; padding-right: 2.13333vw; line-height: 5.33333vw; height: 5.33333vw; align-items: center; margin-bottom: 3.2vw; display: flex; flex-flow: row wrap; justify-content: start; }

.date-picker-container .hotel-search-international-tip-icon { width: 2.93333vw; height: 3.2vw; background-image: url("https://gw.alicdn.com/imgextra/i4/O1CN01xpKpdx1v1LlF1bf6B_!!6000000006112-2-tps-56-56.png"); background-size: 100% 100%; margin-right: 1.86667vw; }

.tims-hotel-search-container .tims-hotel-search-content .date-picker-content .tims-right .total-text::after { display: inline-block; content: ""; text-align: right; background-size: contain; background-position: 50% center; background-repeat: no-repeat; background-image: url("https://gw.alicdn.com/imgextra/i4/O1CN01SbaUde1gvwIoorhRR_!!6000000004205-2-tps-24-44.png"); width: 3.2vw; height: 3.2vw; margin-left: 1.6vw; position: relative; top: 0.26667vw; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector { display: flex; flex-direction: row; justify-content: space-between; align-items: center; margin-left: 4vw; margin-right: 4vw; padding-bottom: 2vw; padding-top: 2vw; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector.is-new-home { padding-bottom: 1.6vw; }

.tims-hotel-search-container .tims-hotel-search-content .show-bottom-border { border-bottom: 0.5px solid rgb(242, 243, 245); }

.tims-hotel-search-container .tims-hotel-search-content .no-bottom-border { border-bottom: 0px solid rgb(242, 243, 245); align-items: center; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value { font-size: 4vw; max-width: 70.6667vw; margin-bottom: 0.26667vw; color: rgb(145, 148, 153); overflow: hidden; white-space: nowrap; text-overflow: ellipsis; flex: 1 1 0%; justify-content: center; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value .keywords-value-text { width: 34.6667vw; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value.is-new-home { flex-direction: row; align-items: center; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-img { max-width: 46.6667vw; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-num { max-width: 33.3333vw; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value-selected { color: rgb(31, 37, 51); font-weight: 500; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .keywords-value-flex { display: flex; flex-direction: row; justify-content: start; align-items: center; max-height: 8.53333vw; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .hotelsearch-body-billion { max-height: 8.53333vw; display: flex; align-items: center; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .hotelsearch-body-billion-img { height: 8.53333vw; margin-right: 1.06667vw; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .hotelsearch-body-clear-filter { width: 3.2vw; height: 3.2vw; margin-right: 1.6vw; text-align: center; background: url("https://gw.alicdn.com/tfs/TB1pwe8H3TqK1RjSZPhXXXfOFXa-48-48.png") 50% center / contain no-repeat rgb(255, 255, 255); }

.tims-hotel-search-container .tims-hotel-search-content .hotel-search-right-arrow { font-size: 6.4vw; position: relative; color: rgb(159, 161, 165); }

.tims-hotel-search-container .tims-hotel-search-content .hotel-search-triangle { width: 0px; height: 0px; border-left: 1.2vw solid transparent; border-right: 1.2vw solid transparent; border-top: 2vw solid rgb(15, 19, 26); margin-left: 2.66667vw; }

.tims-hotel-search-container .tims-hotel-search-content .keywords-selector .search-people-number { font-size: 3.46667vw; color: rgb(92, 95, 102); padding-left: 6.66667vw; }

.tims-hotel-search-container .tims-hotel-search-content .tims-search-btn { height: 11.2vw; border-radius: 5.86667vw; background-color: rgb(255, 224, 3); display: flex; flex-direction: row; justify-content: center; align-items: center; margin-left: 4vw; margin-right: 4vw; margin-bottom: 3.2vw; font-weight: 700; font-size: 4.26667vw; background-size: 100% 100%; background-repeat: no-repeat; position: relative; }

.tims-hotel-search-container .tims-hotel-search-content .tims-search-btn-gap { margin-top: 1.6vw; }

.tims-hotel-search-container .tims-hotel-search-content .tims-search-btn-text { font-size: 4.26667vw; color: rgb(15, 19, 26); }

.tims-hotel-search-container .tims-hotel-search-content .tims-search-bubble-pic { height: 8vw; position: absolute; right: 0px; top: -4vw; }

.tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips { display: flex; flex-direction: row; justify-content: center; padding-bottom: 4vw; margin: 0px auto !important; }

.tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips .tip-text { display: flex; flex-direction: row; justify-content: start; align-items: center; margin-left: 1.6vw; }

.tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips .tip-text .normal-text { font-size: 2.93333vw; line-height: 2.93333vw; color: rgb(92, 95, 102); }

.tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips .tip-text .highlight-text { font-size: 2.93333vw; color: rgb(0, 191, 175); font-weight: 700; margin-left: 0.26667vw; margin-right: 0.26667vw; }

.tims-hotel-search-content .keywords-suggest-shades { height: 6.13333vw; display: flex; margin: 2.8vw 4.8vw 4vw; }

.tims-hotel-search-content .keywords-suggest-shades .keywords-suggest-shade-item { height: 6.13333vw; line-height: 6.13333vw; color: rgb(102, 102, 102); background-color: rgb(247, 248, 250); border-radius: 3.73333vw; padding-left: 2.13333vw; padding-right: 2.13333vw; max-width: 29.8667vw; font-size: 3.2vw; margin-right: 0.8vw; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-shrink: 0; }

.tims-hotel-search-content .keywords-suggest-shades .keywords-suggest-shade-item-img { height: 6.13333vw; border-radius: 3.73333vw; font-size: 3.2vw; margin-right: 1.6vw; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-shrink: 0; }

.tims-hotel-search-content .keywords-suggest-shades-placeholder { display: flex; margin: 2.8vw 4.8vw 4vw; height: 6.13333vw; justify-content: space-between; flex-direction: row; }

.tims-hotel-search-content .keywords-skeleton-item { height: 6.93333vw; display: flex; flex-direction: column; background-image: linear-gradient(90deg, rgb(242, 243, 245) 25%, rgb(255, 255, 255) 37%, rgb(242, 243, 245) 63%); border-radius: 3.73333vw; background-size: 400% 100%; background-position: 100% 50%; animation: 1.4s ease 0s infinite normal none running breathing; width: 16vw; }

.tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips.hotel-mind-service-panel .tip-icon { height: 2.66667vw; }

.tims-hotel-search-container .tims-hotel-search-content .tims-hotel-tips.hotel-mind-service-panel .tip-icon-oversea { height: 3.2vw; }

.tims-hotel-search-container .tab-bar { position: relative; display: flex; flex-direction: row; background: rgb(255, 255, 255); border-top-left-radius: 0px; border-top-right-radius: 0px; width: 100%; }

.tims-hotel-search-container .tab-item-bottom-line { position: absolute; bottom: 1.86667vw; display: block; transform: translateX(-50%); left: 50%; width: 7.2vw; height: 0.8vw; border-radius: 0.4vw; background-color: rgb(15, 19, 26); }

.tims-hotel-search-container .tab-bar .current-tab-wraper { position: absolute; will-change: left; top: 12.1333vw; background: rgb(255, 255, 255); transition: all 0.1s ease 0s; transform: translateX(0px); left: 0px; z-index: 10; width: 25vw; border-top-left-radius: 0px; border-top-right-radius: 0px; }

.tims-hotel-search-container .tab-bar .current-tab-wraper .search-tabbar-indicator2 { display: inline-block; width: 5.6vw; position: absolute; height: 0.8vw; border-radius: 0.8vw; background-color: rgb(255, 219, 0); left: 50%; transform: translateX(-50%); bottom: 0px; margin-bottom: 0px; }

.tims-hotel-search-container .tab-bar .tab-item { position: relative; display: flex; flex: 1 1 0%; height: 11.7333vw; flex-direction: row; justify-content: center; align-items: center; z-index: 10; padding: 0px; font-size: 4vw; color: rgb(15, 19, 26); font-weight: 400; }

.tims-hotel-search-container .tab-bar .tab-item-selected { font-size: 4vw; font-weight: 700; }

.tims-hotel-search-container .tab-bar .tab-item .tabbar-item-sub-cornor { position: absolute; top: 0.26667vw; right: -1.33333vw; font-size: 2.66667vw; background: rgb(255, 80, 0); color: rgb(255, 255, 255); border-radius: 2.13333vw; padding: 0.53333vw 1.06667vw; max-width: 16vw; line-height: 3.73333vw; }

.tims-hotel-search-container .tab-bar .tabbar-item-sub-cornor-background { position: absolute; top: -0.66667vw; height: 4.53333vw; font-size: 2.66667vw; max-width: 16vw; line-height: 3.73333vw; }

.tims-hotel-search-container .tab-bar .tab-item .tabbar-item-sub-cornor::after { content: ""; width: 0px; height: 0px; position: absolute; left: 20%; bottom: -1.2vw; border-color: rgb(255, 80, 0) transparent transparent rgb(255, 80, 0); border-style: solid; border-width: 0.8vw 1.2vw; }

.child-popup-wrapper .tab-item { display: inline-block; width: 33.333%; position: relative; padding: 3.86667vw 0px 4vw; }

.flight-cert-rule-wrap .flight-cert-rule-panel .title .line { width: 0.8vw; height: 20px; margin-right: 4px; background: rgb(255, 230, 0); border-top-right-radius: 0.8vw; border-bottom-right-radius: 0.8vw; }

.rxpi-flight-passenger-selector-inter-tel-input .line { width: 0.26667vw; height: 5.6vw; background-color: rgb(242, 243, 245); margin-left: 2.4vw; }

.flight-form-instructions .flight-form-instructions-tabs .tab-item { align-items: center; justify-content: space-between; margin-right: 7.46667vw; }

.flight-form-instructions .flight-form-instructions-tabs .tab-item:last-child { margin-right: 3.2vw; }

.flight-form-instructions .flight-form-instructions-tabs .tab-item .active-line { width: 7.2vw; height: 0.8vw; border-radius: 0.4vw; margin-top: 0px; }

.family-trip-tip .tip-icon { width: 15.3333vw; height: 12vw; position: absolute; bottom: -2.93333vw; left: -8vw; }

.faq-item-block .faq-item-content .faq-title .tip-icon { width: 4vw; height: 4vw; margin-right: 1.33333vw; }

.guess-like-container .location-text { margin-left: 0.26667vw; font-size: 2.66667vw; line-height: 2.66667vw; color: rgb(255, 255, 255); }

        .home-main-kingkong .home-main-kingkong-search-item { display: none; padding-top: 1.6vw; }

.home-main-kingkong .home-main-kingkong-search-item-active { display: flex; }

.hide { opacity: 0; }

.show { opacity: 1; }

.new-ota-pad .hide { opacity: 0; }

.new-ota-pad .show { opacity: 1; }

.ota-rate-pad-new--international .ota-title.show { box-shadow: none; background: rgb(255, 255, 255); }

.ota-rate-pad-new--international .ota-title.show::after { display: none; }

.ota-rate-pad-new--international .ota-title.show .ota-x-title-text, .ota-rate-pad-new .ota-title.show .ota-x-title-text { height: 3.73333vw; }

.room-ota-common-pad .hide { opacity: 0; }

.room-ota-common-pad .show { opacity: 1; }

.new-ota-pad-2023 .hide { opacity: 0; }

.new-ota-pad-2023 .show { opacity: 1; }

.traffic-tabbar-wrapper { width: 100%; }

.traffic-tabbar { display: flex; flex-direction: row; align-items: center; position: relative; width: 100%; height: 13.0667vw; background-color: rgb(255, 255, 255); }

.traffic-tabbar .tabbar-indicator-wrapper { position: absolute; left: 0px; bottom: 0px; transition: all 0.2s ease 0s; transform: translateX(0px); }

.traffic-tabbar .tabbar-indicator-wrapper .tabbar-indicator { position: absolute; bottom: 2.4vw; left: 50%; transform: translateX(-50%); width: 6.13333vw; height: 0.8vw; border-radius: 0.4vw; background-color: rgb(15, 19, 26); }

.traffic-tabbar .traffic-tabbar-item { position: relative; display: flex; flex-direction: row; align-items: center; justify-content: center; flex: 1 1 0%; height: 13.0667vw; }

.traffic-tabbar .traffic-tabbar-item .traffic-tabbar-item-text { position: relative; bottom: 0.13333vw; font-size: 4vw; font-family: "PingFang SC"; color: rgb(15, 19, 26); line-height: 1; text-align: center; }

.traffic-tabbar .traffic-tabbar-item .traffic-tabbar-item-text-current { font-weight: 500; }

.traffic-tabbar .traffic-tabbar-item .traffic-tabbar-item-tag { display: flex; flex-direction: row; align-items: center; position: absolute; top: 0.53333vw; left: 50%; width: max-content; height: 3.46667vw; padding: 0px 1.06667vw; border-radius: 3.2vw 3.2vw 3.2vw 0.53333vw; background-color: rgb(255, 85, 51); color: rgb(255, 255, 255); font-size: 2.26667vw; }

.flight-search-od-exchange-wrap { flex-direction: row; display: flex; padding: 2.13333vw 4vw; }

.flight-search-od-exchange-wrap.flight-search-od-exchange-multi-wrap { padding: 0px; flex: 1 1 0%; }

.flight-search-od-exchange-wrap .flight-search-od-exchange-dep-name-wx { flex: 1 1 0%; display: flex; flex-direction: row; align-items: center; }

.flight-search-od-exchange-wrap .flight-search-od-exchange-wx { display: flex; flex-direction: row; align-items: center; }

.flight-search-od-exchange-wrap .area-name { flex: 1 1 0%; font-family: "PingFang SC"; color: rgb(15, 19, 26); font-size: 5.86667vw; font-weight: 500; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; word-break: break-all; line-height: 8vw; }

.flight-search-od-exchange-wrap .area-name.flight-search-placeholder-name { color: rgb(210, 212, 217); }

.flight-search-od-exchange-wrap .area-name.flight-search-city-name-small { font-size: 4.8vw; }

.flight-search-od-exchange-wrap .flight-toggle-icon { position: relative; width: 9.06667vw; height: 9.06667vw; display: flex; align-items: center; align-self: center; margin: 0px 2.4vw; justify-content: center; }

.flight-search-od-exchange-wrap .flight-toggle-icon.disabled-toggle { filter: grayscale(100%); opacity: 0.4; }

.flight-search-od-exchange-wrap .flight-toggle-icon .fixed-flight-icon { width: 9.06667vw; height: 9.06667vw; position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); background-image: url("https://gw.alicdn.com/imgextra/i2/O1CN01ejmJ6322u2P4Vf87l_!!6000000007179-2-tps-139-139.png"); background-size: 100% 100%; }

.flight-search-od-exchange-wrap .flight-toggle-icon .od-exchange-icon-ring { width: 9.06667vw; height: 9.06667vw; background-image: url("https://gw.alicdn.com/imgextra/i4/O1CN01oO7XF31f2EiOkaHKq_!!6000000003948-2-tps-138-139.png"); background-size: 100% 100%; }

.flight-search-od-exchange-wrap .flight-toggle-icon .od-exchange-rotate { animation: 0.4s linear 0s infinite normal none running myRotate; }

.flight-search-od-exchange-wrap .flight-search-od-city { flex: 1 1 0%; display: flex; flex-direction: row; align-items: center; color: rgb(61, 61, 61); font-size: 5.86667vw; font-weight: 700; }

.flight-search-od-exchange-wrap .flight-search-od-city-right { text-align: right; justify-content: flex-end; }

.flight-search-od-exchange-wrap .od-dep-translateHide { animation: 0.4s linear 0s 1 normal none running leftTranslate; }

.flight-search-od-exchange-wrap .od-arr-translateHide { animation: 0.4s linear 0s 1 normal none running rightTranslate; }

.trafficx-year-date-wrap { display: flex; flex-direction: row; align-items: baseline; }

.trafficx-year-date-wrap .year-date { font-size: 3.73333vw; color: rgb(15, 19, 26); }

.trafficx-year-date-wrap .year-date-font { font-size: 6.4vw; font-family: AlibabaSans102; }

.trafficx-year-date-week { font-weight: 400; font-size: 3.46667vw; color: rgb(15, 19, 26); margin-left: 1.6vw; }

.flight-view-divider { position: relative; }

.flight-view-divider::after { pointer-events: none; position: absolute; content: ""; height: 1px; background: rgb(242, 243, 245); left: 0px; right: 0px; bottom: 0px; transform-origin: 100% 100%; }

.flight-date.flight-date-month { margin-bottom: 3.2vw; }

.flight-date.flight-date-multi-wrap { padding-left: 0px; padding-right: 0px; }

.flight-date.flight-date-multi-wrap .trafficx-year-date-wrap { padding-left: 3.2vw; }

.flight-date .flight-date-gap { position: absolute; left: 50%; bottom: 0px; transform: translateX(-50%); font-family: "PingFang SC"; font-size: 3.46667vw; line-height: 1; color: rgb(92, 95, 102); padding-bottom: 4vw; font-weight: 400; }

.flight-date .flightsearch-day-wrapper, .flight-date .flightsearch-month-wrapper { position: relative; display: flex; flex-direction: row; align-items: center; justify-content: space-between; color: rgb(15, 19, 26); font-weight: 500; font-size: 4.26667vw; }

.flightHomePassenger-wrapper { width: 100%; }

.keyborad-paste-popover-container .keyborad-paste-popover-content.show { display: block; }

.keyborad-paste-popover-container .keyborad-paste-popover-content.hide { display: none; }

.flight-home-passenger { position: relative; justify-content: flex-end; height: 8.53333vw; box-sizing: border-box; }

.flight-home-passenger, .flight-home-passenger-right { display: flex; align-items: center; flex-direction: row !important; }

.flight-home-passenger-right { font-size: 0px; justify-content: center; margin-right: -1.06667vw; }

.flight-home-passenger-right-icon { width: 4vw; height: 4vw; margin-right: 0.26667vw; }

.flight-home-passenger-right-text { font-size: 3.46667vw; line-height: 1; font-weight: 500; color: rgb(15, 19, 26); font-family: "PingFang SC"; }

.flight-home-passenger-right-image { font-size: 3.46667vw; color: rgb(213, 213, 213); }

.flight-cabin { width: 100%; }

.flight-cabin .flight-cabin-box { width: 100%; display: flex; flex-direction: row; justify-content: space-between; padding: 3.2vw 0px; align-items: center; }

.flight-cabin .flight-cabin-box .flight-cabin-box-dom-left { display: flex; flex-direction: row; align-items: center; height: 8.53333vw; background-color: rgb(242, 243, 245); border-radius: 2.13333vw; padding: 0.8vw 0px 0.8vw 0.8vw; align-self: flex-end; }

.flight-cabin .flight-cabin-box .flight-cabin-box-dom-item { display: flex; flex-direction: row; align-items: center; font-family: "PingFang SC"; font-size: 3.46667vw; line-height: 1; color: rgb(15, 19, 26); margin-right: 0.8vw; padding: 0px 1.86667vw; height: 6.93333vw; border-radius: 1.6vw; letter-spacing: 0px; }

.flight-cabin .flight-cabin-box .flight-cabin-box-dom-item-selected { font-weight: 500; background-color: rgb(255, 255, 255); }

.select-box-wrapper.hide, .select-box-wrapper .hide { display: none; }

.select-box-wrapper.show, .select-box-wrapper .show { display: block; }

.select-box-wrapper .select-box-item-wrapper-right { font-size: 0px; display: flex; flex-direction: row; justify-content: end; align-items: center; }

.select-box-wrapper .checbox-text { font-family: "PingFang SC"; display: inline-block; color: rgb(15, 19, 26); font-size: 3.46667vw; font-weight: 500; }

.select-box-wrapper .checkbox-text-cabin-arrow { font-size: 2.66667vw; margin-left: 1.6vw; color: rgb(213, 213, 213); }

.select-box-wrapper .checkbox-icon { width: 4vw; height: 4vw; margin-right: 0.26667vw; }

.trafficx-search-button-wrapper { width: 100%; padding: 0px 4vw; box-sizing: border-box; }

.trafficx-search-button-wrapper .trafficx-search-button { position: relative; background: rgb(255, 224, 51); color: rgb(15, 19, 26); font-size: 4.26667vw; text-align: center; width: 100%; height: 11.2vw; line-height: 4.26667vw; border-radius: 5.6vw; display: flex; justify-content: center; align-items: center; font-weight: 500; }

.trafficx-search-button-wrapper .trafficx-search-button-tips { display: flex; flex-direction: row; align-items: center; position: absolute; height: 4.53333vw; right: 0px; top: -1.33333vw; padding: 0px 1.6vw; background: linear-gradient(91.13deg, rgb(255, 75, 108) -3.42%, rgb(255, 24, 53) 74.28%, rgb(255, 74, 108) 100.65%); border-radius: 2.53333vw 2.53333vw 2.53333vw 0.26667vw; color: rgb(255, 255, 255); }

.trafficx-search-button-wrapper .trafficx-search-button-tips .trafficx-search-button-tips-title { font-family: "PingFang SC"; font-weight: 500; font-size: 2.66667vw; line-height: 1; }

.trafficx-search-button-wrapper .trafficx-search-button-tips .trafficx-search-button-tips-image { display: flex; height: 4vw; margin-right: 0.8vw; }

.trafficx-search-button-wrapper .trafficx-search-button-tips-icon { position: absolute; height: 5.33333vw; right: 0px; top: -2.66667vw; }

.flight-home-searchhistory-wrapper { position: relative; display: flex; flex-direction: row; padding: 3.2vw 4vw; }

.flight-home-searchhistory-wrapper .flight-home-searchhistory-scroller-wx { width: 0px; flex: 1 1 0%; height: 2.93333vw; font-size: 0px; padding-right: 0px !important; }

.flight-home-searchhistory-wrapper .flight-home-searchhistory-scroller { display: flex; flex: 1 1 0%; flex-direction: row; white-space: nowrap; width: 90.4vw; align-items: baseline; padding-right: 2.66667vw !important; }

.flight-home-searchhistory-wrapper .flight-home-searchhistory-scroller .flight-home-item-container { display: inline-block; margin-right: 4.8vw; white-space: nowrap; }

.flight-home-searchhistory-wrapper .flight-home-searchhistory-scroller .flight-home-item-container .flight-history-text { font-family: "PingFang SC"; color: rgb(92, 95, 102); font-size: 2.93333vw; line-height: 3.2vw; }

.flight-home-searchhistory-wrapper .flight-home-searchhistory-scroller .flight-home-item-container-right { width: 2.66667vw; height: 100%; }

.flight-home-searchhistory-wrapper .flight-home-searchhistory-scroller::-webkit-scrollbar { display: none; width: 0px; height: 0px; color: transparent; }

.flight-home-searchhistory-wrapper .flight-home-history-mask { position: absolute; right: 9.6vw; bottom: 0px; width: 17.8667vw; height: 8vw; background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgb(255, 255, 255)); }

.flight-home-searchhistory-wrapper .flight-home-history-clear { position: relative; z-index: 999; width: max-content; color: rgb(92, 95, 102); font-size: 2.93333vw; line-height: 3.2vw; }

.flight-home-searchnohistory-wrapper { padding: 0px 0px 4vw; }

.trafficx-brand-wrapper .trafficx-brand-logo { width: 93.6vw; }

.flight-brand-container { display: flex; flex-direction: row; justify-content: center; margin: 3.2vw auto; }

.flight-search-body .bottommenu-tab-bar { position: fixed; bottom: 0px; width: 100%; height: 18.6667vw; display: flex; flex-direction: row; align-items: center; justify-content: center; background-color: rgb(255, 255, 255); box-shadow: rgb(229, 229, 229) 0px 0.13333vw 0px 0px; box-sizing: border-box; padding: 0px 1.6vw; z-index: 900; border-top: 0.13333vw solid rgb(241, 241, 241); }

.flight-search-body .bottommenu-iphoneX { padding-bottom: env(safe-area-inset-bottom); box-sizing: content-box; }

.flight-search-body .bottommenu-tab-item { flex: 1 1 0%; display: flex; flex-direction: column; align-items: center; justify-content: center; position: relative; }

.flight-search-body .bottommenu-tab-icon { width: 5.86667vw; height: 5.86667vw; }

.flight-search-body .bottommenu-tab-text { font-size: 3.2vw; color: rgb(153, 153, 153); text-align: center; margin-top: 1.06667vw; white-space: nowrap; }

.flight-search-body .bottommenu-tab-tip { background-color: rgb(255, 80, 0); border-radius: 8px 8px 8px 0px; color: rgb(255, 255, 255); position: absolute; top: -0.53333vw; left: 67%; width: 8.53333vw; height: 3.2vw; line-height: 3.2vw; padding: 0.13333vw 0.8vw; font-size: 2.66667vw; }

.railway12306-blue-flight-search-body .trafficx-search-button-wrapper { padding: 0px 3.2vw; }

.railway12306-blue-flight-search-body .trafficx-search-button-wrapper .trafficx-search-button { height: 13.0667vw; line-height: 13.0667vw; border-radius: 1.06667vw; background: rgb(59, 153, 252) !important; color: rgb(255, 255, 255) !important; }

.railway12306-blue-flight-search-body .flight-toggle-icon .od-exchange-icon-ring { background-image: url("https://gw.alicdn.com/imgextra/i2/O1CN01T97FP21eX5mXsYIe3_!!6000000003880-2-tps-272-272.png") !important; }

.railway12306-blue-flight-search-body .fixed-flight-icon { background-image: url("https://gw.alicdn.com/imgextra/i2/O1CN01bfRqsh1HKVjvlXf1V_!!6000000000739-2-tps-272-272.png") !important; }

.railway12306-blue-flight-search-body .flight-home-passenger-right-icon, .railway12306-blue-flight-search-body .select-box-wrapper .checkbox-icon { display: none !important; }

.flightsearch-row { width: 100%; padding: 0px 4vw; box-sizing: border-box; }

.flightsearch-row-height { min-height: 13.3333vw; }

.flight-search-body { background-color: rgb(242, 243, 245); overflow: hidden; }

.flightsearch-wrapper { width: 100%; background: rgb(255, 255, 255); border-radius: 0px 0px 3.2vw 3.2vw; }

.flight-search-body-pray-gray .pray-gray { filter: grayscale(1); }

.bus-combination-search.railway12306-blue-bus-combination-search .bus-search-wrapper, .bus-combination-search.railway12306-blue-bus-combination-search .flightsearch-wrapper { margin: 0px; box-shadow: none; }

.bus-combination-search.railway12306-blue-bus-combination-search .trafficx-search-button-wrapper .trafficx-search-button { border-radius: 1.06667vw; background: rgb(59, 153, 252) !important; color: rgb(255, 255, 255) !important; }

.traffic-search-show { display: block; }

        .home-main-kingkong .home-main-kingkong-search-item { display: none; padding-top: 1.6vw; }

.home-main-kingkong .home-main-kingkong-search-item-active { display: flex; }

.traffic-tabbar-wrapper { width: 100%; }

.traffic-tabbar { display: flex; flex-direction: row; align-items: center; position: relative; width: 100%; height: 13.0667vw; background-color: rgb(255, 255, 255); }

.traffic-tabbar .tabbar-indicator-wrapper { position: absolute; left: 0px; bottom: 0px; transition: all 0.2s ease 0s; transform: translateX(0px); }

.traffic-tabbar .tabbar-indicator-wrapper .tabbar-indicator { position: absolute; bottom: 2.4vw; left: 50%; transform: translateX(-50%); width: 6.13333vw; height: 0.8vw; border-radius: 0.4vw; background-color: rgb(15, 19, 26); }

.traffic-tabbar .traffic-tabbar-item { position: relative; display: flex; flex-direction: row; align-items: center; justify-content: center; flex: 1 1 0%; height: 13.0667vw; }

.traffic-tabbar .traffic-tabbar-item .traffic-tabbar-item-text { position: relative; bottom: 0.13333vw; font-size: 4vw; font-family: "PingFang SC"; color: rgb(15, 19, 26); line-height: 1; text-align: center; }

.traffic-tabbar .traffic-tabbar-item .traffic-tabbar-item-text-current { font-weight: 500; }

.traffic-tabbar .traffic-tabbar-item .traffic-tabbar-item-tag { display: flex; flex-direction: row; align-items: center; position: absolute; top: 0.53333vw; left: 50%; width: max-content; height: 3.46667vw; padding: 0px 1.06667vw; border-radius: 3.2vw 3.2vw 3.2vw 0.53333vw; background-color: rgb(255, 85, 51); color: rgb(255, 255, 255); font-size: 2.26667vw; }

.trafficx-year-date-wrap { display: flex; flex-direction: row; align-items: baseline; }

.trafficx-year-date-wrap .year-date { font-size: 3.73333vw; color: rgb(15, 19, 26); }

.trafficx-year-date-wrap .year-date-font { font-size: 6.4vw; font-family: AlibabaSans102; }

.trafficx-year-date-week { font-weight: 400; font-size: 3.46667vw; color: rgb(15, 19, 26); margin-left: 1.6vw; }

.flight-date.flight-date-multi-wrap .trafficx-year-date-wrap { padding-left: 3.2vw; }

.passenger-combination-wrapper .passenger-combination-container-content .button-wrap .button { height: 11.2vw; width: 45.6vw; border-radius: 5.86667vw; text-align: center; line-height: 11.2vw; font-weight: 500; font-size: 4.26667vw; color: rgb(15, 19, 26); }

.passenger-combination-wrapper .passenger-combination-container-content .button-wrap .button.reset { background-color: rgb(247, 248, 250); }

.passenger-combination-wrapper .passenger-combination-container-content .button-wrap .button.confirm { background-color: var(--color-primary-5); color: var(--color-primary-9); }

.radio-wrapper { width: 5.33333vw; height: 5.33333vw; border-radius: 2.66667vw; border: 0.4vw solid rgb(235, 237, 240); }

.radio-wrapper { width: 5.6vw; height: 5.6vw; border-radius: 2.8vw; box-sizing: border-box; border: 0.4vw solid rgb(210, 212, 217); background-color: rgb(255, 255, 255); position: relative; flex-shrink: 0; display: flex; flex-direction: row; align-items: center; justify-content: center; }

.passenger-combination-wrapper .container-content .button-wrap .button.confirm { background-color: rgb(255, 224, 51) !important; }

.trafficx-search-button-wrapper { width: 100%; padding: 0px 4vw; box-sizing: border-box; }

.trafficx-search-button-wrapper .trafficx-search-button { position: relative; background: rgb(255, 224, 51); color: rgb(15, 19, 26); font-size: 4.26667vw; text-align: center; width: 100%; height: 11.2vw; line-height: 4.26667vw; border-radius: 5.6vw; display: flex; justify-content: center; align-items: center; font-weight: 500; }

.trafficx-search-button-wrapper .trafficx-search-button-tips { display: flex; flex-direction: row; align-items: center; position: absolute; height: 4.53333vw; right: 0px; top: -1.33333vw; padding: 0px 1.6vw; background: linear-gradient(91.13deg, rgb(255, 75, 108) -3.42%, rgb(255, 24, 53) 74.28%, rgb(255, 74, 108) 100.65%); border-radius: 2.53333vw 2.53333vw 2.53333vw 0.26667vw; color: rgb(255, 255, 255); }

.trafficx-search-button-wrapper .trafficx-search-button-tips .trafficx-search-button-tips-title { font-family: "PingFang SC"; font-weight: 500; font-size: 2.66667vw; line-height: 1; }

.trafficx-search-button-wrapper .trafficx-search-button-tips .trafficx-search-button-tips-image { display: flex; height: 4vw; margin-right: 0.8vw; }

.trafficx-search-button-wrapper .trafficx-search-button-tips-icon { position: absolute; height: 5.33333vw; right: 0px; top: -2.66667vw; }

.trafficx-brand-wrapper .trafficx-brand-logo { width: 93.6vw; }

.railway12306-blue-flight-search-body .trafficx-search-button-wrapper { padding: 0px 3.2vw; }

.railway12306-blue-flight-search-body .trafficx-search-button-wrapper .trafficx-search-button { height: 13.0667vw; line-height: 13.0667vw; border-radius: 1.06667vw; background: rgb(59, 153, 252) !important; color: rgb(255, 255, 255) !important; }

.train-home { margin-bottom: 2.4vw; border-radius: 0px 0px 3.2vw 3.2vw; padding: 0px; overflow: hidden; }

.train-path { display: flex; flex-direction: row; align-items: center; justify-content: space-between; height: 13.6vw; }

.train-path .train-path-place { display: flex; justify-content: center; height: 13.3333vw; flex: 1 1 0%; border-bottom: 0.13333vw solid rgb(235, 237, 240); }

.train-path .train-path-val { color: rgb(15, 19, 26); font-size: 5.86667vw; font-weight: 500; font-family: "PingFang SC"; opacity: 1; transform: translateX(0px) translateZ(0px); transition: opacity 0.2s ease-out 0s, transform 0.2s ease-out 0s; display: block; overflow: hidden; max-width: 36.2667vw; text-overflow: ellipsis; white-space: nowrap; }

.train-path .train-path-departure { align-items: flex-start; }

.train-path .train-path-arrive { align-items: flex-end; }

.train-path .train-path-swap { width: 10.1333vw; height: 10.1333vw; margin: 0px 1.86667vw; position: relative; display: flex; align-items: center; justify-content: center; }

.train-path .train-path-swap .adapt-to-wx, .train-path .train-path-swap .train-path-swap-ring { position: absolute; display: block; width: 10.6667vw; height: 10.6667vw; }

.train-path .train-path-swap .train-path-swap-icon { display: block; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 14.1333vw; height: 9.86667vw; }

.swap-animating .train-path-val { opacity: 0; }

.swap-animating .train-path-departure .train-path-val { transform: translateX(100%) translateZ(0px); }

.swap-animating .train-path-arrive .train-path-val { transform: translateX(-100%) translateZ(0px); }

.swap-animating .train-path-swap-ring { animation: 0.4s ease 0s 1 normal none running ringAnim; }

.train-home-date { height: 13.3333vw; display: flex; flex-direction: row; justify-content: center; align-items: center; border-bottom: 0.13333vw solid rgb(235, 237, 240); }

.train-home-date.sw-date { border-bottom: none; }

.train-home-date .date-selector { display: flex; flex-direction: row; justify-content: center; align-items: center; flex: 1 1 0%; }

.train-home-date .left-date { flex: 1 1 0%; }

.train-home-date .middle-day-tip { position: relative; display: flex; flex-direction: row; align-items: center; }

.train-home-date .middle-day-tip > .middle-day-array-left, .train-home-date .middle-day-tip > .middle-day-array-right { height: 0.26667vw; background-color: rgb(15, 19, 26); width: 1.6vw; }

.train-home-date .middle-day-tip > .middle-day { display: flex; font-size: 3.2vw; height: 4.8vw; padding: 0px 1.6vw; justify-content: center; box-sizing: border-box; border: 0.26667vw solid rgb(15, 19, 26); border-radius: 2.4vw; }

.train-home-date .right-date { flex: 1 1 0%; display: flex; flex-direction: row; justify-content: flex-end; }

.train-home-date .right-tip { font-size: 4.8vw; color: rgb(210, 212, 217); font-weight: 500; }

.train-checklist { height: 13.3333vw; justify-content: space-between; }

.checklist-wrapper, .train-checklist { display: flex; flex-direction: row; align-items: center; }

.checklist-wrapper { height: 8.26667vw; position: relative; }

.checklist-wrapper .radio-wrapper { width: 3.86667vw; height: 3.86667vw; border: 0.4vw solid rgba(51, 51, 51, 0.24); box-sizing: content-box; }

.checklist-wrapper .radio-wrapper-checked { border-color: rgb(255, 221, 0); }

.checklist-wrapper .checklist-text { margin-left: 1.6vw; color: rgb(51, 51, 51); font-size: 4vw; }

.checklist-wrapper .radio-image-container { display: block; margin: 0.53333vw 0px 0px; }

.train-submit { position: relative; }

.train-submit .button { justify-content: center; height: 11.2vw; font-size: 4.26667vw; color: rgb(51, 51, 51); text-align: center; line-height: 10.6667vw; font-weight: 700; background: rgb(255, 221, 0); border-radius: 5.33333vw; width: 100%; margin-bottom: 0.4vw; margin-top: 0px !important; }

.train-submit .tag { height: 4.26667vw; right: 5.06667vw; top: -1.86667vw; }

.train-submit .tag, .train-submit .tag2 { width: 24vw; position: absolute; background-repeat: no-repeat; background-position: 100% center; background-size: contain; }

.train-submit .tag2 { height: 7.46667vw; top: -2.26667vw; right: -0.13333vw; }

.train-submit .tag3 { position: absolute; height: 4.8vw; right: 1.6vw; top: -1.6vw; text-align: center; flex-direction: row; justify-content: center; align-items: center; background-repeat: no-repeat; background-position: 100% center; background-size: 100% 100%; padding-left: 1.6vw; padding-right: 1.6vw; }

.train-submit .tag-icon { width: 4.8vw; height: 4.8vw; margin-right: 0.8vw; }

.train-submit .tag-text { display: inline; font-size: 2.93333vw; color: rgb(255, 255, 255); }

.train-relieved-banner { display: flex; justify-content: center; align-items: center; width: 100%; height: 10.9333vw; }

.train-home-main-card { padding: 0px 5.86667vw; width: 100%; margin: 0px auto; background: rgb(255, 255, 255); }

.train-home-main-card .crm-resource-wx-benfit-bar { margin-top: 1.86667vw !important; }

.login-tips-wrapper .pad { width: 100%; height: 4.8vw; background-color: transparent; }

.train-home-page-wrapper { width: 100vw; background: rgb(242, 243, 245); margin-bottom: -3.2vw; overflow: hidden; }

.bus-combination-search.railway12306-blue-bus-combination-search.bus-main-contianer, .bus-combination-search.railway12306-blue-bus-combination-search.ship-main-contianer, .bus-combination-search.railway12306-blue-bus-combination-search.tour-main-contianer { margin: 0px 3.2vw; }

.bus-combination-search.railway12306-blue-bus-combination-search .bus-search-wrapper, .bus-combination-search.railway12306-blue-bus-combination-search .flightsearch-wrapper { margin: 0px; box-shadow: none; }

.bus-combination-search.railway12306-blue-bus-combination-search .yellow-tip { margin: 0px 1.6vw 4.8vw; }

.bus-combination-search.railway12306-blue-bus-combination-search .trafficx-search-button-wrapper .trafficx-search-button { border-radius: 1.06667vw; background: rgb(59, 153, 252) !important; color: rgb(255, 255, 255) !important; }

.bus-combination-search.railway12306-blue-bus-combination-search .bus-search-yellow-tip { width: 93.6vw; margin: 0px auto; }

.bus-combination-search.railway12306-blue-bus-combination-search .trafficx-banner-wrapper .banner-item-wrapper { margin: 3.2vw 0px 0px; }

.bus-combination-search { border-top-left-radius: 3.2vw; border-top-right-radius: 3.2vw; }

.bus-combination-search .normal-tabbar-wrapper.tabbar-wrapper { border-radius: 0px; }

.bus-combination-search .bus-search-yellow-tip { padding: 0px 3.2vw 0px 4.26667vw; background: rgb(255, 255, 255); }

.other-bus-combination-search .bus-combination-search-card { background: rgb(255, 255, 255); }

.other-bus-combination-search .midmenu-tab-bar { box-shadow: none !important; margin: 3.2vw !important; }

.bus-combination-search-card-search { display: none; }

.bus-combination-search-card-search-show { display: block; }

.bus-search-panel-city { display: flex; flex-direction: row; align-items: center; margin: 0px 5.6vw; height: 13.3333vw; box-sizing: content-box; }

.bus-search-panel-city-item { width: 35.2vw; display: flex; flex-direction: column; justify-content: center; background: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08) 50%, transparent 0px, transparent) 0px 100% / 100% 1px no-repeat transparent; height: 13.3333vw; }

.bus-search-panel-city-item-arr { align-items: flex-end; }

.bus-search-panel-city-value-text { display: -webkit-box; overflow: hidden; -webkit-box-orient: vertical; -webkit-line-clamp: 1; font-size: 5.86667vw; font-weight: 500; font-family: "PingFang SC"; color: rgb(15, 19, 26); }

.bus-search-panel-city-value-small { font-size: 5.33333vw; }

.bus-search-panel-city-value-placeholder { color: rgb(204, 204, 204); }

.bus-search-panel-city-type-text { font-size: 3.46667vw; color: rgb(204, 204, 204); }

.bus-search-panel-city-switch { flex: 1 1 0%; display: flex; flex-direction: row; align-items: center; justify-content: center; background: url("https://gw.alicdn.com/imgextra/i1/O1CN01ZJeHuI1FxJqPEbj04_!!6000000000553-2-tps-56-41.png") 50% center / 6.66667vw no-repeat rgb(255, 255, 255); }

.bus-search-panel-city-switch-circle { width: 10.1333vw; height: 10.5333vw; background: url("data:image/png;base64,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") 50% center / 10.1333vw 10.5333vw no-repeat; }

.bus-search-panel-date { margin: 0px 5.6vw; display: flex; flex-direction: row; align-items: center; height: 13.3333vw; }

.bus-search-panel-reverse .bus-search-panel-city-switch-circle { transform: rotate(1turn); transition: transform 0.4s ease 0s; }

.bus-search-panel-reverse .bus-search-panel-city-value { animation: 0.4s ease 0s 1 normal none running busSearchPanelSlideRight; }

.bus-search-panel-reverse .bus-search-panel-city-value-arr { animation: 0.4s ease 0s 1 normal none running busSearchPanelSlideLeft; }

.bus-search-container { flex: 1 1 0%; background-color: rgb(255, 255, 255); }

.bus-search-container .bus-search-scroll-tip { width: 100%; background-image: linear-gradient(90deg, rgb(255, 242, 234), rgb(255, 251, 248)); display: flex; flex-direction: row; justify-content: space-between; align-items: flex-start; overflow: hidden; position: relative; z-index: 1; margin-bottom: 2.66667vw; }

.bus-search-container .bus-search-scroll-tip-item { color: rgb(255, 115, 0); font-size: 3.46667vw; flex: 1 1 0%; word-break: break-all; padding-left: 2.66667vw; padding-right: 4vw; flex-direction: row; align-items: center; }

.bus-search-container .bus-search-scroll-tip-item-text { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: block; width: 86.6667vw; }

.bus-search-container .bus-search-scroll-tip-icon { width: 1.6vw; height: 2.66667vw; margin-left: 0.8vw; }

.bus-search-container .bus-utils-tips { padding: 2.66667vw; width: 100%; height: 106.667vw; overflow: auto; }

.bus-search-container .bus-utils-tips .bus-utils-tips-list-item { flex-direction: column; margin-bottom: 4vw; }

.bus-search-container .bus-utils-tips .bus-utils-tips-list-title { font-size: 4.26667vw; font-weight: 700; color: rgb(0, 0, 0); line-height: normal; margin: 2.66667vw; word-break: break-all; }

.bus-search-container .bus-utils-tips .bus-utils-tips-list-content { padding: 2.66667vw; border-radius: 3.2vw; font-size: 3.2vw; line-height: 4.8vw; color: rgb(102, 102, 102); background-color: rgb(247, 248, 250); word-break: break-all; }

.bus-search-container .bus-search-tips-button { align-items: center; justify-content: center; height: 10.6667vw; width: 88%; background-color: rgb(255, 221, 0); margin-bottom: 2.66667vw; margin-top: 1.33333vw; border-radius: 5.33333vw; }

.bus-search-container .bus-search-tips-pad-content { align-items: center; }

.bus-search .bus-search-history { margin: 3.46667vw 5.6vw 0px; flex-direction: row; overflow-x: scroll; }

.bus-search .bus-search-history::-webkit-scrollbar { display: none; }

.bus-search { width: 100%; border-radius: 3.2vw; overflow: hidden; background-color: rgb(255, 255, 255); padding-bottom: 3.46667vw; }

.railway12306-blue-fliggy-search-body .traffic-search-tabbar { margin: 0px 3.2vw; padding-top: 4.8vw; background-color: rgb(255, 255, 255); }

.traffic-search-show { display: block; }

.traffic-search-hide { display: none; }

.radio-wrapper { width: 5.33333vw; height: 5.33333vw; border-radius: 2.66667vw; box-sizing: border-box; border: 0.4vw solid rgb(235, 237, 240); background-color: rgb(255, 255, 255); position: relative; flex-shrink: 0; display: flex; flex-direction: row; align-items: center; justify-content: center; }

.home-main-kingkong .home-main-kingkong-search-item { display: none; padding-top: 1.6vw; }

.home-main-kingkong .home-main-kingkong-search-item-active { display: flex; }

.minit-cs-ts { height: 8vw; width: 100%; }

.minit-cs-ts__text { width: 100%; line-height: 8vw; font-weight: 400; font-size: 4vw; text-align: left; color: rgb(145, 148, 153); overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block; }

.tsc2mini__cs { flex-direction: row; align-items: center; }

.tsc2mini__cs-name { font-weight: 500; font-size: 5.6vw; line-height: 7.73333vw; text-align: center; color: rgb(15, 19, 26); max-width: 40vw; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block; }

.tsc2mini__cs-icon { color: rgb(15, 19, 26); margin-left: 1.6vw; }

.minit__scb { position: relative; margin-left: 4vw; margin-right: 4vw; border-radius: 5.6vw; justify-content: center; align-items: center; padding-top: 2.8vw; padding-bottom: 2.8vw; background-color: rgb(255, 224, 51); }

.minit__scb--text { font-weight: 500; font-size: 4vw; line-height: 5.6vw; letter-spacing: 0px; color: rgb(15, 19, 26); }

.minit__cs { flex-direction: row; align-items: center; margin-left: 4vw; margin-right: 4vw; margin-bottom: 3.33333vw; }

.minit__cs-search { flex: 1 1 0%; margin-left: 8vw !important; }

.minit__sc { width: 100%; padding-bottom: 3.2vw; }

.minit__tabs { position: relative; flex-direction: row; align-items: center; justify-content: space-between; margin: 0px 1.6vw 4vw; }

.minit__tabs--item { text-align: center; width: 24.8vw; padding: 4vw 0px 2.4vw; position: relative; }

.minit__tabs--item-text { text-align: center; font-weight: 400; color: rgb(15, 19, 26); font-size: 4vw; line-height: 4vw; }

.minit__tabs--slider { width: 24.8vw; height: 0.8vw; position: absolute; bottom: 0px; left: 0px; transition: left 0.2s ease 0s; }

.minit__tabs--slider-inner { position: absolute; left: 50%; bottom: 0px; transform: translateX(-50%); width: 7.2vw; height: 0.8vw; border-radius: 0.4vw; background-color: rgb(15, 19, 26); }

.TicketSearchCard2mini-wrapper { background-color: rgb(255, 255, 255); border-bottom-left-radius: 3.2vw; border-bottom-right-radius: 3.2vw; overflow: hidden; }

.rent-car-skeleton-box { height: 66.6667vw; align-items: center; background-color: rgb(255, 255, 255); }

.rent-car-skeleton-switch { flex-direction: row; align-items: center; justify-content: center; margin: 3.2vw 0px; }

.switch-one-item { margin-right: 2.13333vw; }

.rent-car-switch-skeleton-item { width: 26.6667vw; height: 8.53333vw; background: linear-gradient(90deg, rgb(246, 246, 246), rgb(239, 239, 239)); border-radius: 3.2vw; }

.rent-car-skeleton-od-search { flex-direction: row; margin-left: 5.33333vw; margin-top: 1.6vw; align-items: center; }

.rent-car-skeleton-city { width: 33.3333vw; margin-right: 1.6vw; }

.rent-car-skeleton-address, .home .rent-car-skeleton-city { height: 10.6667vw; background: linear-gradient(90deg, rgb(246, 246, 246), rgb(239, 239, 239)); border-radius: 1.6vw; }

.rent-car-skeleton-address { width: 40vw; margin-right: 5.33333vw; }

.rent-car-skeleton-radio { width: 10.6667vw; height: 5.33333vw; background: linear-gradient(90deg, rgb(246, 246, 246), rgb(239, 239, 239)); border-radius: 1.6vw; }

.rent-car-skeleton-swiper { width: 74.6667vw; height: 2.66667vw; }

.rent-car-skeleton-button, .home .rent-car-skeleton-swiper { margin-top: 2.66667vw; background: linear-gradient(90deg, rgb(246, 246, 246), rgb(239, 239, 239)); border-radius: 5.33333vw; }

.rent-car-skeleton-button { justify-content: center; width: 66.6667vw; height: 10.6667vw; }

.home-main-kingkong .home-main-kingkong-search-item { display: none; padding-top: 1.6vw; }

.home-main-kingkong .home-main-kingkong-search-item-active { display: flex; }

.home-main-kingkong .home-main-kingkong-search-item { display: none; padding-top: 1.6vw; }

.home-main-kingkong .home-main-kingkong-search-item-active { display: flex; }

.osc2mini__tabs { position: relative; flex-direction: row; align-items: center; justify-content: space-between; margin: 0px 1.6vw 4vw; }

.osc2mini__tabs--item { display: flex; font-size: 4vw; line-height: 4vw; color: rgb(15, 19, 26); padding: 4vw 0px 2.4vw; text-align: center; font-weight: 400; }

.osc2mini__tabs--slider { height: 0.8vw; position: absolute; bottom: 0px; left: 0px; transition: left 0.2s ease 0s; }

.osc2mini__tabs--slider-inner { position: absolute; left: 50%; bottom: 0px; transform: translateX(-50%); width: 7.2vw; height: 0.8vw; border-radius: 0.4vw; background-color: rgb(15, 19, 26); }

.osc2mini__cp { flex-direction: row; align-items: center; justify-content: space-between; }

.osc2mini__cp, .osc2mini__cp--text { width: fit-content; }

.osc2mini__cp--text { display: inline-block; font-size: 5.6vw; line-height: 7.73333vw; color: rgb(145, 148, 153); font-weight: 500; margin-right: 1.6vw; max-width: 34.6667vw; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

.osc2mini__button { align-items: center; }

.osc2mini__button--button { position: relative; width: 100%; margin-bottom: 3.2vw; }

.osc2mini__button--jump { position: relative; width: 100%; height: 11.2vw; background-color: rgb(255, 224, 51); border-radius: 5.6vw; color: rgb(15, 19, 26); font-size: 4vw; font-weight: 500; text-align: center; line-height: 11.2vw; }

.osc2mini--header { flex-direction: row; align-items: center; justify-content: flex-start; margin-bottom: 4vw; }

.osc2mini__visa { background-color: rgb(255, 255, 255); border-radius: 2.66667vw; }

.osc2mini__sc { padding: 0px 4vw 4vw; background-color: rgb(255, 255, 255); }

.OverseasSearchCard2mini-wrapper { background-color: rgb(255, 255, 255); border-bottom-left-radius: 3.2vw; border-bottom-right-radius: 3.2vw; overflow: hidden; }
`;


export const BIZ_CARD_HTML_MAP = {
  hotel: hotelHtml,
  flight: flight,
  train: trainHtml,
  rentalCar: rentalCarHtml,
  ticket: ticketHtml,
  overseas: overseasHtml,
};

export const TRANSLATE_MAP = {
  hotel: '-2.4vw',
  flight: '14.2667vw',
  train: '30.6667vw',
  ticket: '47.0667vw',
  rentalCar: '63.2667vw',
  overseas: '80.53vw'
};

export const KINGKONG_MAP = {
  normal: {
    bgImg: 'https://gw.alicdn.com/imgextra/i3/O1CN01HpsnCf1Id7SWF191Y_!!6000000000915-2-tps-1460-420.png',
    orderList: [
      {
        title: '酒店',
        id: 'hotel',
        bgPosition: '0% 15%',
        bgActivePosition: '0% 100%',
      },
      {
        title: '机票',
        id: 'flight',
        bgPosition: '16.67% 15%',
        bgActivePosition: '16.67% 100%',

      },
      {
        title: '火车/汽车',
        id: 'train',
        bgPosition: '33.33% 15%',
        bgActivePosition: '33.33% 100%',
      },
      {
        title: '门票/跟团',
        id: 'ticket',
        bgPosition: '66.67% 15%',
        bgActivePosition: '66.67% 100%',
      },
      {
        title: '租车/接送',
        id: 'rentalCar',
        bgPosition: '50% 15%',
        bgActivePosition: '50% 100%',
      },
      {
        title: '签证/通讯',
        id: 'overseas',
        bgPosition: '100% 15%',
        bgActivePosition: '100% 100%',
      },
    ],
  },
  overseas: {
    bgImg: 'https://gw.alicdn.com/imgextra/i3/O1CN018RwnCB1U46Y3ACZNS_!!6000000002463-2-tps-1250-420.png',
    orderList: [
      {
        title: '酒店',
        id: 'hotel',
        bgPosition: '0% 15%',
        bgActivePosition: '0% 100%',
      },
      {
        title: '机票',
        id: 'flight',
        bgPosition: '20% 15%',
        bgActivePosition: '20% 100%',
      },
      {
        title: '门票',
        id: 'ticket',
        bgPosition: '80% 15%',
        bgActivePosition: '80% 100%',
      },
      {
        title: '一日游',
        id: 'yiriyou',
        bgPosition: '40% 15%',
        bgActivePosition: '40% 100%',
      },
      {
        title: '租车/接送',
        id: 'rentalCar',
        bgPosition: '60% 15%',
        bgActivePosition: '60% 100%',
      },
      {
        title: '境外上网',
        id: 'visawifi',
        bgPosition: '100% 15%',
        bgActivePosition: '100% 100%',
      },
    ],
  },
}
