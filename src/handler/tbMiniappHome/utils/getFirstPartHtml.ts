import { BIZ_CARD_HTML_MAP, TRANSLATE_MAP, KINGKONG_MAP, bizCSS } from '../constants/bizCardHtml';
import { BRAND_BANNER, BANGDAN_MODULE, ACTIVITY_CYCLE_MODULE, ACTIVITY_COUPON_MODULE, ACTIVITY_CROWD_MODULE, DISCOUNT_MODULE, CHOICENESS_MODULE } from '../constants/dynamicHtml';
import { guesslikeHtml } from '../constants/guesslikeHtml';

// 获取指定天数的日期信息
function getDateInfo(dateObj, days = 0) {
  const date = new Date(dateObj);
  date.setDate(date.getDate() + days);

  return {
    month: date.getMonth() + 1,
    day: date.getDate(),
  };
}

export function getHeadPartHtml(opt) {
  const { version, pathname, isPre } = opt;

  const isOverseas = pathname.indexOf('overseas') > -1;

  return `<!DOCTYPE html><html>
<head>
  <meta name='data-spm' content='181.7474825'>
  <script fixed='true'>window.trackerPerformanceTime = {};window.trackerPerformanceTime.trip_startTime = Date.now();</script>
  <meta name='alitrip-project-version' content='${version || ''}'>
  <meta name='alitrip-project-name' content='rx-miniapp-home'>
  <meta name='aplus-version' content='aplus@202450834'>
  <meta charset='utf-8'>
  <title>飞猪旅行 - 首页</title>
  <script>window._$isOffline$_ = false;</script>
  <meta name='aplus-terminal' content='1'>
  <meta name='aplus-waiting' content='MAN'>
  <meta name='aplus-spm-fixed' content='1'>
  <meta name='weex-viewport' content='750'>
  <meta name='referrer' content='no-referrer-when-downgrade'>
  <meta name='viewport' content='width=device-width,initial-scale=1,user-scalable=no,viewport-fit=cover'>
  <meta name='page-name' content='飞猪旅行 - 首页'>
  <meta name='apple-mobile-web-app-capable' content='yes'>
  <meta name='apple-mobile-web-app-status-bar-style' content='black'>
  <meta name='apple-touch-fullscreen' content='yes'>
  <meta name='format-detection' content='telephone=no, email=no'>
  <link rel='dns-prefetch' href='//g.alicdn.com'>
  <link rel='dns-prefetch' href='//img.alicdn.com'>
  <link rel='dns-prefetch' href='//gw.alicdn.com'>
  <link rel='dns-prefetch' href='//log.mmstat.com'>
  <link rel='dns-prefetch' href='//api.m.taobao.com'>
  <link rel='dns-prefetch' href='//api.m.alitrip.com'>
  <link rel='dns-prefetch' href='//wgo.mmstat.com'>
  <style type='text/css' id='html-common-style'>
    @font-face {
      font-family: 'fliggy-home-iconfont';
      src: url('data:application/x-font-ttf;charset=utf-8;base64,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');
    }
    html, body {
      -ms-overflow-style: scrollbar;
      -webkit-tap-highlight-color: transparent;
      padding: 0;
      margin: 0;
      width: 100%;
      height: 100%;
    }
    body {
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      flex-direction: column;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: 0;
      font-family: BlinkMacSystemFont, 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    }
    input[type='search']::-webkit-search-decoration, input[type='search']::-webkit-search-cancel-button {
      -webkit-appearance: none !important;
    }
    body [web-sticky] {
      position: -webkit-sticky !important;
      position: sticky !important;
      user-select: none;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
    }
    img {
      user-select: none;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
    }
    .rax-view-v2 {
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      display: -webkit-flex;
      display: -moz-box;
      display: flex;
      -webkit-flex-direction: column;
      -moz-box-orient: vertical;
      -moz-box-direction: normal;
      flex-direction: column;
      -webkit-flex-shrink: 0;
      flex-shrink: 0;
      -webkit-align-content: flex-start;
      align-content: flex-start;
      border: 0 solid #000;
      margin: 0;
      padding: 0;
      min-width: 0
    }
    .rax-text {
      box-sizing: border-box;
      display: block;
      font-size: 4.26667vw;
      white-space: pre-wrap;
    }
    .rax-text-v2 {
      box-sizing: border-box;
      display: block;
      font-size: 4.26667vw;
      white-space: pre-wrap;
    }
    .rax-text-v2--overflow-hidden {
      overflow: hidden;
    }
    .rax-text-v2--singleline {
      white-space: nowrap;
    }
    .rax-text-v2--multiline {
      display: -webkit-box;
      -webkit-box-orient: vertical;
    }
    .rax-scrollview::-webkit-scrollbar{
      display: none;
    }
    .rax-scrollview-vertical {
      -webkit-flex-direction: column;
      flex-direction: column;
    }
    .rax-scrollview-horizontal {
      -webkit-flex-direction: row;
      flex-direction: row;
    }
    .rax-scrollview-content-container-horizontal {
      -webkit-flex-direction: row;
      flex-direction: row;
    }
    .rax-scrollview-webcontainer {
      display: block
    }
  </style>
  <style type="text/css">
    .er-holder-home-titlebar {
      z-index: 2000;
      width: 100vw;
      position: sticky;
      top: 0;
      box-sizing: content-box;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
    }
    .htb-transparent {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1;
    }
    .htb-default {
      height: 37.33333vw;
      left: 0;
      right: 0;
      bottom: 0;

      z-index: 0;
      position: absolute;
      background: linear-gradient(0deg, #ffec85, #ffe033 23.86667vw);
    }
    .ha-bottom {
      position: absolute;
      width: 100vw;
      height: auto;
      z-index: 0;
    }
    .hab-default {
      height: 120vw;
      background: linear-gradient(180deg, #ffec85, rgba(255, 236, 133, 0));
    }
    .er-holder-home-content-lay {
      width: 100%;
      position: relative;
      display: flex;
      align-items: center;
      background: linear-gradient(180deg, rgba(242, 243, 245, 0) 26.66667vw, #f2f3f5 51.46667vw);
      transition: border-radius .25s linear;
    }
    .ht-search {
      position: relative;
      width: 100vw;
      top: 0;
      z-index: 2000;
      opacity: 1;
    }
    .hts-content {
      font-size: 0;
      width: 92vw;
      height: 8.53333vw;
      display: flex;
      overflow: hidden;
      background: #fff;
      position: relative;
      flex-direction: row;
      align-items: center;
      margin: 1.6vw auto;
      border-radius: 4.26667vw;
      box-sizing: border-box;
      border: .4vw solid #fff;
      justify-content: space-between;
    }
    .hts-tips {
      width: 100%;
      height: 8.53333vw;
    }
    .htst-item {
      height: 8.53333vw;
      color: #919499;
      font-size: 3.2vw;
      line-height: 8.53333vw;
      padding-left: 7.46667vw;
      box-sizing: border-box;
      overflow: hidden;
    }
    .hts-button {
      z-index: 2;
      right: 0.8vw;
      width: 12.8vw;
      height: 6.4vw;
      color: #fff;
      padding-top: 0.13333vw;
      font-size: 3.2vw;
      font-weight: 500;
      position: absolute;
      line-height: 6.4vw;
      text-align: center;
      pointer-events: none;
      background: #66f;
      border-radius: 3.2vw;
    }

    .er-holder-home-minor-kingkong {
      height: 7.2vw;
      width: 100vw;
      margin-bottom: 2.4vw;
      padding: 0 4vw;
      display: flex;
      flex-direction: row;
      z-index: 0;
    }

    .hmk-item {
      height: 7.2vw;
      padding: 0 1.6vw;
      margin-right: 1.6vw;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      border-radius: 3.6vw;
      background: hsla(0, 0%, 100%, .48);
    }
    .hmki-image {
      height: 4vw;
      width: 4vw;
      margin-right: 1.33333vw;
    }
    .hmki-text {
      font-size: 3.2vw;
      color: rgba(15, 19, 26, .6);
    }
    .er-holder-home-main-kingkong {
      margin-bottom: 3.2vw;
      width: 100vw;
      position: relative;
      border-radius: 3.2vw;
      overflow: hidden;
    }
    .hmkt-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100vw;
      height: 18.13333vw;
      border-radius: 3.2vw 3.2vw 0 0;
      z-index: 0;
      position: relative;
    }
    .hakt-bg {
      align-items: center;
      display: flex;
      height: 18.13333vw;
      left: 0;
      position: absolute;
      top: 0;
      width: 21.86667vw;
      z-index: 1;
    }
    .haktbg-image {
      left: 50%;
      position: relative;
      transform: translateX(-50%);
    }
    .hmkt-item {
      flex: 1;
      width: auto;
      height: 18.4vw;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-bottom: 2.4vw;
      z-index: 9;
      margin-left: -1.66667vw;
    }
    .hmktii-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
    }
    .hmktiic-wrap {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      top: .8vw;
    }
    .hmktiic-img-box {
      background-color: #fff;
      border: .53333vw solid #fff;
      border-radius: 50%;
      box-sizing: content-box;
      margin-top: .8vw;
      overflow: hidden;
      transform: translateY(0);
    }
    .hmktiicw-img {
      width: 13.3333vw;
      height: 9.6vw;
    }
    .hmktiicw-active-img {
      width: 9.6vw;
    }
    .hmkti-title {
      color: #0f131a;
      font-size: 3.2vw;
      line-height: 3.73333vw;
    }
    .hmkti-title-active {
      font-weight: 500;
    }
    .hmktd-container {
      width: 100%;
      margin-bottom: -6.13333vw;
      background: #fff;
      z-index: 0;
      border-top-left-radius: 3.2vw;
      border-top-right-radius: 3.2vw;
    }
    .hmktd-shadow {
      width: 100%;
      height: 6.4vw;
      border-radius: 3.2vw;
    }
    .hmks-area {
      position: relative;
      border-radius: 3.2vw;
    }
    .hmksi-active {
      display: flex;
      padding-top: 1.6vw;
    }
    .er-holder-discount-container {
      width: 92vw;
      margin: 0 auto 2.4vw;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .dc-item {
      width: 29.6vw;
      height: 28.53333vw;
      border-radius: 1.6vw;
      background: white;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0 0.8vw;
    }
    .dci-title {
      width: 16.8vw;
      height: 4vw;
      object-fit: contain;
      margin: 1.6vw 0 0 0.8vw;
    }
    .dci-content {
      width: 100%;
      height: 20.53333vw;
      border-radius: 1.6vw;
      overflow: hidden;
      margin-top: 1.6vw;
      position: relative;
    }
    .dcic-img {
      width: 28vw;
      height: 20.5333vw;
      object-fit: cover;
    }
    .dci-subtitle {
      padding: 1.6vw 1.6vw 0;
      height: 2.93333vw;
      width: 100%;
      font-size: 2.93333vw;
      font-weight: 600;
      line-height: 100%;
      letter-spacing: 0;
      color: #0f131a;
    }
    .er-holder-choiceness-container {
      width: 92vw;
      max-height: 91.46667vw;
      border-radius: 1.6vw;
      opacity: 1;
      display: flex;
      flex-direction: column;
      background: #fff;
      margin: 0 auto 2.4vw;
      overflow: hidden;
    }
    .cc-top {
      position: relative;
      height: 11.73333vw;
      font-size: 4.8vw;
      font-weight: 500;
      line-height: 5.33333vw;
      letter-spacing: 0;
      color: #0f131a;
    }
    .cc-bottom {
      max-height: 79.73333vw;
      width: 92vw;
      overflow: hidden;
      position: relative;
    }
    .ccb-tab {
      width: 92vw;
      height: 9.06667vw;
      padding: 0 .8vw 1.6vw;
      display: flex;
      flex-direction: row;
      justify-content: left;
      overflow: hidden;
    }
    .ccbt-it {
      width: 21vw;
      height: 7.46667vw;
      margin-left: .8vw;
      margin-right: .8vw;
      border-radius: 1.6vw;
      color: #0f131a;
      font-size: 3.46667vw;
      font-weight: 500;
      line-height: 4vw;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 2.26667vw .66667vw;
      background: rgba(0, 0, 0, .03);
    }
    .ccbt-bg {
      flex-direction: row;
      height: 9.06667vw;
      position: relative;
    }
    .ccbt-bg-left {
      height: 100%;
      width: .8vw;
    }
    .ccbt-bg-left-inner {
      background: #fff;
      border-radius: 0 0 4vw 0;
      height: 100%;
      width: .8vw;
    }
    .ccbt-bg-inner {
      border-radius: 1.6vw 1.6vw 0 0;
      height: 100%;
    }
    .ccbt-bg-right {
      height: 100%;
      width: .8vw;
    }
    .ccbt-bg-right-inner {
      background: #fff;
      border-radius: 0 0 0 4vw;
      height: 100%;
      width: .8vw;
    }
    .cb-content {
      width: 92vw;
      max-height: 70.66667vw;
      padding-bottom: 18rpx;
    }
    .cbc-info {
      position: relative;
      width: 85.6vw;
      height: 10.4vw;
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      padding: 0;
      margin-left: 3.2vw;
      margin-right: 3.2vw;
      margin-top: 3.46667vw;
    }
    .cbci-left {
      width: 63.6vw;
      min-height: 5.2vw;
      max-height: 10.4vw;
      opacity: 1;
      font-size: 3.46667vw;
      font-weight: 400;
      line-height: 150%;
      letter-spacing: 0;
      color: #00416c;
    }
    .cbci-right {
      width: 17.6vw;
      height: 4.8vw;
      font-size: 3.2vw;
      font-weight: 500;
      line-height: 150%;
      border-radius: 4vw;
      color: #fff;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }
    .cbc-cards {
      margin-top: 3.2vw;
      width: 92vw;
      display: flex;
      flex-direction: row;
      padding: 0 2.4vw 2.4vw;
      gap: 1.6vw;
    }
    .cbcc-item {
      width: 28vw;
      height: 40vw;
      border-radius: 1.6vw;
      overflow: hidden;
    }
    .cbc-top {
      position: relative;
      width: 28vw;
      height: 22.93333vw;
    }
    .cbcc-img {
      width: 28vw;
      height: 22.93333vw;
    }
    .cbc-bottom {
      padding: 0 1.6vw;
      width: 28vw;
    }
    .cbcb-title {
      width: 24.8vw;
      height: 8vw;
      font-size: 3.46667vw;
      font-weight: 500;
      line-height: 4.26667vw;
      color: #0f131a;
      margin-top: 1.6vw;
      overflow: hidden;
    }
    .cbcb-subtitle {
      margin-top: 1.6vw;
      width: 24.8vw;
      height: 3.73333vw;
      font-size: 3.2vw;
      font-weight: 600;
      line-height: 3.73333vw;
      color: #0f131a;
    }
    .biz-card-content-wrap {
      background: #fff;
      border-radius: 0 0 3.2vw 3.2vw;
      margin-top: 1.6vw;
      overflow: hidden;
      padding-bottom: 2.8vw;
      width: 100vw
    }

     ${bizCSS}
   
    @font-face {
      font-family: AlibabaSans102-Bd;
      font-weight: 700;
      src: url(https://g.alicdn.com/trip/common-assets/1.0.0/fonts/AlibabaSans102-Bd.ttf) format("truetype")
    }
    @font-face {
      font-family: FliggySans102-Bd;
      font-weight: 700;
      src: url(https://g.alicdn.com/trip/common-assets/1.0.2/fonts/FliggySans102-Bd.ttf) format("truetype")
    }

    .mind-block-skeleton {
      flex-direction: row;
      margin-bottom: 3.2vw;
      overflow-x: scroll;
      overflow-y: hidden;
      white-space: nowrap;
      width: 100vw;
      padding: 0 4vw;
    }

    .mind-block-skeleton::-webkit-scrollbar {
      display: none;
    }

    .mind-block-item-skeleton {
      width: 36vw;
      height: 47.4667vw;
      overflow: hidden;
      border-radius: 1.6vw;
    }

    /* 骨架屏动画效果 */
    @keyframes skeleton-shine {
      0% {
        background-position: -200% 0;
      }
      100% {
        background-position: 200% 0;
      }
    }

    /* 只为灰色背景的骨架元素添加闪光效果 */
    [class$="-skeleton"][style*="background-color: #e4e5e7"],
    [class$="-skeleton"][style*="backgroundColor: #e4e5e7"],
    [class$="-skeleton"][style*="background-color: #F5F5F5"],
    [class$="-skeleton"][style*="backgroundColor: #F5F5F5"],
    .item-card-image-skeleton,
    .item-card-top-skeleton,
    .item-card-tag-skeleton,
    .hotel-item-name-skeleton,
    .flight-route-skeleton,
    .coupon-block-coupon-text-skeleton,
    .flight-price-skeleton {
      background: linear-gradient(90deg, #e4e5e7 25%, #f5f5f5 37%, #e4e5e7 63%);
      background-size: 400% 100%;
      animation: skeleton-shine 3s ease infinite;
    }

    .flight-container-skeleton {
      border-radius: 1.6vw;
      /* background: linear-gradient(to bottom, rgba(77, 166, 255, 0.8), rgba(77, 166, 255, 1)); */
      background: #fff;
      padding: 1.6vw;
      display: flex;
      flex-direction: column;
    }

    .flight-header-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 0.8vw;
      width: 32.8vw;
      height: 3.7333vw;
      margin-bottom: 2.4vw;
    }

    .flight-icon-wrapper-skeleton {
      width: 3.7333vw;
      height: 3.7333vw;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .flight-icon-skeleton {
      width: 100%;
      height: 100%;
      box-shadow: inset -0.2667vw -0.1333vw 0.1333vw 0 rgba(0, 0, 0, 0.1);
    }

    .flight-arrow-icon-skeleton {
      color: #FFFFFF;
      width: 1.6vw;
      height: 1.6vw;
    }

    .flight-arrow-skeleton {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .flight-content-skeleton {
      background-color: #FFFFFF;
      border-radius: 0.8vw;
      display: flex;
      flex-direction: column;
      gap: 1.3333vw;
      width: 100%;
    }

    .flight-item-skeleton {
      display: flex;
      flex-direction: column;
      width: 100%;
      background: linear-gradient(270deg, rgba(255, 251, 227, 0) 0%, #FFFBE3 100%);
      border-radius: 0.8vw;
      overflow: hidden;
      height: 11.7333vw;
      padding: 1.0667vw 1.6vw;
    }

    .flight-route-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 0.8vw;
    }

    .flight-city-skeleton {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 3.2vw;
      line-height: 1.4em;
      color: #0F131A;
    }

    .flight-type-skeleton {
      background-color: #F2F3F5;
      border-radius: 2.6667vw;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .flight-type-icon-skeleton {
      width: 2.1333vw;
      height: 2.1333vw;
      background-color: #0F131A;
      border-radius: 0.2vw;
      margin: 0.5333vw;
    }

    .flight-price-container-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .flight-price-skeleton {
      display: flex;
      flex-direction: row;
      align-items: baseline;
      gap: 0.2667vw;
    }

    .flight-price-symbol-skeleton,
    .flight-price-unit-skeleton {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 2.6667vw;
      line-height: 1.4em;
      color: #FF5533;
    }

    .flight-price-value-skeleton {
      font-family: AlibabaSans102-Bd;
      font-weight: 500;
      font-size: 4vw;
      line-height: 1.4em;
      color: #FF5533;
    }

    .flight-tag-skeleton {
      background-color: #FFE033;
      border-radius: 0.5333vw;
      padding: 1.3333vw 0.8vw;
      height: 3.7333vw;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 1.0667vw;
    }

    .flight-tag-text-skeleton {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 2.6667vw;
      line-height: 1.4em;
      color: #0F131A;
    }

    .hotel-container-skeleton {
      display: flex;
      flex-direction: column;
      padding: 1.6vw;
      position: relative;
      border-radius: 1.6vw;
      background-color: #FFFFFF;
      /* 渐变背景 */
      background: radial-gradient(circle at 57% 24%, #F5F5FF 0%, #FFFFFF 52%);
    }

    /* 顶部标题栏 */
    .hotel-header-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 2.4vw;
      width: 32.8vw;
      height: 3.7333vw;
    }

    .hotel-icon-wrapper-skeleton {
      width: 3.7333vw;
      height: 3.7333vw;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .hotel-icon-group-skeleton {
      position: relative;
      width: 4vw;
      height: 4vw;
    }

    .hotel-icon-1-skeleton {
      position: absolute;
      width: 3.7333vw;
      height: 3.7333vw;
      border-radius: 0.5333vw;
      background-color: #CCCCFF;
      z-index: 1;
    }

    .hotel-icon-2-skeleton {
      position: absolute;
      width: 3.7333vw;
      height: 3.7333vw;
      border-radius: 0.5333vw;
      background-color: #6666FF;
      box-shadow: inset -0.2667vw -0.1333vw 0.1333vw 0vw rgba(0, 0, 0, 0.12);
      left: 0.2667vw;
      top: 0.2667vw;
      z-index: 2;
    }

    .hotel-icon-circle-skeleton {
      position: absolute;
      width: 1.0667vw;
      height: 1.0667vw;
      border-radius: 50%;
      background-color: #FFFFFF;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 3;
    }

    .hotel-arrow-skeleton {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .hotel-arrow-icon-skeleton {
      width: 3.7333vw;
      height: 3.7333vw;
    }

    /* 酒店列表 */
    .hotel-list-skeleton {
      display: flex;
      flex-direction: column;
      width: 100%;
      gap: 1.3333vw;
    }

    .hotel-item-skeleton {
      display: flex;
      flex-direction: row;
      width: 100%;
      gap: 1.0667vw;
    }

    .hotel-item-image-skeleton {
      width: 16.5333vw;
      height: 100%;
      border-radius: 0.8vw;
      background-size: cover;
      background-position: center;
    }

    .hotel-item-content-skeleton {
      display: flex;
      flex-direction: column;
      flex: 1;
      gap: 0.2667vw;
      padding: 0.2667vw 0vw;
    }

    .hotel-item-info-skeleton {
      display: flex;
      flex-direction: column;
      gap: 0.5333vw;
    }

    .hotel-item-name-skeleton {
      font-size: 3.2vw;
      font-weight: 500;
      line-height: 1.4em;
      color: #0F131A;
      width: 100%;
    }

    .item-container-skeleton {
      display: flex;
      flex-direction: column;
      gap: 2.4vw;
      padding: 1.6vw;
      position: relative;
      border-radius: 1.6vw;
      background-color: #FFFFFF;
      height: 47.4667vw;
      /* 渐变背景 */
      background: radial-gradient(circle at 57% 24%, #F5F5FF 0%, #FFFFFF 52%);
    }

    /* 顶部标题栏 */
    .item-header-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 0.8vw;
      width: 32.8vw;
      height: 3.7333vw;
    }

    .item-fire-icon-skeleton {
      font-size: 3.7333vw;
      font-weight: 700;
      line-height: 1em;
      background: linear-gradient(to right, #23006E, #0F131A);
      -webkit-background-clip: text;
      color: transparent;
    }

    .item-arrow-skeleton {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .item-icon-wrapper-skeleton {
      width: 3.7333vw;
      height: 3.7333vw;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .item-arrow-icon-skeleton {
      width: 3.7333vw;
      height: 3.7333vw;
    }

    /* 景点卡片 */
    .item-card-skeleton {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;
      width: 32.8vw;
      height: 38.1333vw;
    }

    .item-card-image-skeleton {
      width: 100%;
      height: 16.5333vw;
      border-radius: 0.8vw;
      /* 渐变阴影遮罩 */
      position: relative;
      margin-bottom: 1.6vw;
    }

    /* .item-card-image-skeleton::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 50%;
      background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,1));
      border-radius: 0 0 6rpx 6rpx;
    } */

    .item-card-content-skeleton {
      display: flex;
      flex-direction: column;
      /* gap: 6rpx; */
      width: 100%;
    }

    .item-card-top-skeleton {
      width: 32.8vw;
      height: 4.53vw;
      background-color: #F5F5F5;
      margin-bottom: 1.0667vw;
    }


    .item-card-tag-skeleton {
      width: 32.8vw;
      height: 4.53vw;
      background-color: #F5F5F5;
      margin-bottom: 1.0667vw;
    }

    .item-card-hotel-tag-skeleton {
      width: 13.3vw;
      height: 4.53vw;
      background-color: #fff2f0;
    }

    .item-card-tag-text-skeleton {
      font-size: 2.6667vw;
      color: #5C5F66;
      background: #f2f3f5;
      border-radius: 0.8vw;
      padding: 0 1.0667vw;
      margin-left: 1.0667vw;
      /* 让tag不换行 */
      vertical-align: middle;
      display: inline-flex;
    }

    .item-card-reason-skeleton {
      font-size: 3.2vw;
      line-height: 1.4em;
      color: #5C5F66;
      width: 32.8vw;
        -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      word-break: break-all;
      /* 关键样式 */

      /* text-overflow: ellipsis; */
    }

    .item-card-reason-container-skeleton {
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      word-break: break-all;
    }

    .item-card-price-skeleton {
      font-size: 3.2vw;
      font-weight: 500;
      line-height: 1.4em;
      color: #FF5533;
      align-self: flex-start;
    }

    /* 榜单标签 */
    .item-badge-skeleton {
      position: absolute;
      top: 0;
      left: 5.3333vw;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0.2667vw 0.8vw;
      background-color: #805540;
      border-radius: 0vw 0vw 0.5333vw 0vw;
      z-index: 99;
    }

    .item-badge-text-skeleton {
      font-size: 2.6667vw;
      font-weight: 500;
      line-height: 1.4em;
      color: #FFFFFF;
      text-align: center;
    }

    /* TOP标签 */
    .item-top-skeleton {
      position: absolute;
      top: -0.8vw;
      left: 0;
      width: 6.624vw;
      height: 6.6947vw;
    }

    .item-top-bg-skeleton {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .item-top-inner-skeleton {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 2;
    }

    .item-top-text-container-skeleton {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 3;
    }

    .item-top-text-bg-skeleton {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    .item-top-text-skeleton {
      font-size: 2vw;
      font-weight: 700;
      line-height: 1em;
      color: transparent;
      background: linear-gradient(to right, #6E3500, #0F131A);
      -webkit-background-clip: text;
    }

    .item-card-price-symbol-skeleton {
      font-size: 3.2vw;
      line-height: 1.4em;
      color: #FF5533;
      font-family: "AlibabaSans102-Bd";
    }

    .item-card-price-number-skeleton {
      font-size: 4.2667vw;
      font-weight: 500;
      line-height: 1.4em;
      color: #FF5533;
      font-family: "AlibabaSans102-Bd";
    }

    .item-card-price-unit-skeleton {
      font-size: 3.2vw;
      font-weight: 400;
      line-height: 1.4em;
      color: #FF5533;
    }

    .item-card-price-text-skeleton {
      display: inline;
    }

    .item-card-price-container-skeleton {
      flex-direction: row;
      align-items: baseLine;
      position: absolute;
      left: 0;
      bottom: 0;
    }


    .item-title-container-skeleton {
      width: 300px;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .item-title-name-skeleton {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      word-break: break-all;
    }

    .activity-block-skeleton {
      flex-direction: row;
      margin-bottom: 3.2vw;
      overflow-x: scroll;
      overflow-y: hidden;
      white-space: nowrap;
      width: 100vw;
      padding: 0 4vw;
    }

    .activity-block-skeleton::-webkit-scrollbar {
      display: none;
    }

    .activity-block-item-skeleton {
      height: 26.4vw;
      border-radius: 3.2vw;
      overflow: hidden;
      background-color: #fff;
    }

    .cycle-block-skeleton {
      width: 100%;
      height: 100%;
      padding: 1.8667vw 2.4vw 3.4667vw;
    }

    .cycle-block-head-skeleton {
      justify-content: space-between;
    }

    .cycle-block-title-skeleton {
      background: url('https://gw.alicdn.com/imgextra/i4/O1CN012nmifz1GXxlHI8fBj_!!6000000000633-2-tps-409-60.png') no-repeat;
      background-size: 54.5333vw 8vw;
      width: 14.5333vw;
      height: 4.1333vw;
      background-position: -14.4vw 0;
    }

    .cycle-block-title-content-skeleton {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .cycle-block-title-text-skeleton {
      color: #5C5F66;
    }

    .cycle-title-btn-text-gray-skeleton {
      color: #919499;
    }

    .cycle-title-btn-skeleton {
      justify-content: flex-end;
      min-width: 14.4vw;
      height: 6.4vw;
      border-radius: 7.7333vw;
      padding: 0 2.1333vw;
      background: linear-gradient(226.32deg, #FF4E13 27.89%, #FF5613 44.39%, #FF9C24 84.51%, #FFFFFF 115.75%);
    }

    .cycle-title-btn-text-skeleton {
      font-size: 3.2vw;
      line-height: 140%;
      /* color: #FFFFFF; */
      font-weight: 500;
    }

    .cycle-block-title-common-skeleton {
      flex-shrink: 0;
      white-space: nowrap;
      font-size: 2.9333vw;
      line-height: 140%;
    }

    .cycle-block-title-common-skeleton:last-child {
      flex-shrink: 1;          /* 允许最后一个span被压缩 */
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
    }

    .cycle-block-progress-skeleton {
      height: 1.0667vw;
      background: #F2F3F5;
      margin: 3.7333vw auto 0;
      overflow: hidden;
    }

    .crowd-block-skeleton {
      width: 100%;
      height: 100%;
      padding: 2.1667vw 1.8667vw 1.6vw;
      background: no-repeat;
      background-size: cover;
    }

    .crowd-block-header-skeleton {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: 5.4667vw;
    }

    .crowd-block-tips-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .crowd-block-content-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 1.3333vw;
      margin-top: 2.0333vw;
      height: 15.2vw;
    }

    .crowd-block-subtitle-text-skeleton {
      font-size: 3.2vw;
      line-height: 140%;
      color: #5C5F66;
    }

    .crowd-block-item-skeleton {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 37.47vw;
      height: 15.2vw;
      background: linear-gradient(133.62deg, rgba(255, 247, 204, 0.48) 0%, rgba(255, 247, 204, 0.8) 100.16%);
      border-radius: 1.6vw;
      border: 0.1053vw solid #FFE866;
      min-width: 0;
      position: relative;
    }

    .crowd-block-item-text-skeleton {
      font-size: 3.2vw;
      color: #333;
      text-align: center;
      margin-bottom: 1.0667vw;
    }

    .crowd-block-item-name-skeleton {
      font-size: 3.2vw;
      color: #652B23;
      line-height: 140%;
    }

    .crowd-arrow-icon-skeleton {
      object-fit: contain;
      margin-left: 0.8vw;
      font-size: 1.6vw;
      line-height: 1.6vw;
      color: black;
    }

    .crowd-item-mark-skeleton {
      position: absolute;
      top: -1.3333vw;
      left: 0;
      width: 100%;
      height: 100%;
      background: #6666FF;
      padding-right: 0.8vw;
      padding-left: 0.8vw;
      border-top-left-radius: 1.6vw;
      border-top-right-radius: 1.6vw;
      border-bottom-right-radius: 1.6vw;
      border-bottom-left-radius: 0.2667vw;
      color: white;
      font-size: 2.6667vw;
      font-weight: 500;
      height: 3.7333vw;
      width: fit-content;
      align-items: center;
      justify-content: center;
    }

    .coupon-block-skeleton {
      width: 100%;
      height: 100%;
      padding: 2.1333vw 2.4vw 1.6vw;
    }

    .coupon-block-header-skeleton {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: 5.4667vw;
    }

    .coupon-block-tips-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .coupon-block-content-skeleton {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 1.6vw;
      margin-top: 1.6vw;
      height: 15.6vw;
    }

    .coupon-block-coupon-skeleton {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 1.8vw 1.2vw;
      width: 36.8vw;
      height: 15.6vw;
      background: linear-gradient(133.62deg, rgba(255, 247, 204, 0.48) 0%, rgba(255, 247, 204, 0.8) 100.16%);
      border-radius: 1.6vw;
      border: 0.1053vw solidrgb(0, 0, 0);
      min-width: 0;
    }

    .coupon-block-coupon-text-skeleton {
      margin-bottom: 1.0667vw;
      width: 25.4vw;
      height: 4.53vw;
      background-color: #e4e5e7;
    }

    .coupon-block-coupon-btn-skeleton {
      width: 21.6vw;
      height: 5.8667vw;
      border-radius: 7.7333vw;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 3.2vw;
      color: #fff;
      font-weight: 500;
      background: linear-gradient(226.32deg, #FF4E13 27.89%, #FF5613 44.39%, #FF9C24 84.51%, #FFFFFF 115.75%);
    }

    .coupon-arrow-icon-skeleton {
      object-fit: contain;
      margin-left: 0.8vw;
      font-size: 1.6vw;
      line-height: 1.6vw;
      color: black;
    }

    .sign-reward-container-skeleton {
      width: 92vw;
      min-height: 26.6667vw;
      overflow: hidden;
      background: white;
      border-radius: 1.6vw;
      margin-bottom: 2.4vw;
    }

    .home-flex-row-skeleton {
      display: flex;
      align-items: center;
      flex-direction: row;
    }

    .reward-title-content-skeleton {
      width: 85.6vw;
      margin: 2.1333vw auto 0;
      justify-content: space-between;
      font-weight: 500;
    }

    .reward-title-text-skeleton {
      color: #0F131A;
      font-size: 3.4667vw;
      line-height: 4.8vw;
      white-space: nowrap;
    }

    .reward-title-text-red-skeleton {
      color: #FF5533;
    }

    .reward-title-btn-skeleton {
      justify-content: flex-end;
      min-width: 18.6667vw;
      height: 6.9333vw;
      border-radius: 6.6667vw;
      padding: 0 2.1333vw;
    }

    .reward-title-btn-red-skeleton {
      background: #FF5533;
      justify-content: center;
    }

    .reward-title-btn-text-skeleton {
      color: white;
      font-size: 3.4667vw;
      line-height: 4.8vw;
    }

    .reward-title-btn-text-gray-skeleton {
      color: #919499;
    }

    .reward-title-btn-icon-skeleton {
      font-size: 1.6vw;
      color: white;
      line-height: 4.8vw;
      margin-left: 1.0667vw;
    }

    .reward-list-content-skeleton {
      position: relative;
      margin-top: 2.6667vw;
      width: 100%;
    }

    .reward-list-process-skeleton {
      width: 80vw;
      height: 1.0667vw;
      background: #F2F3F5;
      margin: 3.7333vw auto 0;
      overflow: hidden;
    }

    .reward-list-process-inner-skeleton {
      height: 100%;
      background: #FFA74B;
    }

    .reward-list-skeleton {
      width: 100%;
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      flex-wrap: nowrap;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .reward-item-skeleton {
      flex: 1;
      position: relative;
      align-items: center;
    }

    .reward-item-top-skeleton {
      position: relative;
      width: 8vw;
      height: 8vw;
      display: flex;
      align-items: center;
    }

    .reward-item-img-box-skeleton {
      width: 8vw;
      height: 8vw;
    }

    .reward-item-img-box-bg-skeleton {
      width: 7.4667vw;
      height: 7.4667vw;
      background: #FFF0E1;
      border-radius: 4vw;
      align-items: center;
    }

    .reward-item-img-skeleton {
      width: 8vw;
      height: 8vw;
      opacity: 1;
      object-fit: cover;
    }

    .reward-item-img-small-skeleton {
      width: 6.9333vw;
      height: 6.9333vw;
      opacity: 0.9;
    }

    .reward-item-img-icon-skeleton {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      font-size: 2.4vw;
      line-height: 3.3333vw;
      color: rgba(255, 255, 255, 0.8);
    }

    .reward-item-bottom-skeleton {
      margin-top: 0.2667vw;
      justify-content: center;
    }

    .reward-item-bottom-icon-skeleton {
      font-size: 2.2667vw;
      line-height: 4.5333vw;
      color: #FF8C1A;
    }

    .reward-item-bottom-text-skeleton {
      width: 100%;
      font-size: 2.9333vw;
      color: #919499;
      white-space: nowrap;
    }

    .reward-item-bottom-text-highlight-skeleton {
      width: auto;
      color: #FF8C1A;
      font-weight: bold;
    }

    .reward-item-bottom-bg-highlight-skeleton {
      color: white;
      font-weight: bold;
      background: rgba(255, 140, 26, 0.7);
      border-radius: 5.3333vw;
      padding: 0 1.0667vw;
    }

    .er-holder-guesslike-skeleton {
      position: relative;
      width: 100%;
      margin-top: -1.6vw;
    }

    .mini-tabs-scroll-skeleton {
      height: 12.8vw;
      padding: 2.4vw 4vw;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
    }

    .mini-tabs-item-skeleton {
      height: 8vw;
      margin-right: 1.6vw;
      position: relative;
      border-radius: 4vw;
      background: #fff;
      padding: 0 6.13vw;
      transform: translateZ(0.13vw);
      color: #5c5f66;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .mini-tabs-item-selected-skeleton {
      background: #ffdd00;
      color: #0f131a;
      font-weight: 500;
    }

    .mini-tabs-item-text-skeleton {
      font-size: 3.73vw;
      line-height: 1;
      white-space: nowrap;
      position: relative;
    }

    .mini-tabs-item-image-skeleton {
      height: 8vw;
    }

    .waterfall-wrap-skeleton {
      display: flex;
      flex-direction: row;
      padding: 0 4vw;
      justify-content: space-between;
      gap: 2.4vw;
    }

    .item-card-wrap-skeleton {
      overflow: hidden;
      border-radius: 1.6vw;
      position: relative;
      background: #fff;
    }

    .item-content-wrap-skeleton {
      border-top-left-radius: 1.6vw;
      border-top-right-radius: 1.6vw;
      overflow: hidden;
      position: relative;
    }

    .item-like-text-area-skeleton {
      position: relative;
      padding: 1.6vw;
      background: #fff;
      border-bottom-left-radius: 2.4vw;
      border-bottom-right-radius: 2.4vw;
      overflow: hidden;
    }

    .item-like-title-wrap-skeleton {
      display: -webkit-box;
      position: relative;
      z-index: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .item-like-title-skeleton {
      display: inline;
      text-overflow: ellipsis;
      overflow: hidden;
      font-weight: 600;
      font-size: 3.73333vw;
      color: #0f131a;
      line-height: 5.33333vw;
    }

    .item-like-sub-title-skeleton {
      margin-top: 1.6vw;
      line-height: 5.06667vw;
      font-size: 3.46667vw;
      color: #999999;
      text-align: left;
      border-radius: 0.8vw;
      padding: 0 0.8vw;
      max-width: 42.66667vw;
      -webkit-align-self: flex-start;
      align-self: flex-start;
      overflow: hidden;
      text-overflow: ellipsis;
    }
      
  </style>
  <link tag="combocss" rel="preload" href="//${isPre ? 'dev.' : ''}g.alicdn.com/trip/${isOverseas ? 'rx-miniapp-home-overseas' : 'rx-miniapp-home'}/${
    version || ''
  }/hotel.css" as="style" crossorigin="anonymous"/>
  <link tag="combocss" rel="preload" href="//${isPre ? 'dev.' : ''}g.alicdn.com/trip/${isOverseas ? 'rx-miniapp-home-overseas' : 'rx-miniapp-home'}/${
    version || ''
  }/trafficx.css" as="style" crossorigin="anonymous"/>
  <link tag="combocss" rel="preload" href="//${isPre ? 'dev.' : ''}g.alicdn.com/trip/${isOverseas ? 'rx-miniapp-home-overseas' : 'rx-miniapp-home'}/${
    version || ''
  }/rentCar.css" as="style" crossorigin="anonymous"/>
  <link tag="combocss" rel="preload" href="//${isPre ? 'dev.' : ''}g.alicdn.com/trip/${isOverseas ? 'rx-miniapp-home-overseas' : 'rx-miniapp-home'}/${
    version || ''
  }/newTicket.css" as="style" crossorigin="anonymous"/>
  <link tag="combocss" rel="preload" href="//${isPre ? 'dev.' : ''}g.alicdn.com/trip/${isOverseas ? 'rx-miniapp-home-overseas' : 'rx-miniapp-home'}/${
    version || ''
  }/overseas.css" as="style" crossorigin="anonymous"/>
  <link tag="combocss" rel="preload" href="//${isPre ? 'dev.' : ''}g.alicdn.com/trip/${isOverseas ? 'rx-miniapp-home-overseas' : 'rx-miniapp-home'}/${
    version || ''
  }/other.css" as="style" crossorigin="anonymous"/>
</head>`;
}

function getKingkongHolderHtml(opt) {
  const { anchorTab, pathname } = opt;
  const isOverseas = pathname.indexOf('overseas') > -1;
  const kingkongData = isOverseas ? KINGKONG_MAP.overseas : KINGKONG_MAP.normal;
  const { bgImg = '', orderList = [] } = kingkongData || {};

  let holder = '';
  orderList.forEach((item, index) => {
    holder += `<div class="rax-view-v2 hmkt-item" ${index === 0 ? 'style="margin-left: 0px;"' : ''}>
      <div class="rax-view-v2 hmktii-container">
        <div class="rax-view-v2 hmktiic-wrap">
          <div class="rax-view-v2 ${anchorTab === item.id ? 'hmktiic-img-box' : ''}">
            <div class="rax-view-v2 hmktiicw-img ${anchorTab === item.id ? 'hmktiicw-active-img' : ''}"
              style="background:url(${bgImg}) no-repeat;background-position:${
      anchorTab === item.id ? item.bgActivePosition : item.bgPosition
    };background-size:720%;">
            </div>
          </div>
        </div>
      </div><span class="rax-text-v2 hmkti-title ${anchorTab === item.id ? 'hmkti-title-active' : ''}">${item.title}</span>
    </div>`;
  });

  return holder;
}

export function getBodyPartHtml(opt) {
  const { pathname, anchorTab, geolocation, titleBarOpt, pageStructure } = opt;

  // 金刚锚定背景
  const translateX = TRANSLATE_MAP[anchorTab];
  const kingkongBg = translateX
    ? `<div class="rax-view-v2 hakt-bg" style="transform:translateX(${translateX});"><img class="haktbg-image" src="https://gw.alicdn.com/imgextra/i4/O1CN01QTV4Uo1czMz66iOv2_!!6000000003671-2-tps-328-272.png" style="width:21.8667vw;height:18.1333vw;"></div>`
    : '';
  // 金刚icon
  const kingkongHolder = getKingkongHolderHtml({
    anchorTab,
    pathname,
  });
  // 获取当前、明天和后天的日期，构造锚定tab对应的小搜卡
  const currentDate = new Date();
  const { month: currentMonth, day: currentDay } = getDateInfo(currentDate, 0);
  const { month: tomorrowMonth, day: tomorrowDay } = getDateInfo(currentDate, 1);
  const { month: dayAfterTomorrowMonth, day: dayAfterTomorrowDay } = getDateInfo(currentDate, 2);
  // 获取定位城市
  const locationCity = geolocation.cityName || '北京';
  // 小搜卡内
  const bizCardStr =
    (BIZ_CARD_HTML_MAP[anchorTab] || BIZ_CARD_HTML_MAP['flight'])
      .replace(/{{currentMonth}}/g, currentMonth)
      .replace(/{{currentDay}}/g, currentDay)
      .replace(/{{tomorrowMonth}}/g, tomorrowMonth)
      .replace(/{{tomorrowDay}}/g, tomorrowDay)
      .replace(/{{dayAfterTomorrowMonth}}/g, dayAfterTomorrowMonth)
      .replace(/{{dayAfterTomorrowDay}}/g, dayAfterTomorrowDay)
      .replace(/{{locationCity}}/g, locationCity) || '';

  let bodyHtml = `<div id="ssr-er-holder">
  <div class="rax-view-v2" style="position:fixed;z-index:2;height:100vh;background:#f2f3f5;">
    <div class="rax-view-v2 er-holder-home-titlebar"
      style="padding-top:_cookie_status_bar_height_er_;height:44px;box-sizing:content-box;">
      <div class="rax-view-v2" style="position:relative;"><img class="home-titlebar-image" mode="aspectFill"
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOAAAAA2CAYAAADeSpXTAAAAAXNSR0IArs4c6QAAIABJREFUeF7NXQuYHUWVrlN9701mSEhmEDMQmVjVdzKJgQAOKCzgRtz1hboioigyo8ii+BaR1UUk6+ITFV0UH7i7AousIr4VV3zEBwsuRBESQ5Lb1Sa6gCskBMhkXl1n+8zXfb++3VXdfe/MHa3vy8fH3HpXn3qc85//gBBiBfsLS77v//HP1aWNGzfya6+99kMA8Gul1I22fgwPDwvTb4cddtgDmzZtmrCVW7169RMAYGn69/7+/gdvv/32Aws1binlkOM4B6XbW7ly5ZZNmzbNdNqPoaGhp8/MzNzOGINkHQDwaK1Wk/fdd9/DndbdSbnh4eFhrXVPuqzjOL9f6L6Y+g9SykcQcVkng+tGGQDwlFL1btRdVOfw8PDS6enpLyHiC+iDqVQqx+7YsUOly7mue7rW+muG+u4fGBio5wmSlPJriHi6oezRvu/fU9TH+fpdCOExxmRKSKYHBwd7OxXAkZGR6p49e+5kjB1t6Oclvu9/YL76X6YeKeVRiPhrxpiTyj9erVZX79ix43/L1NPNPCSAP0PEU7rZSJt1f9T3/Xe2WWbO2elEm56e/jYirosrA4D/6evrO3nz5s3T8d/WrVvXPz4+voUxdli6Uc756zzP+3xeZ4QQ2xhja1J59KpVqw7KOznnPMBEBRs2bFi8a9eu/Ywxnqp3m+/7T+m0LSHEJYyxyw3lH+jv769v3rx5vNO62y135plnOps3b74dEY9PlwWAjUqpf0r+XUr5XsbYGe2202l+ALjJ87zLSQDPRsT/6LSi+S7nOM5fNRoNusIsWBoaGnpGEAQ3I+ITDEL1Xs/z/jn+u5TyRkQ8y7CoOwYHB9flnR4bNmyo7N69exwRq6nyyvd9t8yApZSfBYDNnuddUya/Kc/q1auPnp6evtswhq8rpV7SSb2rV69eMzMzczciLjLM4flz6W8n/RFCvIMx9lHDGP/Q19c3nN4MhBA/YYxt6KStTspwzp/ved4ts/d0KeW3EPGFnVQ0z2XuV0o9CQBwnuvNrU4IcQVj7CJLpnHHcYYbjcYf6vX6y4Ig+LIpHwCcppT6Xl5D9JFOT0/TCdiSAOB7SqnTisYshHgTY+wqygcAP65Wq+dt377dLyqX/t113Zdrrf/TICgf8DyPTrG2Em0su3bt+hlj7ERDwV2VSuXFQRAEbVWayux53pay30W9XneDILiXMZZ5+zHGzvF9P3PgCCEeYIwNzKWPZcsCQGNwcHAtbdazArhmzZpDpqam/v3PLYQA8Bml1BvKDmS+8q1bt642Pj7+S8bYMRbhuqGnp+ei6Op5iEGAbuScf15r/Z68PiFiP2PsWEOe+wEgI5iRoP2UTmAp5cmMsR+nTs/9AHDJ6OjoVRs3btSUHxHhyCOPTJ+wLU2Oj49TPy81jOPcnp6eG2xj2LJly7RJCKSUn0HE18/Xehjq2e77fvrabmwu2gx+FM7VMwzjuzM8dZ6eHsMxxxyzfN++fXu72P9k1fs55yfQhjK7vslfouvo+Yyxoxhj3VLMUJst7cZ9cBznuY1G478WaCJamqnX608JgmAzY2yxoX06kenKZhKehx3HWRsEAc2b6f0zp+EAwAer1eqnpqamqG+mHXp7rVY7fvv27Y9RQ/V6fUMQBHSdmvfEOT/O8zzqRzNJKd+KiJ+Y98YSFXLOL/Y8j24phcm2GYQbFb3jTwpvGqQkSq/9CUEQLMSz53ec83M9z2uuj1EQCkc5hwyu656vtf6cYXd6rKen5wlbt26dmkP1cyoqpbwQET/WRiUIAGcqpW6WUl6PiK9qo2zZrH/PGDvXcr3b7zjO0xqNxm/jyqSUr0fEz5StvJ18vb29S7du3fp4oq3nI+K3DFrGdqrNzQsAv6/VaseWMRmEGv23hRr9K00VAsDblFKfNP0mpXwNIv7bvHW6tSJSPN3FOb91+fLlH0+/PRdcAIUQ32SMvcgw2K/4vv/yLk1CqWpJjb53797fhkqpUmYQAHifUuoyqlxKeSciHleqoTYyhW9LqjejyaMqOOdneZ7X8iaVUn4CEd/aRhOlspIgKKUG48yu656KiN9ExCWlKuggEwA8XqlUTt6xY8dvioqH839atBmkNbv0Xv6qUupMWx30BJmZmckoj4raLPP7+vXrx2+66Sbr+3dBBbBery8KgoAMsRkDMACcrZT6UplBdTOPEOKljLGbitoAgJs9zzszfk9IKR/r5seY7g8AfEIp9fb034UQdIV/dlH/2/0dAG5VSs3WK4Sgcf8HItbaraeN/Jsqlcobdu7caXwbJ+up1+vPDoKA7LKm72onXZ0bjcajbbS9YFkXWgCfEwTB9w0f08zBBx986N133/3Igo08pyEp5XZEXJ2T5e6lS5eefM8995Atjd5dTwqC4PcL1XcA+Png4OCpJpNHeJ3aFV6nmifVPPbpKt/33yKEeCNj7F8MNkRqiq7ktyBip0iaCUIgIeJtvu//vEzfw7V6RbgnXGsw7VBxQhadsJAAhzJ9TuZZUAF0XfcqrTWp0lsSAPxIKfU3RZ0nDV+9Xn8qItJOvIquKIioFi1a9PVt27aRGnnOidqQUn6dMfZ3psoA4P8YY8crpXbHv9MVZnp6+kl5jQdBcIrW+ouGsd9QqVTICNxMQRA8DRHpTVkx1LmLMfZ0E1wvumGQsqi5rgDgWK7UBJejukolzjm9jdeb1i+qQHPOX+t5XmaMpRroIJPrum/RWpMCyPQdB5zzs9NX9A6a6WqRBRVAE/yJRsc5f4vnebP2LVsSQqwP0SekvDnBkEcDwL8zxt4RXsv2dTpjrus+kUAJ4Q78txbhmwKAZ3qe99/ttpG04SXLhpC3C5VSTcXB0NDQ2pmZGdr9TeaOfeFj/qRGo7G1bPvRvGXeUADwNaVUKeSHlPI42hAMCJ64GwEAjM7XE2JkZKS3VquhDdJHv+/du/djNtMHAMyEtrZX/aULH03egglg9GE1tXXJD8hxnHqj0SBsojG5rvtqRLzGciI0yxCOFBFP6gTMTUoFrTXZwPKMsZt8339m2Y8/mU9K+SlEpOtbS+KcP8/zvNlr+dq1a1dNTk7eFo51ZTpfpEZ/nlKKbFylk+u6Y6aTl3N+KUGhiiqKIFqX2uae+oWIZ/u+X/huNrVFt4eJiQkye5GQk7LpeIIDcs6PiW1lyXJCiKcBAN0OjE8EEj5EfIXv+18tGttfwu8LJoCu616ktc7YcggVoJQask1GhD4hr4SMdstySt05Ojp6QmyYLprkCDP43vDdQsbp3DaKtGl5bYWG9B8i4rPSeRYtWiTuu+++30kp6d1GhnYjJI1z/ppOrndCCFK9v8Ug0IXIHSojpfyB7UbAGJtwHOesRqNBmu3CFM01YU1JW0zCRv892qTMCZVMy9O3GSklaZzfk7cZMMbOUkqZgPKF/ftzZFgwAbR9gJzzT3me92bT4AnNzhjbbHlgW+eLcz7med51RRMqhHghAFyOiHS9LUyc846gWlSxEIKQ94enGhlXSi1Zs2bNk6empsg4u8qyqTTNHYWdTGWwge0XL158eJl3c71ePzEIAtOVm7STZ9kUHPSWdl2XNtZY0OhkIyBDb9EYCOuqlMqYdOhtjogvtpQnyOAry24GRX1YqN8XRAAj5QBBfTLYvHCyXxhO9nfSA44W8Jc2G1jeBJEXg1Lq6bY80XXz/Zb3ZF7VTRwhIWcAoJQaHhEXm5AWdPpzzs/TWtO706bEIXV8xtwQd5Jz7sUoGMsc7gvBBWn/wwd93894c9gGLoSgK/Jz4t8B4N/6+vrenDQq0/V5YmLieACIT7eRObi5vcj3/W+n+xOByMm9KO1reK/jOC+PTRZSyo2hKanp1ZKYq5sajcZXFkq4yrSzUAJohEcBwNSSJUv6Y3V+ssP1ev3vgiD4hmUQhIG8ChH/2oQQAYDJvr6+pUk3IqrHdd0jEfGTiHhqmclJ53EcZ6TRaPwqctolE4QJttZJ1Z2WIQPvStubV0q5OnwPbU9XThsUIrbcOiqVyhQibms0GpPp/PV6fRaqRT6SAEAuV7NAbtd1T0JE0mr/jcmTpMNBfd73/dfZyoa3IvJaaXpsAMBnBwcH3x67ckkpX4CIGeFljD1SrVaHduzY8ZDrurQ5lN6AOhxHYbFKpfLbBRFAIQS582SAynnmBynlzxGRAMgtiYSLc/6cRqPxU/Jr27179x8R8eB0PhNusUCo4ypIsDIGXbJx9ff3L6Fdf/Xq1XJ6etqqNCqc+XnKUGS+sXk92JqPFCqbHcd5I200yXykjKlWq9eT98W6deuWHDhw4DqLY3FHo4uUTB/3PO/deV4PkVaXTC37AOA8ggHGDdbr9UO11veGULwMy0MSiial/AUp6zrq6DwWIte7BRFAKSVp9v7KICRGkK3rukdorclGlekfAFyplLowrsuG/DAha9avX3/Q448//rDJZ43qA4Bvc84/aHnz/M73/VkaCiklYSC/O49r0XZV9MECwIY8k0iBm5W1TbqZMMZeYVJmnHjiiT0PPvjgbRZgelvjiNoht6EfVCqVT5f1UHdd9521Wu0r27Zta7Fj2mCOAPDbwcHBo2PgQmgSeshk5mmr83PMDAC7Q6Xak7sugNFuucekSCHKh507d2YcQ3NsZkQT4dI1Ih6/7WHOOT/d87zMFVYIcQtj7LnJ+QMAUpC8NQJVPys8eX9oOHlvUUo9n/5uc/ac45q0VRwALlBKfTavkBDipya3nDIN0Zz09fWtToOHbWCKEnWSrZDMUOSNcBf96+np+c18ge9d1/17rbWNjeDZvu/fSn0kTp7p6ek/lehvN7Og4zgvbjQa3+q6ALqu+zyttclR9ZGxsbFDTOYCIQRpMM8xCMGNSqlXJv9u+8g456cm3T4SJybZ4j4VnXh/QsQPDwwMXB0bfW3CT0gQz/NmnXZd171Ga31eN1cop27fcZwLity2oncq4R9N1+lSXeecvyJ+71GBCHJHCKCi74bct3YAAAnanfTf5cuX/7pblBThc4XMO9+zYFOv831/LLH+RL9CzsN/tsQ5b7IsFE3knDsppfwwIl5sEKbvKqVeYGpASnkPIpIJIp3e5Pv+p1MCaPRkNr0BqVyk8ifn2yuXLFlyVVoBZDOYh94Arw29AWZdVqSUxCdi6p9pOAQne26eKYWuYoh4CwDMOtUakiYFCQDcvmLFip+UYU+LCInmRPIEAIQs+njcnwj6ZXTpIVgbOVSTgofMCAsFfpZSkpmD7Kcmr4zfDAwMnJicr4KTcs7fe0EF5A94QQy8mD0Eut1izglldbK03dHTQkWUc1NTU/eZxpBn5yL0he3qI6X8kUlL2glXDeFW6VoUauVGbPNMbj6I+FLf9/9nPtdCCPFaxtgXDHXeEWNAAWAJIhIPivGUTJMXSSm/iogZ+BoAfJdzfoZJgzqfY0rXlQfbC58Ue6vV6nFpVrtISzpnbxEAONw0F4wxso8mnzAEUCfBu33RokW/Sn93XRVAQj7cddddhM3MLHClUjlh586ddBJlkpRy0nSdqFQqT0m6p0gpL0DEqw1V3O/7fgbOVeZjsBjM2bJly/rKemtE9Ib/HKno05R4zW6QFrNSqZyVfNOW6WOZPETehIgZdb7jOE9tNBpkS5tNZM/UWpMHgsmm2cKfIqXckmSNi6rAWq3m5nHTDA0NHaO1fhUiDtNJFeJGFSJ+f9WqVV/vlAIxsgnS0yYNbqBu0Uf/giKOnjLzaMtjIzNL+oiWqb+rAphzDdq/atWq5bbJl1LSlSzDa5L+eIQQZFTNOFoCQEfsXvV6/eAgCExg7gd83zctdMsc07vr+uuvf5XW+oOWDyPOTx/Ih0ZGRi7Nc9Yss4A5H0jmGh9eKR8bGRnpS7dpQ8uEJxt5fdA7jkUwMmJ0SwtqUzuc7gtxDU1OTv5rjmdJo1KpvLSMw22y7gjBRPytRmfg5BtrLnOYV9Z13fdrrf8xnSf9bi5qv6sCKIQgRUWGPq/IfmU7hcgTIFa7R+rw+xljy9ODLKMhNE0MAX0ZY5lTuai/0UnyHK31R0rA2ohD5lzSgBUtTqe/RyRDewxPjP/yfb9FA0xtCCFIE50h001SUBCTNiLuMMx1Uzuc/C1S2PzCBq9L5B0HgBNCzwwyRxSmSAP9kRzc7s983yeARleTjWC5Wq0e086G0m0BJLUwcZq0JAD4sFLqXTm7912md1NSsMhDQmtNLkiZxDkf9DyvbQfZHM8BK1414hS9rA10zfbIp7DoAyG/QzKKf6OMV3iyMpud0uQBEWlLieelBSYYGrn/oJQ6Iq63Xq+/KAiCDOg6bZel/NEVnGy/ZRVV28bGxo7MA9AfddRRffv37yePkhYtuOHb+rhSijhBu5rmi2C52wJo3FlNXCapD8hIc0eGcqXULJ+MlJJwonRipdMvfd83+QzmLkiEqiF+1IwvIAC8USnV8tYkGgSiIew2qzghfxDxIt/3Z00nZZKU8oOImNngHMfZQAiiZB3ECD41NZWh3weAHyqlmnPhuu7FYYyFD6fb55xnSHellFci4tvK9DWRx4j/jNaa+F7oJlUIHzP1p81+FGaPuIPoOt7iMN1JWIWuCWDOm4EQJ8OhPS9znYlHTjQDob+XiR8mWLx4sTs1NfUErfXs28TwQbza87xrC2cxkcF13brWmt6TJtpB5jjOMxuNxqZo4unN+fZuEDAV9LlpTC4amwnGR4K8YsWKvrQJIwfVM0tBkVgT4o19taHtZyTpI0gzGQQBvT9N3vxkjD9gebu12OuoHQLxa61JmWRq1zgN4XPhlJBdga6+XUs239YQiP4dpVRbBNddE8CInbiRngVSBHietywP7xfFXyB0SgbsTJwjjDHCf5oWZc+qVatWthNjISIY+oIJTxr3PfQeXw8AL9ZaX1BmF+7Syv/a9/2nFtUdneQUcKeF5QsAfqKUyoDQbVSM6VNfCEG8mZmbheM4T2w0Gk1kSQ7tyEPVavXkWq32f/v3799tEMLf+L6fIUaWUpI7WuG443mpVquHdkOrnJx3KeUZiGhy+L3C9/2MzTtvzbomgDYEDAD8IvTqLgwGI6W07bh54ykd2CXyxP5YDsdJs53oGtgV2roigUr8ro877rhakdZUCEHXxh8Y6jVGJxJCGN/paSSREILcydIKr4fDq3EznkYUHYlCy/UZ2m9eMaWUWxGxJQgMYSOVUhl/SNJ4MsbKKqxa+tPG3LaV1XXd92itm/FCEhv1ueEYjHoJWwNdE0AbSSoAfE4pVUhjXq/Xjw2CgK6ZpTzhyfbjOM5QHrVFPAlklwqCgCguCnk8AYDQ9UcuBGihxFcwUES3IYQgDWEmupTN7mrzOkkCGdatWzcwPj6eIb0CgNuUUk2PlYg7JsM8TXEslFJNNgAhBHlatFz307yjqROnhXOVdAGmMAplN/cS85ybJdQI32BSBjmOc2Kj0SCgQ+nUTQG8GhHpytaSwtPk3UqpD5XpYTvA3zIBTiLkzPsi22GpsQMAQd9ObOcaFOYnbs73c85/VzagCDkgE88JIhI51VrD/DTdofLmzvJx7xsZGTnEdHpKKQkP2xIVCgD2KaWap53rus/UWv843S7n/Aue5zW13BFloUlZ9OZYiRSBM4hGP611tTpRR6S75LR9PzkwM8YmtdYZbpx0f8p8Y53kMc0x1WOi0Siqv9RHWFSJ6Xcbl0g7hsrIk+IOA/oi02Qc7snUl4jsaCMiEsDbikyxjPNNALASEd+d/J0+UpPHN2EhBwcHT+oU4RF562c+LtsVLdkn8ocLgoCugGmPcSMDWo5nQIsmOQdxRNrZJpW/TfuaRDDR7WNmZqaJxElc325QSlmp/aWU/3DwwQd/jtBIUso3IGILJjgSgBbsaiffbVEZm9mGMVYKrJE5kIoa7PR32+O53WN6eHj48KmpKdJqGUNCJ/pH1IR0Zf0R0bkfccQR3yYhiBjVPtcpizPn/FkEko4CnlAb3+Scf1Jr/dfhqdUS5DHqy3m+7xP6o6MUBYkx0Q4ajejJRqSUr0TETHSjJJA8lf/kkCjKRIB7re/7TSWXEIKIeDO8PemQbLbAKIsXL35y7LuXU9cblFKlYlrkKHpKEU11tDBRIQLzT05OZkLClQFrmNrt2gkohCDbUkZoent7D9u6deuD7UxC5MFAwR9LRWwi74LBwUGKOEsCSNpLItq1JrJ5RZwsmRBY9BbinD88MTFxWbVavYYYzKiikO6BoFDEytyS8jCuZcZsUzoAwCeVUrm2NSEExYl4Waod7O3tPdw05zbPAAB4l1KqafOz3WZCD3nyzWzaEG2eJNVq9UnkbBsZ6P/XwFFD3LBDnudltOamOZNS3oqIGSJnikHfSbzEMusS58kBOVjBGrnfXjuNt5NXCEFQqIw2zHGcxe2i5nNishu7BAD3KKVmoVXRNfYhkxc8ADzEOb+w0WhcH16zyLTxxFSFj/i+b9LoEXwro0igspGCgCjRO0oh56VrQgFxzl/veV4mqlTcSGQzo/dcCwET3QZCULIJsECbCJHbNtkF4roiZ9Em6kVKSWaDJiomyjcxNjZ2UBK9QrQVpltB7MVioyYJ3bXu8H3fFNzTOIdSSvIgSZNYZfrT0QIUFMqJvJsBa5RpvysnYETvTvEBMhpMpRQvq5igARAE6fHHH6crWSEKIh4wAHxJKXV2/P9p2oqIf+QLtVrtUgp7ZYtcG4YsNhLxRuMj+FYhxV6ZRSiTx4RiSZaz7czhXF8WzjkpnjJJSklOrM9L/5AEStAGNj4+Pht7MJmSm1xinukNRwza6bwXkjtOEAT0lMi8wUNlVWnwBJ2iU1NTpkArRjtimbltJ4+UkmzG5OrVkmwO4EV1d0UAI6A0xUVLL8SMUio3emu6jC0me5SPnFQJJJyGBP2jUoo8EmaT67pv1lr/S0RZ/sVarXZ5kk/EZozmnL/f87wMmVREwTd7FV2oxDlf4Xke4UONyeal7zjOkTYqe9MzgTanwcHB3liJRAxiFtRRJpxczvuINtBFphgVRM04MjKypsi+GQ/aZuoINcdf9n3/rG6vh43fqJOnFfW1KwJIFQsh6ARM73bjvu+Xpkiw+VzFwtff33/0xMTE0snJydO01oQRPSVixGrBFRLecXp6+uJKpXJF2kEzOs3IiXI4vXi2iL31et0Y5amLi7/X930Kb21MUYht8gxpiSdBHCxKqQw/JlUSbZJ0irfcUtJlhBC2U+2flFLEv9mSwjXbWTa+IhXknL/E87zcN3qygXq9fk4QBBnSZQAw9me+18TytLI+VYra76YAmpATODAwcFAZSgXCZyLir0wPdhpUjM9MD5DKVSqVP9rIag35z9JaE/V9SwqhWH/q6+tbmeYWpUx5kViLJryT3wHgv5VSVhq9kMLjJaHnQZOeL24j76OMHFozhFjpoC2u616utb4k3W+bOYkYy8gtq+Q4b/J9P600yi2a44eXCVZasg+ls9kACSFp8e2+72dY/8pU3DUBtDzcSXDWJUMqmzpJ/myPPvoocTcad+9QBjLA3TKDTedZu3btYRMTE1vCeHeZ0wUArG4tNm/zTvpQpgwxUSulMu+OuKyUkpAhGX6dvLm2cYam6ffTRLhxmza/t8i2SNfzopvOrogyoslwV2Yu5ssPr0xbhs3aCEgoWp+8tromgDYtIYWnNtGOx52MaOwJy/gMS8cfWLJkybp7772XTtg5pZDQ57uIOEs1mEzRW3GtTS2ew3NDDHCk/e0occ5fprXO+LJxzt/ped5HTZXSrnzgwAHSCqbfwVbtJ9VD9O2IOBteO5XSNBQZ3CZjTA8MDCyx3WSEEOSNMpozCeOVSuUkEyVl0cQJIYjaMI0Uyu1PUZ1lf7cBABhjLYCEsvVRvm4KIKnMzzd83FcrpTJhuigfvWUOHDjw5ZwAHKTmJ66POZPi5nyA1ManlVKZQKLxWEwmC/LyCBnEMgzd7SyGLb67LX5GJEjvDkEGHzDMc65hWwhB9PIvN5Rr0lBs2LChsnv3bvJ7SyvOQqY/X6bLUty+PXv2kPIrE40ptcGRFpP6lwEN5M1Xu/1pZ+7L5O0GAKBrAmjzWKcPtaenhwzDpABopshIS9FvMiG8EtnadvdIT2yksCDnTtsOvddxnOGki02yjshViuLct6SiMGtlFlgIQUDeTFAZW/zEyPZHIanTVOwTy5YtO8xGIhUpnsjonRGiJA2FjXWOXMJikuLEpkRs4eS0bIzwZBo/ANxcq9UuisENRXOU05/vKaVOKyo/198tpFRsLgCArglgHmUgY6zFbSgK/kGcmyYQ8uy8AcA3RkdHzygb98802dF17St5Xuw2Ru24PgpIorXOOHwS+gYA6p1QYVDdURxEQrKkBXtydHS01zRu13XP11pnjPMA8EWl1GtsH5wN22mgobAFyPm+7/uz9kOyoc7MzFCIt1LRdg19ogAzX61UKlfaWPISc29ENeW91+cqdFQ+itRFQUpN0MMDY2NjSzr9LrsmgNRxm6tLNClEfkT/CP5FsCKr2xEA/Kqvr++UTpmVo9DTBOAlZmurciBEs38iDAppDQUWjek1iDhL0GsQFmKNJn+w0u9TAKhqrYlc9qWmObDFyiNQ8HXXXbfdpPJPUv4T3AwRY3MQ+TSSqYaiC5nibrScJLagqtGGSI7Rh0ZeImVdxorkgTCWJIzfqdVqd6Q5NKWU7woRTU37blzZXGgoiAk9RC9ZeVsZY/S2fmrafzFue64uUN0WwPkIYrK9Vqudun37drJztZ1c131d6G5DZD4mioRmfYSeGR0dPadoJ7PZxdruWMkCaVxmXExKadsImuidduMghB4XLQ6lxACOiO8t2VVTNgojR5seQdno2l9I7ZioJKOoEUJ8kTHWpJlPCEHHNBQ5ysKyw86wtZctOLuRtZO5k7w2KoMydZFrT6VSOW0uFAMUN8AUbCXZfgi9unFkZOScMmgMImMKgoD8/bqeIshcXSlFJ2szEX91LKFkAAACtElEQVSp1nqHKQxXEjwQRiW2eTvYhGVlMix0Drt2mbHfEQYPPSfWJEesZp82AdhtlfX29h6ydevWplbZRsTVKQ3FXCGF5JJWqVTqc/k+uy6AxA9JUCbTx1Kwij9YunTpS0zBO8usfpwnIlIiMLZJQ0kkQZd4nveRsvhUCnH22GOP7WwHm9pOf1MbgxHgawNRpw327cRBMDHVCSFWkB9im65cD1PY79AB+CrThhZ5p9CpaiTASow/w24upSQfzJZ1JEC9UurQTuZ4jpBCck2j6M6mwEOlu9N1AaSeEOpiZmaGIpu6RT0jG1wY0vlD/f397zOhUIrKm36XUt5E8RdSv22Logy10PSVqd/GH1qmbNk8BUAA4suhq1jL+qUBwTZBTfUBOeeXeZ6X4TihfGGYAKOZwzAOCmx6peM4V5QJzEKOx4j4jggMbvoOW/wfI79QIupqSXN5g80BUkhjfetc/D7jQSyIAFJj0bWJqLxfawlnPOvsGiqdCL1fiiW57Mfsuu6o1jqmKnyQgnAeccQRV3fqtU7tRi5S5HhrdFcq2zdDPiLupZMv4xWfzBuBkq8IPTYouAppiW9WSrVsMjZvh+biA2yNQk5TwE1rCk8eUky9z0IneB8AXAcA/5oHFrdVHvl6ns4YOz2KWhsrdFo05REtRiZuIwWgyQtpXTCut4U+pleWXSu6cjLGbq1WqxfPl9/hgglgPMjIdnV8KGzHIqJAxP3hW8HTWt9SRDhUdqLS+SKqhutDaolrBgcHvzkXwUvWHY2FKAuPQcQ5uSaFMdsfrtVqt7W7sFGcBLrSnZF+K4ZwwHMBoIXvJer/Hx3H+XkamF7wsS4j0mIytRBDAABQVKqtZUiwyq4brRMxjCMiUcsTx+acrndF7VKoca112s/RWCwIgr1jY2NbipR0RW2mf/9/o4zObQ4On6sAAAAASUVORK5CYII="
          style="width:20vw;height:4.8vw;margin-left:13.33333vw;z-index:2;position:absolute;transform:translateY(-50%);">
      </div>
      <div class="rax-view-v2 htb-transparent" style="background:#FFFFFF00;"></div>
      <div class="rax-view-v2 htb-default"></div>
    </div>
    <div class="rax-view-v2 rax-scrollview rax-scrollview-vertical"
      style="width:100vw;flex:1;-webkit-overflow-scrolling:touch;overflow:scroll;">
      <div class="rax-view-v2 rax-scrollview-webcontainer">
        <div class="rax-view-v2 ha-bottom">
          <div class="rax-view-v2 hab-default"></div>
        </div>
        <div class="rax-view-v2 er-holder-home-content-lay" style="border-radius:6.4vw 6.4vw 0 0;">
          <div class="rax-view-v2" style="height:0.1333vw;width:100vw;position:absolute;top:0.4vw;"></div>
          <div class="rax-view-v2 ht-search" style="background:#FFFFFF00;">
            <div class="rax-view-v2 hts-content">
              <div class="swiper hts-tips">
                <div class="swiper-wrapper">
                  <div class="swiper-slide htst-item swiper-slide-duplicate"></div>
                </div>
              </div>
              <span class="rax-text-v2" style="font-family:fliggy-home-iconfont;position: absolute;left: 2.13333vw;font-size: 16px;color: #000;">&#xe604;</span>
              <div class="rax-view-v2 hts-button">搜索</div>
            </div>
          </div>
          <div class="rax-view-v2 rax-scrollview rax-scrollview-horizontal er-holder-home-minor-kingkong"
            style="flex:1;-webkit-overflow-scrolling:touch;overflow-x:scroll;overflow-y:hidden;">
            <div class="rax-view-v2 rax-scrollview-content-container-horizontal"
              style="display: flex;flex-direction: row;">
              <div class="rax-view-v2 hmk-item">
                <div class="rax-view-v2 hmki-image"
                  style="background:url(https://gw.alicdn.com/imgextra/i3/O1CN01bNiBEb1Ehv9sFeBY3_!!6000000000384-49-tps-700-124.webp) no-repeat;background-position:0 0;background-size:1166%;">
                </div><span class="rax-text-v2 hmki-text">特价机票</span>
              </div>
              <div class="rax-view-v2 hmk-item">
                <div class="rax-view-v2 hmki-image"
                  style="background:url(https://gw.alicdn.com/imgextra/i3/O1CN01bNiBEb1Ehv9sFeBY3_!!6000000000384-49-tps-700-124.webp) no-repeat;background-position:10% 0;background-size:1166%;">
                </div><span class="rax-text-v2 hmki-text">出境游</span>
              </div>
              <div class="rax-view-v2 hmk-item">
                <div class="rax-view-v2 hmki-image"
                  style="background:url(https://gw.alicdn.com/imgextra/i3/O1CN01bNiBEb1Ehv9sFeBY3_!!6000000000384-49-tps-700-124.webp) no-repeat;background-position:40% 0;background-size:1166%;">
                </div><span class="rax-text-v2 hmki-text">酒店套餐</span>
              </div>
              <div class="rax-view-v2 hmk-item">
                <div class="rax-view-v2 hmki-image"
                  style="background:url(https://gw.alicdn.com/imgextra/i3/O1CN01bNiBEb1Ehv9sFeBY3_!!6000000000384-49-tps-700-124.webp) no-repeat;background-position:30% 0;background-size:1166%;">
                </div><span class="rax-text-v2 hmki-text">周边游</span>
              </div>
              <div class="rax-view-v2 hmk-item">
                <div class="rax-view-v2 hmki-image"
                  style="background:url(https://gw.alicdn.com/imgextra/i3/O1CN01bNiBEb1Ehv9sFeBY3_!!6000000000384-49-tps-700-124.webp) no-repeat;background-position:50% 0;background-size:1166%;">
                </div><span class="rax-text-v2 hmki-text">国内游</span>
              </div>
            </div>
          </div>
          <div class="rax-view-v2 er-holder-home-main-kingkong">
            <div class="rax-view-v2 hmkt-container">
              ${kingkongBg}
              ${kingkongHolder}
            </div>
            <div class="rax-view-v2 hmktd-container" style="border-top-left-radius: ${anchorTab === 'hotel' ? '0vw' : '3.2vw'};border-top-right-radius: ${anchorTab === 'overseas' ? '0vw' : '3.2vw'}">
              <div class="rax-view-v2 hmktd-shadow"></div>
            </div>
            <div class="rax-view-v2 hmks-area">
              ${bizCardStr}
            </div>
          </div>
          
          ${getDynamicModuleHtml(opt)}
          
          ${guesslikeHtml}
          <img src="https://gw.alicdn.com/imgextra/i2/O1CN01rnrqBu1KxOCQbN3gm_!!6000000001230-2-tps-60-60.png_90x90Q30.jpg_.webp" style="position: absolute; left: -9999px">
        </div>
      </div>
    </div>
  </div>
</div>`;

  if (titleBarOpt && titleBarOpt.hasImmersiveQuery && titleBarOpt.canUseImmersive) {
    bodyHtml = bodyHtml
      .replace('_cookie_status_bar_height_er_', `${titleBarOpt['statusBarHeight'] || 0}px`)
      .replace('_cookie_total_bar_height_er_', `${titleBarOpt['totalBarHeight'] || 44}px`);
  }

  return bodyHtml;
}

export function getTimeoutScript(opt) {
  const { isDebug, newUrl, isPre } = opt;
  return `<script data-id="first-part-success">
    window._first_part_show_time = Date.now();
    if(window.performance && typeof window.performance.mark == 'function'){
      window.performance.mark('stage-first-chunk')
    }
    if (window.performance && window.performance.timing && window.performance.timing.navigationStart) {
      window._first_part_time = window._first_part_show_time - window.performance.timing.navigationStart;
    }
    setTimeout(function(){
      if (!window._er_next_part_render && ${isDebug ? 'false' : 'true'}) {
        location.replace("${newUrl.toString()}")
      }
    }, ${isPre ? 10000 : 5000});
  </script>`;
}

export function getDynamicModuleHtml(opt) {
  const { pageStructure, activityBlock, bangdanBlock } = opt;

  const activityBlockMap = {
    s_vertical_homepage_home_crowd_module_2025: ACTIVITY_CROWD_MODULE,
    s_vertical_homepage_coupon_module_2025: ACTIVITY_COUPON_MODULE,
    s_vertical_homepage_homepage_cycle_activity_2025: ACTIVITY_CYCLE_MODULE,
  };

  const activityBlockHtml = activityBlock
    .map(item => {
      if (activityBlock.length > 1) {
        return activityBlockMap[item] || '';
      } else {
        return activityBlockMap[`${item}_SINGLE`] || '';
      }
    })
    .join('');

  const pageStructureMap = {
    s_vertical_homepage_activity_module: `<div class="rax-view-v2 activity-block-skeleton">${activityBlockHtml}</div>`,
    s_vertical_homepage_bangdan_module: bangdanBlock.length ? BANGDAN_MODULE : '',
    s_vertical_homepage_taobao_brand_banner: BRAND_BANNER,
    s_vertical_homepage_home_sift_destination: CHOICENESS_MODULE,
    s_vertical_homepage_live_air_hotel_202406: DISCOUNT_MODULE
  };

  let dynamicHtml = '';

  for (let structure of pageStructure) {
    if (pageStructureMap[structure]) {
      dynamicHtml += pageStructureMap[structure];
    }
  }

  return dynamicHtml;
}
