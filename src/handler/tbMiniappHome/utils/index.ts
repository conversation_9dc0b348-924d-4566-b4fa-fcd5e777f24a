function parseCookie(cookieString) {
  const result = {};
  if (!cookieString) {
    return result;
  }
  cookieString.split(';').forEach(n => {
    if (!n) {
      return;
    }
    const [key, val] = n.trim().split('=');
    try {
      result[key] = decodeURIComponent(val);
    } catch (err) {}
  });
  return result;
}

export function getAnchorTab(request, pathname, searchParams) {
  try {
    // 海外版默认锚定机票
    if (pathname.indexOf('overseas') > -1) {
      return 'flight';
    }

    const currentTab = searchParams.get('currentTab');
    if (currentTab) {
      return currentTab;
    }

    const cookie = request.headers.get('cookie') || '';
    const cookieMap = parseCookie(cookie);
    let anchorData = cookieMap['__fm_home_anchored_tab_key'] || cookieMap['__fm_home_init_anchored_tab_key'] || '{}';
    anchorData = JSON.parse(anchorData);
    return anchorData['lastKingkongTab'] || '';
  } catch (error) {
    return 'flight';
  }
}

export function getGeolocation(request) {
  try {
    const cookie = request.headers.get('cookie') || '';
    const cookieMap = parseCookie(cookie);
    const cityCode = cookieMap['_fli_cityCode'] || '';
    const cityName = decodeURIComponent(cookieMap['_fli_cityName'] || '');
    const country = decodeURIComponent(cookieMap['_fli_country'] || '');
    const iataCode = cookieMap['_fli_iataCode'] || '';
    const latitude = cookieMap['_fli_latitude'] || '';
    const longitude = cookieMap['_fli_longitude'] || '';

    return {
      cityCode,
      cityName,
      country,
      iataCode,
      latitude,
      longitude,
    }
  } catch (error) {
    return {};
  }
}

export function getPageStructure(request) {
  try {
    const cookie = request.headers.get('cookie') || '';
    const cookieMap = parseCookie(cookie);
    const pageStructure = cookieMap['__fm_home_page_structure'] || '[]';
    const activityBlock = cookieMap['__fm_home_activity_block'] || '[]';
    const bangdanBlock = cookieMap['__fm_home_bangdan_block'] || '[]';

    const pageStructureData = JSON.parse(pageStructure);
    const activityBlockData = JSON.parse(activityBlock);
    const bangdanBlockData = JSON.parse(bangdanBlock);

    return {
      pageStructure: pageStructureData,
      activityBlock: activityBlockData,
      bangdanBlock: bangdanBlockData,
    };
  } catch (err) {
    return {
      pageStructure: [],
      activityBlock: [],
      bangdanBlock: [],
    };
  }
}