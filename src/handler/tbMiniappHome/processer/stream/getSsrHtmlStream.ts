// 获取SSR文档
export default async function (opt) {
  const {
    ssrUrl, 
    request, 
    timeLog, 
    isPre,
  } = opt;

  try {
    timeLog.timeStamp['faas_req'] = Date.now();
    const newSsrUrl = ssrUrl.indexOf('?') > -1 ? `${ssrUrl}&newStream=1` : `${ssrUrl}?newStream=1`;
    const res: any = await Promise.race([
      fetch(newSsrUrl, {
        headers: request.headers,
      }),
      new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve('get faas request is timeout.Timeout values is 5000');
        }, isPre ? 10000 : 5000);
      })
    ]).catch(err => {
      return `getSecondHtmlError:${err.message || ''}`;
    });
    if (res.headers && res.headers.get('X-Ssr-Start') && res.headers.get('X-Ssr-End')) {
      timeLog.timeStamp[`faas_res`] = Date.now();
      timeLog[`faas_duration`] = timeLog.timeStamp[`faas_res`] - timeLog.timeStamp[`faas_req`];
      timeLog.timeStamp[`ssr_start`]  = parseInt(res.headers.get('X-Ssr-Start'));
      timeLog.timeStamp[`ssr_end`]  = parseInt(res.headers.get('X-Ssr-End'));
      timeLog[`ssr_duration`] = timeLog.timeStamp[`ssr_end`] - timeLog.timeStamp[`ssr_start`];
      timeLog[`faas_req_duration`]  = timeLog.timeStamp[`ssr_start`] - timeLog.timeStamp[`faas_req`];
      timeLog[`faas_res_duration`]  = timeLog.timeStamp[`faas_res`] - timeLog.timeStamp[`ssr_end`];
    }

    if (res.headers && res.headers.get('X-Redirect-Url')) {
      return `ssrRedirectUrl:${res.headers.get('X-Redirect-Url')}`;
    }

    return res;
  } catch (err) {
    return (err && err.message) || JSON.stringify(err);
  }
}
