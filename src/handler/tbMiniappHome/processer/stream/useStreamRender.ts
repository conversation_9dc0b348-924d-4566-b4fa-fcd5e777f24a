import writeStream from "./writeStream";
import getMiniworkErConfig from "../../../../processer/stream/getMiniworkErConfig";
import checkIsGray from "../../../../processer/stream/checkIsGray";
import { getAnchorTab, getGeolocation, getPageStructure } from '../../utils';
import { getKvCache, getTitleBarDefaultHeight } from '../../../../utils';
import { SlsLog } from '../../utils/slsLog';

// 手淘首页流式入口
export default async function (event, opt) {
  const {
    reqUrl,
    ssrUrl,
    raxUrl,
    isPre,
    request,
    timeLog
  } = opt;
  timeLog.timeStamp[`stream_start`] = Date.now();

  const isDebug = reqUrl.searchParams.get('_debug_er');
  const onlyShowFirstPart = reqUrl.searchParams.get('_debug_first_screen');
  const getErConfig = reqUrl.searchParams.get('_get_er_config');

  // 获取页面配置
  timeLog.timeStamp[`er_config_start`] = Date.now();
  let pageErConfig = null;
  let isGray = false;

  try {
    // "/app/trip/rx-miniapp-home/pages/home" --> "rx-miniapp-home_home"
    const reqPathArr = reqUrl.pathname.split('/');
    // 预发拿不到 ER 后台配置，只能请求ssr后台获取配置
    if (isPre) {
      const erDataStr = await getMiniworkErConfig(reqPathArr[3], reqPathArr[5]);
      pageErConfig = JSON.parse(erDataStr);
    } else {
      const pageErConfigKey = `${reqPathArr[3]}_${reqPathArr[5]}`;
      // 获取流式配置
      const pageErConfigData: any = await getKvCache('fliggyrax_124215', pageErConfigKey);
      pageErConfig = JSON.parse(pageErConfigData.data);

      // 如果命中灰度，则使用灰度中的骨架配置
      isGray = checkIsGray(pageErConfig, request);
      if (isGray && pageErConfig.grayVersion) {
        pageErConfig = {
          version: pageErConfig.grayVersion,
          template: pageErConfig.grayTemplate
        };
      }
    }
  } catch (err) {
    pageErConfig = false;
  }

  if (getErConfig) {
    return JSON.stringify(pageErConfig);
  }

  // 如果流式配置不存在，则返回false
  if (!pageErConfig || !pageErConfig.template) {
    timeLog.timeStamp[`page_er_config_err_time`] = Date.now();
    return false;
  }

  // 获取titlebar数据
  const titleBarOpt = getTitleBarDefaultHeight(request, reqUrl.searchParams);
  // 获取锚定tab
  const anchorTab = getAnchorTab(request, reqUrl.pathname, reqUrl.searchParams);
  // 获取cookie中的用户定位
  const geolocation = getGeolocation(request);
  // 获取页面结构
  const { pageStructure, activityBlock, bangdanBlock } = getPageStructure(request);

  timeLog.timeStamp[`er_config_end`] = Date.now();

  try {
    const { writable, readable } = new TransformStream();
    // 流式的拼接html文本
    event.waitUntil(writeStream({
      writer: writable.getWriter(),
      titleBarOpt,
      anchorTab,
      geolocation,
      pageErConfig,
      reqUrl,
      ssrUrl,
      raxUrl,
      request,
      isPre,
      isDebug,
      onlyShowFirstPart,
      timeLog,
      event,
      pageStructure,
      activityBlock,
      bangdanBlock,
    }).catch(e => {
      SlsLog.sendLog({
        event,
        reqUrl,
        logConfig: {
          logName: 'er_stream_log',
          logType: 'error',
          logData: {
            slsNew: true,
            reqUrl: reqUrl.toString(),
            errorMsg: (e && e.message) || JSON.stringify(e),
            ...timeLog
          }
        }
      })
    }));

    return new Response(readable, {
      headers: {
        'content-type': 'text/html; charset=utf-8',
        'use-stream': '1.0.8',
        "Transfer-Encoding": "chunked",
        "streaming-parser": "open",
      },
      status: 200,
    });
  } catch (err) {
    if (isDebug) {
      return err.message;
    } else {
      // 增加降级标识
      let redirectRaxUrl = '';
      try {
        const raxUrlObj = new URL(raxUrl);
        raxUrlObj.searchParams.set('ssr_fail_back', '1');
        redirectRaxUrl = raxUrlObj.href;
      } catch {
        redirectRaxUrl = raxUrl;
      }
      return Response.redirect(redirectRaxUrl);
    }
  }

}