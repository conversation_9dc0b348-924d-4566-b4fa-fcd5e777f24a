import sendFirstPartHtml from './sendFirstPartHtml';
import getSsrHtmlStream from './getSsrHtmlStream';
import getLastPartHtml from './getLastPartHtml';
import { SlsLog } from "../../utils/slsLog";

// 流式入口
export default async function (opt) {
  const {
    writer = {},
    titleBarOpt,
    anchorTab,
    geolocation,
    pageErConfig = {},
    reqUrl,
    ssrUrl = '',
    raxUrl = '',
    request,
    isPre,
    isDebug,
    onlyShowFirstPart,
    timeLog,
    event,
    pageStructure = [],
    activityBlock = [],
    bangdanBlock = [],
  } = opt;

  timeLog.timeStamp[`url`] = raxUrl;
  timeLog.timeStamp[`er_cache_start`] = Date.now();

  const encoder = new TextEncoder();
  const writeHtml = (e) => writer.write(encoder.encode(e));

  try {
    timeLog.timeStamp[`first_part_start`] = Date.now();
    const [writeReason, ssrRes] = await Promise.all([
      sendFirstPartHtml({
        ssrUrl,
        raxUrl,
        titleBarOpt,
        anchorTab,
        geolocation,
        pageErConfig,
        timeLog,
        isDebug,
        isPre,
        pageStructure,
        activityBlock,
        bangdanBlock,
        writeHtml,
      }),
      getSsrHtmlStream({
        ssrUrl, 
        request, 
        timeLog, 
        isPre,
      })
    ]);

    // 只返回第一屏数据，debug使用
    if (onlyShowFirstPart) {
      await writeHtml('</body></html>');
      await writer.close();
      return;
    }

    timeLog.timeStamp[`other_part_start`] = Date.now();

    // 如果ssr返回为空，则抛出错误，强制回源
    const ssrHtmlValid = (function() {
      if (!ssrRes) {
        return false;
      } else if (typeof ssrRes === 'string') {
        return ssrRes.indexOf('<body') > -1;
      } else if (typeof ssrRes === 'object') {
        return true;
      } else {
        return false;
      }
    })();
    if (!ssrHtmlValid) {
      if ((ssrRes || '').includes('ssrRedirectUrl')) {
        throw new Error(ssrRes);
      }
      throw new Error(`ssrHtml invalid(use cache)|${ssrRes}`);
    }

    await getLastPartHtml({
      ssrRes,
      timeLog,
      writer,
      writeHtml,
    });
    timeLog.timeStamp[`other_part_end`] = Date.now();
  } catch (error) {
    // 如果catch，则跳转market页面
    if (isDebug) {
      await writeHtml(`<div id="error">${error && error.message}</div></body></html>`)
    } else {
      SlsLog.sendLog({
        event,
        reqUrl,
        logConfig: {
          logName: 'er_stream_log',
          logType: 'error',
          logData: {
            slsNew: true,
            reqUrl: reqUrl.toString(),
            errorMsg: (error && error.message) || JSON.stringify(error),
            ...timeLog
          }
        }
      })
      const newUrl = error && error.message && error.message.includes('ssrRedirectUrl') ?
        new URL(error.message.replace('ssrRedirectUrl:', '')) : new URL(raxUrl);
      newUrl.searchParams.set('_er_failback', '1');
      newUrl.searchParams.set('ssr_fail_back', '1');

      await writeHtml(`<script>location.replace("${newUrl.toString()}")</script></body></html>`);
    }
  }

  // 打日志
  SlsLog.sendLog({
    event,
    reqUrl,
    logConfig: {
      logData: {
        ...timeLog,
        timeStamp: {
          ...timeLog.timeStamp,
          er_end: Date.now(),
          stream_end: Date.now(),
        },
        er_duration: Date.now() - timeLog.timeStamp.er_start,
        stream_duration: Date.now() - timeLog.timeStamp.stream_start,
        er_config_duration: timeLog.timeStamp.er_config_end - timeLog.timeStamp.er_config_start,
        html_stringify_duration: timeLog.timeStamp.html_stringify_end - timeLog.timeStamp.html_stringify_start,
        first_part_duration: timeLog.timeStamp.first_part_end - timeLog.timeStamp.first_part_start,
        other_part_duration: timeLog.timeStamp.other_part_end - timeLog.timeStamp.other_part_start,
      }
    }
  })

  await writer.close();
}
