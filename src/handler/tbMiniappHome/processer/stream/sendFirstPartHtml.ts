import { getHeadPartHtml, getBodyPartHtml, getTimeoutScript } from '../../utils/getFirstPartHtml';

export default async function sendFirstPartHtml(opt) {
  const {
    ssrUrl,
    raxUrl,
    titleBarOpt,
    anchorTab,
    geolocation,
    pageErConfig = {}, 
    timeLog = {},
    isDebug,
    isPre,
    writeHtml = () => {},
    pageStructure = [],
    activityBlock = [],
    bangdanBlock = [],
  } = opt;

  const newUrl = new URL(raxUrl);
  newUrl.searchParams.set('_er_failback', 'timeout');

  const version = pageErConfig.version;

  if (!version) {
    return '';
  }

  const ssrUrlObj = new URL(ssrUrl);
  const pathname = ssrUrlObj.pathname;

  const headPartHtml = getHeadPartHtml({
    version, 
    pathname,
    isPre
  });
  const bodyPartHtml = getBodyPartHtml({
    pathname,
    anchorTab,
    geolocation,
    titleBarOpt,
    pageStructure,
    activityBlock,
    bangdanBlock,
  });
  const timeoutScript = getTimeoutScript({
    isDebug,
    newUrl,
    isPre,
  });

  const firstPartHtml = headPartHtml + `<body>${bodyPartHtml}` + timeoutScript;
  await writeHtml(firstPartHtml);

  timeLog.totalHolder = JSON.stringify({
    version,
    holder: encodeURIComponent(firstPartHtml),
    isNew: true,
  });

  timeLog.timeStamp[`first_part_end`] = Date.now();
}