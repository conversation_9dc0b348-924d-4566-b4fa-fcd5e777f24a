import { WX_HOST, RAX_ORIGIN_HOST, GD_HOST } from "../../constant/rax";

export default async function handleCsrRequest(event: FetchEvent) {
  const { request } = event;
  const reqUrl = new URL(request.url);
  const hostname = reqUrl.hostname;

  let csrHost = RAX_ORIGIN_HOST;
  if(WX_HOST.includes(hostname)){
    csrHost = hostname.replace('proxy-er', 'proxy-h5')
  }else if(GD_HOST.includes(hostname)){
    csrHost = hostname.replace('front-traffic-fliggy-er', 'front-traffic-fliggy')
  }
  
  const csrUrl = WX_HOST.includes(hostname) ?
  request.url.replace('proxy-er', 'proxy-h5').replace('/csr/', '/') :
  `https://${csrHost}${reqUrl.pathname}`.replace('/csr/', '/');
  if (reqUrl.searchParams.get('_debug_er')) {
    return csrUrl;
  }
  const csrRes = WX_HOST.includes(hostname) ?
  await fetch(csrUrl, {
    headers: request.headers
  }) :
  await fetch(csrUrl, {
    headers: request.headers,
    cdnProxy: true,
    decompress: 'manual'
  });
  return csrRes;
}
