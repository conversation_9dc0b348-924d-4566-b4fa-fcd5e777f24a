import detailFcpToken from './fcp-detail';
import searchFcpToken from './fcp-search';
import poiFcpToken from './fcp-poi';

// 通用配置地址
export const streamRemoteConfigPath = 'https://traveldetail.feizhu.com/config/';

// 流式配置地址
export const vstreamUrlPrefix = 'https://traveldetail.feizhu.com/config';

// 流式渲染页面级配置
export const streamRenderConfig = {
  '/app/trip/rx-travel-detail/pages/index': {
    // --- FCP模板配置
    enableFcpLazyCss: true,
    // 远程基础顶部html，和发布关联上传+预热cdn
    remoteKey: 'rx-travel-detail',
    // fcp渲染内容
    remoteCacheUrl: 'https://traveldetail.feizhu.com/detailsnapshot',
    // 动态渲染模板，@ali/mpi-detailer-screen-parse的编译结果
    templateToken: detailFcpToken,
    // templateUrl: string, 远程模版地址
    // --- FSP数据配置
    // 每次访问的数据缓存根据url什么字段区分存储，
    cacheKey: ['id', 'itemId', 'itemid', 'item_id'],
    remoteCacheHtmlUrl: 'https://traveldetail.feizhu.com/htmlcache_detail',
    fcpEndScript: 'https://o.alicdn.com/mpi/detail-fcp-prepare/index.js',
    // 兜底模板数据
    fcpDefaultData: {
      action: [
        ['客服', 'https://gw.alicdn.com/imgextra/i1/O1CN011eO3fb23lcO6TstkM_!!6000000007296-2-tps-144-144.png_60x60.jpg'],
        ['店铺', 'https://gw.alicdn.com/imgextra/i1/O1CN01HFXFtC1Q4KImxU4gi_!!6000000001922-2-tps-63-63.png'],
        ['收藏', 'https://gw.alicdn.com/imgextra/i4/O1CN01UFdeG01Ha52oy6Hh4_!!6000000000773-2-tps-84-84.png'],
      ],
    },
    secondSsrTimeout: 300,
    // 在
    fspEndScriptString:
      "(function () {try {var fcpImage = document.getElementById('_ermpic');if (!fcpImage) {return;}var mainPic = fcpImage.getAttribute('src');if (!mainPic) {message('nopic');return;}var cssList = ['.td-banner__erholder{background-image:url(' + mainPic + ')}'];if (fcpImage.previousElementSibling) {var mainPic64 = fcpImage.previousElementSibling.getAttribute('src');if (mainPic64) {cssList.push('.td-banner__erholder64{background-image:url(' + mainPic64 + ')}');}}var styleEl = document.createElement('style');document.body.appendChild(styleEl);styleEl.setAttribute('name', 'er-fcp-end');styleEl.innerHTML = cssList.join('')} catch (err) {}})()",

    /*
    // 指定命中流式csr
    csr: {
      enable: boolean;
      white: {},
      black: {}
    },
    // 指定开启fsp缓存
    fspCacheRule: {
      enable: boolean;
      white: {},
      black: {}
    },
    // 指定开启二段ssr
    secondssr: {
      enable: boolean;
      white: {},
      black: {}
    },
    // 是否在1段ssr成功后仍合并二段ssr(商详不需要一段里有价格，而poi的货架价格在二段里)
    waitSecondSsr: boolean;
     */
  },
  '/app/trip/rx-travel-order-detail/pages/detail': {
    cacheKey: ['orderId'],
    remoteKey: 'rx-travel-order-detail',
    remoteCacheHtmlUrl: 'https://traveldetail.feizhu.com/htmlcache_orderdetail',
    useLogin: 1,
    useMonky: 0,
  },
  '/app/trip/rx-travel-search/pages/list': {
    cacheKey: false,
    remoteKey: "rx-travel-search",
    templateToken: searchFcpToken,
  },
  '/app/trip/rx-trip-ticket/pages/detail': {
    cacheKey: ['poiId'],
    remoteKey: "rx-trip-ticket_detail",
    waitSecondSsr: true,
    templateToken: poiFcpToken,
    remoteCacheUrl: 'https://traveldetail.feizhu.com/snapshot_ticketpoi',
    remoteCacheHtmlUrl: 'https://traveldetail.feizhu.com/htmlcache_ticketpoi',
  },
};

export const streamBasicUrl = 'https://traveldetail.feizhu.com/streambasic';

// --- html解析
// ssr内容起点
export const streamRootStartReg = /\<div\s+id=['"]root['"]/g; // '<div id="root"'

// Faas数据起点
export const streamFaasStartReg = /\<script\s+data-from=['"]server['"]/g; // <script data-from="server"

// 业务脚本位置
export const streamBizScriptStartReg = /\<script\s+src=['"]/g; // <script src="

// --- window 上挂载的变量
// ------ 数据下发
// debug模式下，日志信息
export const streamWinKey_DebugInfo = 'window._er_debug';
// 透传er统计的性能数据
export const streamWinKey_PerfInfo = 'window._er_perf';

// ------ 框架标记
// 标记fcp结束时间点
export const streamWinKey_FcpEndTimestamp = 'window._er_firstscreen';
// 标记从fcp结束点到fsp结束点之间过了多久
export const streamWinKey_FcpToFspCostTime = 'window._er_streamgap';
// 标记fspcache可见时间点
export const streamWinKey_FspCacheViewEndTime = 'window._er_fspcacheendtime';
// 标记fspcache+二段ssr替换成功时间点
export const streamWinKey_FspCacheViewReplaceStatus = 'window._er_fspcachereplacetime';
// 标记fsp是否渲染成功，告知兜底重定向不跳转
export const streamWinKey_FspSuccess = 'window._er_fsp_success';
// 标记er透传数据，包含fcp数据中featrue字段 + er上解析端结果
export const streamWinKey_FeatrueData = 'window._er_featrue';
// fspcache模式下，标记成功启用了缓存
export const streamWinKey_FspCacheSuccess = 'window._er_fspcache';
// fspcache模式下，标记当前ssr的数据是静态缓存中的兜底的
export const streamWinKey_FspCacheDataEnable = 'window._er_fspcachedata';
// 标记当前是同域名下csr
export const streamWinKey_CsrRender = 'window._er_csr';
// 隐藏fcp视图的api
export const streamWinKey_FspClose = 'window._er_closefcp';

// ------ 生命周期钩子
// fspcache模式下，约定fsp视图渲染结尾调用的函数
export const streamWinKey_Hook_FspCacheEnd = 'window._er_fspcache_end';
// fspcache模式下，约定fsp数据渲染结尾调用的耗时（业务js执行之前）
export const streamWinKey_Hook_FspCacheDataEnd = 'window._er_fspcachedata_end';
// 实时ssr模式下，视图之后调用
export const streamWinKey_Hook_FspOnlineEnd = 'window._er_fsponline_end';
// fsp降级为csr渲染时钩子调用
export const streamWinKey_Hook_FspCsrStart = 'window._er_fspcsr_start';

// --- html内容约定
// fcp渲染内容
export const streamTagId_fcp = '_er-fcp';

export const streamTagId_fspcachestyle = '_fsp-cache-style';

// fcp渲染阶段preload，但在结束后要正常应用的css
export const streamTagName_preloadcss = 'fcp-pcss';

// fcp渲染完成后，需要删除的冗余css
export const streamTagName_removecss = 'fcp-rcss';

// html中解析出页面版本号的meta标签的name属性值
export const streamMetaName_Version = 'alitrip-project-version';
// 静态缓存上传时间点
export const streamMetaName_CacheTime = 'atomcache';
// 静态缓存自定义过期时间
export const streamMetaName_ExpireTime = 'atomexpire';

// --- 框架插入
// 流式渲染的基本dom元素，缺少会无法上屏
export const streamBaseTemplate =
  '<style>@font-face{font-family:AlibabaSans102;src:url("https://g.alicdn.com/trip/common-assets/1.0.2/fonts/FliggySans102-Bd.ttf") format("truetype");}</style><div style="font-family:AlibabaSans102;width:0;height:0;font-size:16px;overflow:hidden">123</div><div style="position:fixed;overflow:hidden;top:0;left:0;pointer-events:none;"><div style="font-size:0">%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D</div><img src="https://gw.alicdn.com/imgextra/i1/O1CN01MwAFGF1wu8c1qURA5_!!6000000006367-2-tps-42-42.png" style="opacity:0;width:100vw;position:absolute"/></div>';

// ------ 框架脚本
// 停止FCP中注入的超时监测降级
const streamScript_StopFcpBack = `${streamWinKey_FspSuccess}=true;`;
// 隐藏FCP渲染结果
const streamScript_InitHideFcpApi = `${streamWinKey_FspClose}=function (){ (function(els){var len=els.length;for(var i=0;i<len;i++){try {var el=document.getElementById(els[i]);if(el){el.style.display="none"}}catch(err){ }}})(["${streamTagId_fcp}"]) };`;
// fsp有效时间点（区间值）
export const streamScript_FspValidTime = `if(!${streamWinKey_FcpToFspCostTime}){${streamWinKey_FcpToFspCostTime}= Date.now() - ${streamWinKey_FcpEndTimestamp};}`;
// 流式渲染fcp结尾脚本，记录耗时 + 恢复head中preload的css
export const streamScript_FcpRenderEnd = `(function () {var links = document.getElementsByTagName('link');for (var j = 0, len = links.length; j < len; j++) {var l = links[j];var name = l.getAttribute('name');if (l.href && name) {if (name == "${streamTagName_preloadcss}") {l.setAttribute('rel', 'stylesheet'); } else if (name == "${streamTagName_removecss}") {setTimeout(function (){ l.remove(); }, 500)}}}})()`;
// 流式渲染fsp结尾脚本，隐藏fsp+记录耗时
export const streamScript_CloseFcp = `${streamScript_StopFcpBack};${streamScript_InitHideFcpApi};${streamWinKey_FspClose}();`;
// 流式渲染fsp缓存模式结束，删除开始阶段注入的样式，展示隐藏模块(模块应已被js业务处理完毕)
export const streamScript_FspCacheEnd = `(function(els){var len=els.length;for(var i=0;i<len;i++){try {var el=document.getElementById(els[i]);if(el){el.remove()}}catch(err){ }}})(["${streamTagId_fspcachestyle}"])`;
// 渲染csr阶段开始，不能直接隐藏fcp，交由业务脚本手动执行
export const streamScript_CsrRenderStart = `${streamScript_StopFcpBack};${streamScript_InitHideFcpApi};setTimeout(function (){${streamWinKey_FspClose}();},3000)`;

// ------ 框架样式
// fsp缓存场景，通过注入样式隐藏敏感模块
export const streamCss_FspCacheStart = '.td-erhide{opacity:0 !important;}.td-erdelete{display:none;!important;}';

// 底部垫高距离
export const streamStyle_BottomFit = '5.8667vw';
