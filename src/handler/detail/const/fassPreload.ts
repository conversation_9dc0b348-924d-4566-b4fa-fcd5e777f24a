// 透传给faas预加载的url参数，https://aliyuque.antfin.com/remtwr/sh17vq/ynkcql5p5d0hhtdg#ufG3Y
// 触发点可能在tracklink/utils等组件内
export const FAAS_PRELOAD_QUERY = [
  // 开关
  '_pressr',
  // 自定义缓存key
  '_fz_cli_cache_key',
  // 一些配置参数
  '_preMaxAge', // 缓存时长
  '_preNotClear', // 禁止用后即删，默认是false
  '_deletePre', // 强制删除
  '_preReAge', // ？？？实在没看懂
].reduce((memo, n) => {
  memo[n] = 1;
  return memo;
}, {});

export function isPreloadFaas(query) {
  if (query._pressr) {
    return true;
  }
}

// ssr预加载场景，多ssr并行时用相似但不同的cache_key
export function getMultiSsrPreloadQuery(query, index) {
  if (query._fz_cli_cache_key) {
    return {
      _fz_cli_cache_key: `${query._fz_cli_cache_key}_ssr${index}_`,
    };
  }
  return {};
}
