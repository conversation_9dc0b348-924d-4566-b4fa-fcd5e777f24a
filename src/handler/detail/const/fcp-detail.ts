export default [
    [
        "tag",
        [
            [
                "text",
                [
                    "::-webkit-scrollbar{display:none;width:0;height:0;color:transparent;}._main{position:fixed;overflow:hidden;height:100vh;width:100vw;z-index:9999;top:0;left:0;right:0;bottom:0;opacity:1;}._body{background:#f3f3f3;font-size:0;height:100%;width:100%;overflow:hidden;}._hold{background-color:#ececec;opacity:0.5;border-radius:1.6vw}._imggroup{padding-top:2.4vw;padding-bottom:2.4vw;background-color:#fff;margin:2.4vw;margin-bottom:0;border-radius:1.6vw;box-sizing:border-box;overflow:hidden;}._imghold{width:95.2vw;object-fit:contain;}._card{background-color:#fff;padding:3.2vw;margin:2.4vw;margin-bottom:0;border-radius:1.6vw;box-sizing:border-box;}._frow{display:-webkit-flex;display:-moz-box;display:flex;flex-direction:row;-webkit-flex-direction:row;-moz-box-orient:horizontal;-moz-box-direction:normal;-webkit-align-items:center;-moz-box-align:center;align-items:center;}._fcol{flex-direction:column;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;}._fjc{display:flex;-webkit-justify-content:center;justify-content:center;-moz-box-pack:center;}._fac{display:flex;-webkit-align-items:center;-moz-box-align:center;align-items:center;}._fill{flex:1;-webkit-flex:1;-moz-box-flex:1;}._overflow{display:block;max-width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}._overflow2{display:block;display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-line-clamp:2;-webkit-box-orient:vertical;}._esl{font-size:2.93333vw;color:#919499;line-height:4.8vw;}._ecop{color:#f53;font-size:3.2vw;line-height:5.33333vw;}._ecop2{color:rgb(255,85,51);border-radius:.8vw;flex-direction:row;align-items:center;justify-content:center;line-height:0;height:4vw;font-size:3.2vw;line-height:4.26667vw;overflow:hidden;}._dprice-f25{margin-top:0;padding-bottom:0;overflow:hidden;}"
                ]
            ]
        ],
        "style"
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._erpic{height:100vw;width:100vw;object-fit:cover;position:relative;z-index:0;}._erpic0{position:absolute;left:0;top:0;z-index:1;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "img",
                        {
                            "x-if": "pic64",
                            "src": "{{pic64}}",
                            "class": "_erpic"
                        }
                    ],
                    [
                        "tag",
                        [],
                        "img",
                        {
                            "id": "_ermpic",
                            "x-if": "pic",
                            "src": "{{pic}}",
                            "class": "_erpic _erpic0"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_erpic"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Mpic"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._equick{height:9.6vw;position:relative;z-index:2;}._equick-b{position:relative;overflow:hidden;border-radius:3.2vw;background:rgba(0,0,0,0.3);padding:0.53333vw;}._equick-t{padding-left:1.6vw;padding-right:1.6vw;align-items:center;justify-content:center;height:5.33333vw;border-radius:2.66667vw;font-weight:bolder;color:#fff;overflow:hidden;font-size:2.93333vw;line-height:5.33333vw;}._equick-1{background:#FFE033;color:#0F131A;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "{{$.text}}"
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "x-if": "$.index",
                                            "class": "_equick-t"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "{{$.text}}"
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "x-else": "",
                                            "class": "_equick-t _equick-1"
                                        }
                                    ]
                                ],
                                "Fragment",
                                {
                                    "x-for": "quick"
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_frow _fjc _equick-b"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_frow _fjc _equick"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Quick"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            ".erate-txt{margin-left:1.6vw;color:#919499;font-size:3.2vw;line-height:4.26667vw;font-weight:400;height:4.26667vw;}.erate-img{width:4.26667vw;height:4.26667vw;object-fit:cover;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "img",
                                {
                                    "class": "erate-img",
                                    "src": "{{$[1]}}"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{$[0]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": "erate-txt"
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-for": "suffix",
                            "class": "_frow _fill _fjc"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_frow",
                    "style": "padding:3.46667vw 0 3.2vw;"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Rate"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._erate25{color:#0F131A;font-size:3.2vw;line-height:4.26667vw;margin-bottom:2.4vw;}._erate25-n{color:#fff;background:#66f;height:4.8vw;font-weight:bold;font-size:3.73333vw;line-height:4.8vw;padding-left:1.33333vw;padding-right:1.33333vw;border-radius:1.6vw;margin-right:0.8vw;}._erate25-l{color:#66f;}._erate25-m{color:#0F131A;margin-left:1.33333vw;padding-left:1.33333vw;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{frate[0]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_erate25-n _frow _fjc"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{frate[1]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_erate25-l"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{frate[2]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_erate25-m _fill _overflow"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{frate[3]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_erate25-r"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_frow _erate25"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Rate25"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._efrank{height:8vw;border-radius:1.6vw;background:linear-gradient(90deg,#fef5ec,#fff);padding-left:1.86667vw;margin-bottom:2.4vw;}._efrank-i{width:3.4244vw;height:3.6vw;object-fit:cover;margin-right:2.4vw;}._efrank-t{color:#805540;font-size:3.2vw;line-height:3.73333vw;flex:1;padding-right:1.06667vw;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "img",
                        {
                            "src": "{{frank[0]}}",
                            "class": "_efrank-i"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{frank[1]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_efrank-t"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_frow _efrank"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Rank25"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._btg{padding-bottom:3.2vw;font-size:3.2vw;color:#abaeb3;line-height:4.26667vw;overflow:hidden;}._btg-i{height:16.26667vw;overflow:hidden;height:100%;}._btg-irow{height:5.33333vw;-webkit-justify-content:flex-start;justify-content:flex-start;-moz-box-pack:center;}._btg-t{font-size:3.46667vw;color:#292c33;margin:1.33333vw;}._btg-m{height:4.26667vw;width:4.26667vw;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "img",
                                {
                                    "class": "_btg-m",
                                    "src": "{{$[0]}}"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{$[1]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_btg-t _overflow"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{$[2]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": ""
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-for": "baseTag.list",
                            "class": "_f{{baseTag.type}} _btg-i{{baseTag.type}} _fill _fjc _fac _btg-i"
                        }
                    ]
                ],
                "div",
                {
                    "x-if": "baseTag.type",
                    "class": "_frow _btg"
                }
            ]
        ],
        "Template",
        {
            "x-name": "BaseTag"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._ebillion{height:4.26667vw;padding-right:1.86667vw;font-size:3.2vw;color:#0f131a;overflow:hidden;margin-bottom:2.4vw;}._ebillion-p{color:#0f131a;margin-right:3.2vw;padding-left:0.8vw;font-size:3.2vw;line-height:4.26667vw;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "img",
                        {
                            "x-if": "brand[0]",
                            "src": "{{brand[0]}}",
                            "style": "object-fit: contain;height:4.26667vw;border-radius: 0.66667vw;"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{brand[1]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_ebillion-p _fill _overflow",
                            "style": "background: {{brand[2]}}"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_frow _ebillion"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Brand"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._etoga{background:#fdeff3;height:8vw;padding-left:3.2vw;padding-right:3.2vw;font-size:3.2vw;color:#0f131a;overflow:hidden;border-radius:1.6vw;margin-bottom:2.4vw;}._etoga-p{color:#ff394d;margin-right:3.2vw;}._etoga-s{flex:1;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{togather[0]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_etoga-p"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{togather[1]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_etoga-s _overflow"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_frow _etoga"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Togather"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._erbr{padding-left:2.4vw;padding-right:3.46667vw;padding-top:1.73333vw;padding-bottom:1.73333vw;line-height:4.53333vw;font-size:3.46667vw;}._erbr0{margin-right:0.8vw;color:#292c33;font-weight:500;}._erbr1{flex:1;color:#666;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "img",
                                {
                                    "src": "{{brule[0]}}",
                                    "style": "height:3.46667vw;vertical-align: middle;line-height: 4.26667vw;object-fit:contain;"
                                }
                            ],
                            [
                                "tag",
                                [],
                                "div",
                                {
                                    "style": "margin-left:1.6vw;margin-right:1.6vw;width:1px;height:3.46667vw;background: #f53;"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{brule[1]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_overflow",
                                    "style": "flex:1;color:#f53;font-size: 3.2vw;line-height: 4.26667vw;font-weight:500;"
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_frow",
                            "style": "padding-left: 2.4vw;padding-right: 2.4vw;height:8vw;border-radius: 1.33333vw;background:linear-gradient(90deg,#fed8d3,#fff6ed);overflow: hidden;"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "{{$[0]}}"
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "class": "_erbr0"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "{{$[1]}}"
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "class": "_erbr1 _overflow"
                                        }
                                    ]
                                ],
                                "div",
                                {
                                    "x-for": "bruleList",
                                    "class": "_erbr _frow"
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-if": "bruleList",
                            "style": "margin-top: 2.4vw;padding-top: 1.6vw;padding-bottom: 1.6vw;background:#f6f6f6;border-radius: 1.6vw;"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_card"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Brule"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._ttag{flex-wrap:wrap;overflow:hidden;max-height:4.8vw;margin-bottom:2.93333vw;}._ttag-i{box-sizing:border-box;height:4.26667vw;line-height:4.53333vw;font-size:3.2vw;padding-left:0.8vw;padding-right:0.8vw;margin-right:0.8vw;margin-bottom:0.8vw;position:relative;color:#5c5f66;vertical-align:middle;}._ttag-b{box-sizing:border-box;border-width:1px;border-color:rgba(92,95,102,0.5);border-style:solid;position:absolute;border-radius:1.6vw;left:0;top:0;width:200%;height:200%;transform:scale(0.5);}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "img",
                        {
                            "x-if": "prefix[0]",
                            "src": "{{prefix[0]}}",
                            "style": "height:4.8vw;object-fit:contain;margin-right:.8vw"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{prefix[1]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_esl _fill",
                            "style": "margin-right:1.6vw;"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{prefix[2]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_esl"
                        }
                    ]
                ],
                "div",
                {
                    "x-if": "prefix",
                    "class": "_frow",
                    "style": "height:5.86667vw;"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{title}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "style": "display: inline;color:#0f131a;word-break: break-all;font-weight:700;line-height: 5.86667vw;font-size:4.26667vw;"
                        }
                    ]
                ],
                "div",
                {
                    "x-if": "title",
                    "class": "_overflow2",
                    "style": "margin-top:1.33333vw;margin-bottom:2vw;line-height: 5.86667vw;font-size:4.26667vw;"
                }
            ],
            [
                "tag",
                [],
                "div",
                {
                    "x-else": "",
                    "class": "_hold",
                    "style": "margin-top:2.66667vw;height: 10.66667vw;width: 100%;"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{$.text}}"
                                ]
                            ],
                            [
                                "tag",
                                [],
                                "div",
                                {
                                    "class": "_ttag-b",
                                    "style": "transform-origin: left top"
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-for": "ftags",
                            "class": "_ttag-i"
                        }
                    ]
                ],
                "div",
                {
                    "x-if": "ftags",
                    "class": "_frow _ttag"
                }
            ],
            [
                "tag",
                [],
                "Brand",
                {
                    "x-if": "billion",
                    "brand": "billion"
                }
            ],
            [
                "tag",
                [],
                "Brand",
                {
                    "x-if": "fserver",
                    "brand": "fserver"
                }
            ],
            [
                "tag",
                [],
                "Brand",
                {
                    "x-if": "brandPoint",
                    "brand": "brandPoint"
                }
            ],
            [
                "tag",
                [],
                "BaseTag",
                {
                    "x-if": "baseTag"
                }
            ],
            [
                "tag",
                [],
                "Rate25",
                {
                    "x-if": "frate[0]"
                }
            ],
            [
                "tag",
                [],
                "Rank25",
                {
                    "x-if": "frank"
                }
            ],
            [
                "tag",
                [],
                "Togather",
                {
                    "x-if": "togather"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "Rate",
                        {
                            "x-if": "suffix"
                        }
                    ]
                ],
                "Fragment",
                {
                    "x-if": "!featrue.fy25"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Title"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._erpd{padding-top:1.86667vw;}._erpd0{color:rgb(255,85,51);background:rgb(255,247,242);align-items:flex-start;}._erpd1{height:4.8vw;width:4.8vw;line-height:4.8vw;object-fit:contain;vertical-align:middle;margin-right:1.06667vw;}._erpd2{flex:1;font-size:3.2vw;line-height:4.8vw;white-space:wrap;margin-right:1.6vw;}._erpd3+._erpd3{margin-top:1.6vw;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "div",
                        {
                            "style": "height:5.6vw;width: 13.33333vw;margin: 0 0.8vw;"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_frow _fac",
                    "style": "height:6.13333vw;width:26.66667vw;"
                }
            ],
            [
                "tag",
                [],
                "div",
                {
                    "x-for": "prices",
                    "style": "height: 4vw;width:26.66667vw;margin-top: 2.4vw;"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [],
                                        "Image",
                                        {
                                            "x-if": "$[0]",
                                            "class": "_erpd1",
                                            "src": "{{$[0]}}"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "{{$[1]}}"
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "x-if": "$[1]",
                                            "class": "_erpd2"
                                        }
                                    ]
                                ],
                                "div",
                                {
                                    "x-if": "$[0]",
                                    "class": "_erpd0 _erpd3 _frow",
                                    "style": "padding:0.4vw 2vw;{{$[3]}}"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{$[1]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "x-else": "",
                                    "class": "_erpd2 _erpd3",
                                    "style": "{{$[3]}}"
                                }
                            ]
                        ],
                        "Fragment",
                        {
                            "x-for": "pdesc"
                        }
                    ]
                ],
                "div",
                {
                    "x-if": "pdesc",
                    "class": "_erpd"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "加载中"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_frow _ecop _fill",
                            "style": "color:#0f131a;font-weight:500;justify-content:flex-end"
                        }
                    ]
                ],
                "div",
                {
                    "x-if": "couponTag",
                    "class": "_frow",
                    "style": "margin-top: 1.6vw;"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "Title"
                    ]
                ],
                "div",
                {
                    "style": "padding-top: 2.13333vw;"
                }
            ]
        ],
        "Template",
        {
            "x-name": "DailyPrice"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._erpdp{background-image:url({{e.promBg}});background-size:100%;background-repeat:no-repeat;width:100vw;position:relative;}._erpdp-f25{margin-top:-9.6vw;padding-top:7.46667vw;}._erpdp0{color:rgb(255,85,51);align-items:flex-start;}._erpdp1{height:4.8vw;width:4.8vw;line-height:4.8vw;object-fit:contain;vertical-align:middle;margin-right:1.06667vw;}._erpdp2{flex:1;font-size:3.2vw;line-height:4.8vw;white-space:wrap;margin-right:1.6vw;}._erpdp3+._erpdp3{margin-top:1.6vw;}._erpdp4{position:relative;z-index:2;padding-bottom:0;overflow:hidden;}._erpdp5{height:100%;font-size:3.2vw;line-height:4.8vw;padding-left:3.2vw;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "div",
                        {
                            "style": "position: absolute;left:0;right:0;bottom:0;top: 29.33333vw;height:13.33333vw;z-index:0;background-image: linear-gradient(180deg,transparent,#fff 50%,#f0f0f0);"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "div",
                                {
                                    "class": "_frow",
                                    "style": "margin-left: 2.4vw;height:22.66667vw;color:#fff;font-size: 3.2vw;line-height: 3.2vw;"
                                }
                            ],
                            [
                                "tag",
                                [],
                                "img",
                                {
                                    "x-if": "e.promIcon",
                                    "src": "{{e.promIcon}}",
                                    "style": "position: absolute;top:0;right: 1.33333vw;width: 29.6vw;object-fit: contain;"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [],
                                                        "Image",
                                                        {
                                                            "x-if": "$[0]",
                                                            "class": "_erpdp1",
                                                            "src": "{{$[0]}}"
                                                        }
                                                    ],
                                                    [
                                                        "tag",
                                                        [
                                                            [
                                                                "text",
                                                                [
                                                                    "{{$[1]}}"
                                                                ]
                                                            ]
                                                        ],
                                                        "div",
                                                        {
                                                            "x-if": "$[1]",
                                                            "class": "_erpdp2"
                                                        }
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "x-if": "$[0]",
                                                    "class": "_erpdp0 _erpdp3 _frow",
                                                    "style": "{{$[3]}}"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [
                                                            [
                                                                "text",
                                                                [
                                                                    "{{$[1]}}"
                                                                ]
                                                            ]
                                                        ],
                                                        "div",
                                                        {
                                                            "class": "_erpdp2 _erpdp3",
                                                            "style": "{{$[3]}}"
                                                        }
                                                    ],
                                                    [
                                                        "tag",
                                                        [
                                                            [
                                                                "text",
                                                                [
                                                                    "{{$[2]}}"
                                                                ]
                                                            ]
                                                        ],
                                                        "div",
                                                        {
                                                            "class": "_erpdp5",
                                                            "style": "{{$[3]}}"
                                                        }
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "x-else": "",
                                                    "class": "_frow",
                                                    "style": "align-items: stretch;-webkit-align-items: stretch"
                                                }
                                            ]
                                        ],
                                        "Fragment",
                                        {
                                            "x-for": "pdesc"
                                        }
                                    ]
                                ],
                                "div",
                                {
                                    "x-if": "pdesc",
                                    "class": "_erpdp4",
                                    "style": "margin:0 2.4vw 2.4vw;background-color: rgb(255, 255, 255); border-radius: 1.6vw;padding: 2.4vw 3.2vw;"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [],
                                        "div",
                                        {
                                            "class": "_fill"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "加载.."
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "x-if": "e.promBtn",
                                            "class": "_frow",
                                            "style": "padding-left:2.13333vw;font-size: 3.46667vw;background-image:url({{e.promBtn}});color:#fff;width:11.06667vw;height:5.06667vw;background-repeat:no-repeat;background-size:100% 100%;"
                                        }
                                    ]
                                ],
                                "div",
                                {
                                    "x-if": "couponTag",
                                    "class": "_frow _ecop _erpdp4",
                                    "style": "background: #fff;line-height: 4.26667vw;margin: 0 2.4vw;padding: 1.73333vw 0 1.73333vw 3.2vw;border-radius: 1.6vw;"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [],
                                        "Title"
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_card _erpdp4",
                                    "style": "padding-top: 3.2vw;"
                                }
                            ]
                        ],
                        "div",
                        {
                            "style": "position: relative;"
                        }
                    ]
                ],
                "Template",
                {
                    "x-name": "PromPriceContent"
                }
            ],
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "{{e.mrowText}}"
                        ]
                    ]
                ],
                "div",
                {
                    "x-if": "e.mrowText",
                    "class": "_overflow",
                    "style": "background-image: url({{e.mrowBg}});background-size:100%;background-repeat:no-repeat;width:100vw;height:8vw;box-sizing: border-box;line-height: 8vw;color:#e9c18b;font-size: 3.46667vw;font-weight: 700;padding:0 3.2vw 0 3.73333vw;"
                }
            ],
            [
                "tag",
                [],
                "Quick",
                {
                    "x-if": "quick"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "PromPriceContent"
                    ]
                ],
                "div",
                {
                    "x-if": "quick",
                    "class": "_erpdp _erpdp-f25"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "PromPriceContent"
                    ]
                ],
                "div",
                {
                    "x-else": "",
                    "class": "_erpdp"
                }
            ]
        ],
        "Template",
        {
            "x-name": "PromPrice"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._emr{position:relative;background-image:url({{member.bg}});background-size:100%;background-repeat:no-repeat;width:100vw;}._emr-f25{margin-top:-9.6vw;padding-top:7.46667vw;}._emr-txt{text-align:center;font-size:4vw;line-height:5.6vw;}._emr-txt2{text-align:center;font-size:2.66667vw;line-height:4.26667vw;}._erpdp0{color:rgb(255,85,51);align-items:flex-start;}._erpdp1{height:4.8vw;width:4.8vw;line-height:4.8vw;object-fit:contain;vertical-align:middle;margin-right:1.06667vw;}._erpdp2{flex:1;font-size:3.46667vw;line-height:4.8vw;white-space:wrap;margin-right:1.6vw;}._erpdp3+._erpdp3{margin-top:1.6vw;}._erpdp4{position:relative;z-index:2;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [],
                                                "img",
                                                {
                                                    "src": "{{member.f3f4}}",
                                                    "style": "object-fit: contain;height: 3.46667vw;margin-bottom: 1.6vw;"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "text",
                                                        [
                                                            "{{member.copy}}"
                                                        ]
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_emr-tx2t"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "text",
                                                        [
                                                            "{{member.gap}}"
                                                        ]
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_emr-txt2"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "text",
                                                        [
                                                            "{{member.name}}"
                                                        ]
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_emr-txt2"
                                                }
                                            ]
                                        ],
                                        "Fragment",
                                        {
                                            "x-if": "member.f3f4"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "text",
                                                        [
                                                            "{{member.seller}}"
                                                        ]
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_emr-txt"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "text",
                                                        [
                                                            "{{member.desc}}"
                                                        ]
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_emr-txt"
                                                }
                                            ]
                                        ],
                                        "Fragment",
                                        {
                                            "x-else": ""
                                        }
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_fcol _fjc _fac",
                                    "style": "position:absolute;right: 2.13333vw;top:50%;transform: translateY(-50%);width:29.33333vw;color: #f2c191;"
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_frow",
                            "style": "position: relative;;margin-left: 2.4vw;height:22.13333vw;color:#fff;font-size: 3.2vw;line-height: 3.2vw;"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [],
                                                "Image",
                                                {
                                                    "x-if": "$[0]",
                                                    "class": "_erpdp1",
                                                    "src": "{{$[0]}}"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "text",
                                                        [
                                                            "{{$[1]}}"
                                                        ]
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "x-if": "$[1]",
                                                    "class": "_erpdp2"
                                                }
                                            ]
                                        ],
                                        "div",
                                        {
                                            "x-if": "$[0]",
                                            "class": "_erpdp0 _erpdp3 _frow",
                                            "style": "{{$[3]}}"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "{{$[1]}}"
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "x-else": "",
                                            "class": "_erpdp2 _erpdp3",
                                            "style": "{{$[3]}}"
                                        }
                                    ]
                                ],
                                "Fragment",
                                {
                                    "x-for": "pdesc"
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-if": "pdesc",
                            "class": "_erpdp4",
                            "style": "margin:0 2.4vw 2.4vw;background-color: rgb(255, 255, 255); border-radius: 1.6vw;padding: 1.6vw 3.2vw;"
                        }
                    ],
                    [
                        "tag",
                        [],
                        "div",
                        {
                            "style": "position: absolute;left:0;right:0;bottom:0;top: 29.33333vw;height:13.33333vw;z-index:0;background-image: linear-gradient(to bottom, transparent, #fff 60%, #F0F0F0);"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "text",
                                                [
                                                    "加载中"
                                                ]
                                            ]
                                        ],
                                        "div",
                                        {
                                            "class": "_frow _ecop _fill",
                                            "style": "justify-content:flex-end;color:#0f131a;font-weight:500;"
                                        }
                                    ]
                                ],
                                "div",
                                {
                                    "x-if": "couponTag",
                                    "class": "_frow",
                                    "style": "margin-bottom: 2.13333vw;"
                                }
                            ],
                            [
                                "tag",
                                [],
                                "Title"
                            ]
                        ],
                        "div",
                        {
                            "class": "_card",
                            "style": "position: relative;z-index: 2;margin-top: 0;overflow: hidden;padding-bottom: 0;"
                        }
                    ]
                ],
                "Template",
                {
                    "x-name": "MemberContent"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "MemberContent"
                    ]
                ],
                "div",
                {
                    "x-if": "quick",
                    "class": "_emr _emr-f25"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "MemberContent"
                    ]
                ],
                "div",
                {
                    "x-else": "",
                    "class": "_emr"
                }
            ]
        ],
        "Template",
        {
            "x-name": "MemberPrice"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "img",
                        {
                            "x-if": "e.prowIcon",
                            "src": "{{e.prowIcon}}",
                            "style": "margin-right: 2.13333vw;width: 7.46667vw;height: 7.46667vw;object-fit: cover;"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{e.prowText}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "x-if": "e.prowText",
                            "class": "_overflow",
                            "style": "font-size: 3.46667vw;line-height: 4.8vw;color: #fff;"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_card _frow",
                    "style": "padding:2.13333vw;background-image: url({{e.prowBg}});background-size:100% 100%;"
                }
            ]
        ],
        "Template",
        {
            "x-name": "PromRow"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._igroup{font-size:3.2vw;line-height:5.33333vw;padding-left:1.6vw;padding-right:1.6vw;box-sizing:border-box;}._info{margin:2.4vw;margin-bottom:0;border-radius:1.6vw;box-sizing:border-box;background-color:#fff;overflow:hidden;padding:1.6vw;padding-top:2.66667vw;padding-bottom:2.66667vw;}._ilabel{min-width:16vw;color:#919499;}._itext{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#0f131a}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "img",
                        {
                            "x-if": "vrule[0]",
                            "src": "{{vrule[0]}}",
                            "style": "object-fit:contain;height:3.2vw;margin-right: 1.6vw;vertical-align: middle;"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "text",
                                [
                                    "{{vrule[1]}}"
                                ]
                            ]
                        ],
                        "div",
                        {
                            "class": "_overflow",
                            "style": "color:#805540;line-height: 3.2vw;"
                        }
                    ]
                ],
                "div",
                {
                    "x-if": "vrule",
                    "class": "_info _frow _igroup",
                    "style": "padding:3.2vw;height:9.6vw"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{$[0]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_ilabel"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{$[1]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_itext"
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-for": "$g",
                            "class": "_frow _igroup"
                        }
                    ]
                ],
                "div",
                {
                    "x-for": "info",
                    "x-item": "$g",
                    "class": "_info"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Info"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "img",
                                {
                                    "src": "{{mrule[0]}}",
                                    "style": "height:4vw;vertical-align: middle;line-height: 4.26667vw;object-fit:contain;"
                                }
                            ],
                            [
                                "tag",
                                [],
                                "div",
                                {
                                    "style": "margin-left:1.6vw;margin-right:1.6vw;width:1px;height:3.46667vw;background: #f53;"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{mrule[1]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_overflow",
                                    "style": "flex:1;color:#ff8c1a;font-size: 3.46667vw;line-height: 4.26667vw;font-weight:700;"
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_frow",
                            "style": "border-radius: 1.6vw;padding-left: 2.4vw;padding-right: 2.4vw;height:11.46667vw;background:#fff5eb;overflow: hidden;"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_card",
                    "style": "background:#fff;padding:0;"
                }
            ]
        ],
        "Template",
        {
            "x-name": "Mrule"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            "._emall{overflow:hidden;border-radius:2.4vw;}._emall-card{background:url(https://gw.alicdn.com/imgextra/i1/O1CN01RzTLDT1bI1mjvDFpO_!!6000000003441-2-tps-714-191.png);background-position:50%;background-size:cover;margin-bottom:-7.33333vw;box-sizing:border-box;position:relative;height:25.46667vw;z-index:0;}._emall-logo{position:absolute;right:0;top:50%;transform:translateY(-50%);z-index:0;}._emall-info{border-radius:2.4vw;background:#fff;position:relative;z-index:1;}._emall-title{font-size:4vw;line-height:6vw;color:#0f131a;font-weight:600;}._emall-sold{font-size:3.2vw;line-height:3.73333vw;margin-top:3.2vw;color:#919499;}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [],
                                        "img",
                                        {
                                            "class": "_emall-logo",
                                            "style": "width:27.4109vw;height:4.5333vw;object-fit:cover;",
                                            "src": "https://gw.alicdn.com/imgextra/i3/O1CN01jhQMRj1tylpgYK86i_!!6000000005971-2-tps-260-43.png"
                                        }
                                    ]
                                ],
                                "div",
                                {
                                    "style": "position: relative;width:100%"
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_emall-card _frow",
                            "style": "padding: 0 3.2vw 7.33333vw;"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{title}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "class": "_emall-title"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{prefix[2]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "x-if": "prefix[2]",
                                    "class": "_emall-sold"
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_emall-info",
                            "style": "padding: 2.66667vw 3.2vw;"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_emall",
                    "style": "margin: 2.4vw 2.4vw 3.2vw;"
                }
            ],
            [
                "tag",
                [],
                "img",
                {
                    "src": "https://gw.alicdn.com/imgextra/i2/O1CN01OsET0f1CVQzDMojXf_!!6000000000086-2-tps-1071-225.png",
                    "mode": "scaleToFill",
                    "style": "margin:0 2.4vw 3.2vw;width:95.2vw;height:20vw;object-fit:cover;"
                }
            ],
            [
                "tag",
                [],
                "img",
                {
                    "src": "https://gw.alicdn.com/imgextra/i1/O1CN01mNSbNV1SqzX4ocbyj_!!6000000002299-2-tps-1071-1799.png",
                    "style": "margin:0 2.4vw 0;width:95.2vw;object-fit: contain;"
                }
            ]
        ],
        "Template",
        {
            "x-name": "MemberMall"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "text",
                        [
                            ".buy-loading{animation-duration:.5s;animation-iteration-count:infinite;-webkit-animation-name:er-buy;-moz-animation-name:er-buy;animation-name:er-buy;-webkit-animation-duration:.5s;-moz-animation-duration:.5s;animation-duration:.5s;-webkit-animation-iteration-count:infinite;-moz-animation-iteration-count:infinite;animation-iteration-count:infinite;width:4vw;height:4vw;background-size:contain;background-repeat:no-repeat;background-image:url(https://gw.alicdn.com/imgextra/i3/O1CN01iPuluj1kAXcTlHqbh_!!6000000004643-2-tps-60-60.png_24x24q20.jpg)}@keyframes er-buy{from{transform:rotate(0)}to{transform:rotate(360deg)}}"
                        ]
                    ]
                ],
                "style"
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "img",
                                {
                                    "src": "{{$[1]}}",
                                    "style": "width:4.8vw;height:4.8vw;object-fit:cover;{{$[2]}}"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "text",
                                        [
                                            "{{$[0]}}"
                                        ]
                                    ]
                                ],
                                "div",
                                {
                                    "style": "margin-top:.8vw;color:#666;font-size:2.66667vw;line-height:4vw"
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-for": "action",
                            "class": "_frow _fcol",
                            "style": "width:13.06667vw;"
                        }
                    ]
                ],
                "Template",
                {
                    "x-name": "Action"
                }
            ],
            [
                "tag",
                [
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [
                                                            [
                                                                "text",
                                                                [
                                                                    "加载中"
                                                                ]
                                                            ]
                                                        ],
                                                        "div",
                                                        {
                                                            "style": "line-height:10.6667vw;margin-right:1.0667vw"
                                                        }
                                                    ],
                                                    [
                                                        "tag",
                                                        [],
                                                        "div",
                                                        {
                                                            "class": "buy-loading",
                                                            "style": "width:4vw;height:4vw"
                                                        }
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_frow _fjc _fill",
                                                    "style": "color:#ffffff;font-size:4.26667vw;background-image:linear-gradient(90deg,#ff7a00,#fe560a);"
                                                }
                                            ]
                                        ],
                                        "div",
                                        {
                                            "class": "_frow _fill",
                                            "style": "font-weight:500;align-items:stretch;border-radius:10.66667vw;overflow:hidden;height:11.2vw;margin-left: 2.13333vw;"
                                        }
                                    ]
                                ],
                                "Fragment",
                                {
                                    "x-if": "isWeChatMiniProgramH5"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [
                                                            [
                                                                "tag",
                                                                [
                                                                    [
                                                                        "text",
                                                                        [
                                                                            "加载中"
                                                                        ]
                                                                    ]
                                                                ],
                                                                "div",
                                                                {
                                                                    "style": "line-height:10.6667vw;margin-right:1.0667vw"
                                                                }
                                                            ],
                                                            [
                                                                "tag",
                                                                [],
                                                                "div",
                                                                {
                                                                    "class": "buy-loading",
                                                                    "style": "width:4vw;height:4vw"
                                                                }
                                                            ]
                                                        ],
                                                        "div",
                                                        {
                                                            "class": "_frow _fjc _fill",
                                                            "style": "color:#0f131a;font-size:4.26667vw;background: #ffe033;"
                                                        }
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_frow _fill",
                                                    "style": "font-weight:500;align-items:stretch;border-radius:10.66667vw;overflow:hidden;height:11.2vw;margin-left: 2.13333vw;"
                                                }
                                            ]
                                        ],
                                        "Fragment",
                                        {
                                            "x-if": "isXhsMiniAppH5"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [],
                                                        "Action",
                                                        {
                                                            "x-if": "action"
                                                        }
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_frow _fill"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [],
                                                        "Fragment",
                                                        {
                                                            "x-if": "sBottom"
                                                        }
                                                    ],
                                                    [
                                                        "tag",
                                                        [
                                                            [
                                                                "text",
                                                                [
                                                                    "加入购物车"
                                                                ]
                                                            ]
                                                        ],
                                                        "div",
                                                        {
                                                            "x-else": "",
                                                            "class": "_frow _fjc _fill",
                                                            "style": "color:hsla(0,0%,100%,.3);font-size:4.26667vw;background-image:linear-gradient(90deg,#ffc500,#ff9402)"
                                                        }
                                                    ],
                                                    [
                                                        "tag",
                                                        [
                                                            [
                                                                "tag",
                                                                [
                                                                    [
                                                                        "text",
                                                                        [
                                                                            "加载中"
                                                                        ]
                                                                    ]
                                                                ],
                                                                "div",
                                                                {
                                                                    "style": "line-height:10.6667vw;margin-right:1.0667vw"
                                                                }
                                                            ],
                                                            [
                                                                "tag",
                                                                [],
                                                                "div",
                                                                {
                                                                    "class": "buy-loading",
                                                                    "style": "width:4vw;height:4vw"
                                                                }
                                                            ]
                                                        ],
                                                        "div",
                                                        {
                                                            "class": "_frow _fjc _fill",
                                                            "style": "color:#fff;font-size:4.26667vw;background-image:linear-gradient(to right,#FF7A00,#FE560A);{{buyStyle}}"
                                                        }
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "class": "_frow",
                                                    "style": "font-weight:500;align-items:stretch;border-radius:10.66667vw;overflow:hidden;max-width:53.6vw;min-width:53.6vw;height:11.2vw"
                                                }
                                            ]
                                        ],
                                        "Fragment",
                                        {
                                            "x-else": ""
                                        }
                                    ]
                                ],
                                "Fragment",
                                {
                                    "x-else": ""
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_frow _fill",
                            "style": "padding:0 3.2vw 0 1.06667vw;"
                        }
                    ]
                ],
                "div",
                {
                    "style": "position:absolute;z-index:5;bottom:0;left:0;background:#fff;width:100vw;display:flex;box-sizing:content-box;padding-bottom:{{iosPadding}};height:14.93333vw;box-shadow:0 0 5px 1px rgba(0,0,0,.03)"
                }
            ]
        ],
        "Template",
        {
            "x-name": "BottomBar"
        }
    ],
    [
        "tag",
        [
            [
                "tag",
                [
                    [
                        "tag",
                        [],
                        "Mpic"
                    ],
                    [
                        "tag",
                        [],
                        "MemberMall",
                        {
                            "x-if": "featrue.mall"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "PromPrice",
                                {
                                    "x-if": "e.promBg"
                                }
                            ],
                            [
                                "tag",
                                [
                                    [
                                        "tag",
                                        [],
                                        "Quick",
                                        {
                                            "x-if": "quick"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [],
                                        "MemberPrice",
                                        {
                                            "x-if": "member"
                                        }
                                    ],
                                    [
                                        "tag",
                                        [
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [],
                                                        "DailyPrice"
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "x-if": "quick",
                                                    "class": "_card _dprice-f25"
                                                }
                                            ],
                                            [
                                                "tag",
                                                [
                                                    [
                                                        "tag",
                                                        [],
                                                        "DailyPrice"
                                                    ]
                                                ],
                                                "div",
                                                {
                                                    "x-else": "",
                                                    "class": "_card"
                                                }
                                            ]
                                        ],
                                        "Fragment",
                                        {
                                            "x-else": ""
                                        }
                                    ]
                                ],
                                "Fragment",
                                {
                                    "x-else": ""
                                }
                            ]
                        ],
                        "Fragment",
                        {
                            "x-else": ""
                        }
                    ],
                    [
                        "tag",
                        [],
                        "PromRow",
                        {
                            "x-if": "e.prowBg"
                        }
                    ],
                    [
                        "tag",
                        [],
                        "Brule",
                        {
                            "x-if": "brule"
                        }
                    ],
                    [
                        "tag",
                        [],
                        "Fragment",
                        {
                            "x-if": "fserver"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "Info",
                                {
                                    "x-if": "info"
                                }
                            ]
                        ],
                        "Fragment",
                        {
                            "x-else": ""
                        }
                    ],
                    [
                        "tag",
                        [],
                        "Mrule",
                        {
                            "x-if": "mrule"
                        }
                    ],
                    [
                        "tag",
                        [],
                        "RouteShelf",
                        {
                            "x-if": "rshelf"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "img",
                                {
                                    "x-for": "$",
                                    "x-item": "item",
                                    "src": "{{item.text}}",
                                    "class": "_imghold"
                                }
                            ]
                        ],
                        "div",
                        {
                            "x-for": "holds",
                            "class": "_imggroup"
                        }
                    ],
                    [
                        "tag",
                        [
                            [
                                "tag",
                                [],
                                "div",
                                {
                                    "class": "_hold",
                                    "style": "height:5.33333vw;width:42.6667vw;margin-bottom:2.66667vw"
                                }
                            ],
                            [
                                "tag",
                                [],
                                "div",
                                {
                                    "style": "height:40vw;width:100%;border-radius:1.6vw;background-color:#fbfaef"
                                }
                            ]
                        ],
                        "div",
                        {
                            "class": "_card"
                        }
                    ]
                ],
                "div",
                {
                    "class": "_body"
                }
            ],
            [
                "tag",
                [],
                "BottomBar"
            ]
        ],
        "div",
        {
            "class": "_main"
        }
    ]
]
