export * from './stream';
export * from './streamRender';
export * from './make-script';
export * from './fassPreload';

// 在请求头中插入版本区分每次构建
export const BUILD_VERSION = process.env.BUILD_TIME;

// 度假独立KV空间
export const VACATION_KV_NS = 'fliggy_vacation';


// ssr服务域名
export const FAAS_HOST_ONLINE = 'raxssr.m.fliggy.com';
// ssr服务预发域名
export const FAAS_HOST_PRE = 'pre-raxssr.wapa.fliggy.com';

// ssr服务域名，独立度假ssr分流
export const FAAS_HOST_VACATION_ONLINE = 'detailssr.m.fliggy.com';
export const FAAS_HOST_VACATION_PRE = 'fl-detail-ssr.pre-fc.alibaba-inc.com';

// 降级csr时域名
export const CSR_HOST_ONLINE = 'market.m.taobao.com';

export const CSR_HOST_PRE = 'market.wapa.taobao.com';

// 特殊csr降级域名
export const CSR_HOST_MAP = {
  // 通用套壳（微信/小红书等）
  'proxy-er.feizhu.com': 'proxy.feizhu.com',
  'pre-proxy-er.feizhu.com': 'pre-proxy.feizhu.com',
  'proxy-er.fzwxxcx.com': 'proxy.fzwxxcx.com',
  'pre-proxy-er.fzwxxcx.com': 'pre-proxy.fzwxxcx.com',
  'proxy-er.fzwxxcx.cn': 'proxy.fzwxxcx.cn',
  'pre-proxy-er.fzwxxcx.cn': 'pre-proxy.fzwxxcx.cn',
  // 高德登录态域名
  'front-traffic-fliggy-er.amap.com': 'front-traffic-fliggy.amap.com',
  'pre-front-traffic-fliggy-er.amap.com': 'pre-front-traffic-fliggy.amap.com',
  'h5-er.fliggy.cn': 'market.m.taobao.com',
};

// 走预发环境的白名单
export const PRE_HOST = [
  'pre-h5-er.fliggy.cn',
  'pre-proxy-er.feizhu.com',
  'pre-proxy-er.fzwxxcx.com',
  'pre-proxy-er.fzwxxcx.cn',
  'pre-front-traffic-fliggy-er.amap.com',
  'outfliggys.wapa.taobao.com'
]


// 一些需要重点关注的url参数
export const IMPORTANT_QUERY = [
  'id',
  'itemId',
  'itemid',
  'item_id',
  'category',
  'categoryId',
  'categoryid',
  'skuId',
  'spuId',
  'poiId',
  'shid',
  'shopId',
  'shopid',
  'fpt',
  'ttid',
  'disableNav',
  'hybrid',
  '_fli_webview',
];

