export default [
  [
    'tag',
    [
      [
        'text',
        [
          '.__er-poi{position:fixed;overflow:hidden;height:100vh;width:100vw;z-index:9999;top:0;left:0;right:0;bottom:0;opacity:1;}.__er-poi-main-img{width:100vw;height:65.6vw;object-fit:cover;}.__er-poi-main-backup{width:100vw;height:65.6vw;background:#f3f3f3;}.__er-poi-base-info{position:absolute;top:56vw;left:0;right:0;height:100vh;overflow:hidden;background:#f3f3f3;color:#0f131a;border-top-left-radius:3.2vw;border-top-right-radius:3.2vw;z-index:2;margin-bottom:3.2vw;}.__er-poi-bi0{display:flex;flex-direction:row;align-items:center;border-radius:5.86667vw;background:#f2f3f5;margin-top:2.4vw;overflow:hidden;padding-top:0.8vw;padding-bottom:0.8vw;padding-left:2.4vw;padding-right:2.4vw;width:fit-content;margin-left:2.4vw;margin-right:2.4vw;}.__er-poi-bi01{margin-right:1.6vw;margin-left:0.26667vw;font-size:2.66667vw;font-weight:500;line-height:3.46667vw;color:#0f131a;}.__er-poi-bi1{display:inline-block;font-size:5.6vw;line-height:6.66667vw;margin-right:1.06667vw;font-weight:500;white-space:pre-wrap;overflow:hidden;text-overflow:ellipsis;margin-top:3.2vw;margin-left:2.4vw;margin-right:2.4vw;}.__er-poi-bi2{display:flex;flex-direction:row;align-items:center;height:4.8vw;background-color:#ebebff;border-radius:1.33333vw;overflow:hidden;width:fit-content;margin-right:1.6vw;}.__er-poi-bi21{width:7.86667vw;height:4.8vw;padding-top:0.26667vw;font-size:3.73333vw;line-height:4.26667vw;padding-top:0.53333vw;text-align:center;color:#fff;background-color:#66f;border-radius:1.33333vw;}.__er-poi-bi2-group{display:flex;flex-direction:row;align-items:center;padding-top:0.93333vw;padding-bottom:0.93333vw;padding-left:0.8vw;padding-right:0.8vw;}.__er-poi-bi22{color:#66f;font-size:2.66667vw;line-height:2.93333vw;font-weight:500;}.__er-poi-bi23{line-height:2.93333vw;font-size:2.66667vw;color:#0f131a;}.__er-poi-bi3{display:flex;flex-direction:row;align-items:center;height:4.8vw;max-width:52vw;margin-left:0.8vw;background-color:#f6ebd8;border-radius:1.33333vw;overflow:hidden;padding-right:1.06667vw;width:fit-content;}.__er-poi-bi31{flex:1;font-size:2.66667vw;color:#805540;text-overflow:ellipsis;white-space:nowrap;max-width:40vw;overflow:hidden;align-items:center;margin-left:0.8vw;}.__er-poi-bi-info1{display:flex;flex-direction:row;align-items:center;margin-top:1.6vw;margin-left:2.4vw;margin-right:2.4vw;}.__er-poi-title{white-space:nowrap;font-size:3.73333vw;line-height:5.33333vw;margin-top:1.6vw;width:90%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-left:2.4vw;margin-right:2.4vw;}.__er-shelf{width:100vw;margin-top:3.2vw;position:relative;border-top-left-radius:3.2vw;border-top-right-radius:3.2vw;background-color:#fff;padding-top:2.4vw;padding-bottom:2.4vw;padding-left:0;z-index:2;}.__er-shelf-header{display:flex;flex-direction:row;}.__er-shelf-content{display:flex;flex-direction:row;justify-content:space-between;}.__er-shelf-content-left{margin-top:14.8vw;}.__er-shelf-content-right{padding-right:2.4vw;}.__er-shelf-content-right-item{width:77.6vw;margin-top:6.53333vw;}.__er-shelf-content-right-item-row{display:flex;flex-direction:row;}.__er-shelf-content-right-item-bottom{display:flex;flex-direction:row;justify-content:flex-end;margin-top:5.33333vw;}.__er-shelf-content-right-item-bottom-left{display:flex;align-items:flex-end;justify-content:flex-end;}.__er-shelf-content-right-text{font-size:4.8vw;color:#d2d4d9;}.__er-placeholder{display:flex;background:#e0e0e0;opacity:0.4;border-radius:0.8vw;justify-content:center;align-items:center;}',
        ],
      ],
    ],
    'style',
  ],
  [
    'tag',
    [
      [
        'tag',
        [
          [
            'tag',
            [
              [
                'tag',
                [],
                'div',
                {
                  class: '__er-placeholder',
                  style: 'width: 11.2vw; height: 7.46667vw; margin-left: 2.4vw',
                },
              ],
              [
                'tag',
                [],
                'div',
                {
                  class: '__er-placeholder',
                  style: 'width: 11.2vw; height: 7.46667vw; margin-left: 2.4vw',
                },
              ],
              [
                'tag',
                [],
                'div',
                {
                  class: '__er-placeholder',
                  style: 'width: 24.13333vw; height: 7.46667vw; margin-left: 2.4vw',
                },
              ],
              [
                'tag',
                [],
                'div',
                {
                  class: '__er-placeholder',
                  style: 'width: 14.4vw; height: 7.46667vw; margin-left: 2.4vw',
                },
              ],
            ],
            'div',
            {
              class: '__er-shelf-header',
            },
          ],
          [
            'tag',
            [
              [
                'tag',
                [
                  [
                    'tag',
                    [],
                    'div',
                    {
                      class: '__er-placeholder',
                      style: 'width: 17.6vw; height: 136.53333vw',
                    },
                  ],
                ],
                'div',
                {
                  class: '__er-shelf-content-left',
                },
              ],
              [
                'tag',
                [
                  [
                    'tag',
                    [
                      [
                        'tag',
                        [],
                        'div',
                        {
                          class: '__er-placeholder',
                          style: 'width: 29.86667vw; height: 7.2vw; margin-bottom: 1.6vw',
                        },
                      ],
                      [
                        'tag',
                        [],
                        'div',
                        {
                          class: '__er-placeholder',
                          style: 'width: 47.73333vw; height: 4.26667vw; margin-bottom: 1.6vw',
                        },
                      ],
                      [
                        'tag',
                        [],
                        'div',
                        {
                          class: '__er-placeholder',
                          style: 'width: 47.73333vw; height: 4.26667vw; margin-bottom: 1.6vw',
                        },
                      ],
                      [
                        'tag',
                        [
                          [
                            'tag',
                            [],
                            'div',
                            {
                              class: '__er-placeholder',
                              style: 'width: 19.86667vw; height: 4.26667vw',
                            },
                          ],
                          [
                            'tag',
                            [],
                            'div',
                            {
                              class: '__er-placeholder',
                              style: 'width: 23.33333vw; height: 4.26667vw; margin-left: 1.06667vw; margin-bottom: 1.6vw',
                            },
                          ],
                          [
                            'tag',
                            [],
                            'div',
                            {
                              class: '__er-placeholder',
                              style: 'width: 19.86667vw; height: 4.26667vw; margin-left: 1.06667vw; margin-bottom: 1.6vw',
                            },
                          ],
                        ],
                        'div',
                        {
                          class: '__er-shelf-content-right-item-row',
                        },
                      ],
                      [
                        'tag',
                        [
                          [
                            'tag',
                            [
                              [
                                'tag',
                                [],
                                'div',
                                {
                                  class: '__er-placeholder',
                                  style: 'width: 14.93333vw; height: 4.26667vw; margin-bottom: 1.6vw',
                                },
                              ],
                              [
                                'tag',
                                [],
                                'div',
                                {
                                  class: '__er-placeholder',
                                  style: 'width: 30.53333vw; height: 4.26667vw; margin-bottom: 1.6vw',
                                },
                              ],
                            ],
                            'div',
                            {
                              class: '__er-shelf-content-right-item-bottom-left',
                            },
                          ],
                          [
                            'tag',
                            [
                              [
                                'tag',
                                [['text', ['订']]],
                                'div',
                                {
                                  class: '__er-shelf-content-right-text',
                                },
                              ],
                            ],
                            'div',
                            {
                              class: '__er-placeholder',
                              style: 'width: 11.2vw; height: 11.2vw; border-radius: 1.6vw; margin-left: 2vw',
                            },
                          ],
                        ],
                        'div',
                        {
                          class: '__er-shelf-content-right-item-bottom',
                        },
                      ],
                    ],
                    'div',
                    {
                      'x-for': 'shelf',
                      class: '__er-shelf-content-right-item',
                    },
                  ],
                ],
                'div',
                {
                  class: '__er-shelf-content-right',
                },
              ],
            ],
            'div',
            {
              class: '__er-shelf-content',
            },
          ],
        ],
        'div',
        {
          class: '__er-shelf',
          style: 'width: 100vw',
        },
      ],
    ],
    'Template',
    {
      'x-name': 'Shelf',
    },
  ],
  [
    'tag',
    [
      [
        'tag',
        [
          [
            'tag',
            [],
            'img',
            {
              style: 'width: 2.93333vw; height: 2.93333vw',
              src: 'https://gw.alicdn.com/imgextra/i4/O1CN01LWJnxB264WExPYij5_!!6000000007608-2-tps-26-26.png',
            },
          ],
          [
            'tag',
            [['text', ['{{ baseInfo.local }}']]],
            'div',
            {
              class: '__er-poi-bi01',
            },
          ],
          [
            'tag',
            [],
            'img',
            {
              style: 'width: 0.93333vw; height: 1.6vw',
              src: 'https://gw.alicdn.com/imgextra/i4/O1CN01n9B0Ys1EPbDg7GIxX_!!6000000000344-2-tps-7-12.png',
            },
          ],
        ],
        'div',
        {
          class: '__er-poi-bi0',
          'x-if': 'baseInfo.local',
        },
      ],
      [
        'tag',
        [['text', ['{{ baseInfo.name }}']]],
        'div',
        {
          class: '__er-poi-bi1',
        },
      ],
      [
        'tag',
        [
          [
            'tag',
            [
              [
                'tag',
                [['text', ['{{ rateInfo.score }}']]],
                'div',
                {
                  class: '__er-poi-bi21',
                },
              ],
              [
                'tag',
                [
                  [
                    'tag',
                    [['text', ['{{ rateInfo.scoreStr }}']]],
                    'div',
                    {
                      class: '__er-poi-bi22',
                      'x-if': 'rateInfo.scoreStr',
                    },
                  ],
                  [
                    'tag',
                    [['text', ['{{ rateInfo.comment }}']]],
                    'div',
                    {
                      class: '__er-poi-bi23',
                      'x-if': 'rateInfo.comment',
                    },
                  ],
                ],
                'div',
                {
                  class: '__er-poi-bi2-group',
                },
              ],
            ],
            'div',
            {
              class: '__er-poi-bi2',
              'x-if': 'rateInfo.score',
            },
          ],
          [
            'tag',
            [
              [
                'tag',
                [],
                'img',
                {
                  'x-if': 'poiRankingBoard.boardIconUrl',
                  style: 'height: 4.8vw',
                  src: '{{ poiRankingBoard.boardIconUrl }}',
                },
              ],
              [
                'tag',
                [['text', ['{{ poiRankingBoard.rankName }}']]],
                'div',
                {
                  class: '__er-poi-bi31',
                },
              ],
            ],
            'div',
            {
              class: '__er-poi-bi3',
              'x-if': 'poiRankingBoard',
            },
          ],
        ],
        'div',
        {
          class: '__er-poi-bi-info1',
        },
      ],
      [
        'tag',
        [['text', ['{{ baseInfo.tips }}']]],
        'div',
        {
          class: '__er-poi-title',
          'x-if': 'baseInfo.tips',
        },
      ],
      [
        'tag',
        [['text', ['{{ baseInfo.address }}']]],
        'div',
        {
          class: '__er-poi-title',
          'x-if': 'baseInfo.tips',
        },
      ],
    ],
    'Template',
    {
      'x-name': 'Basic',
    },
  ],
  [
    'tag',
    [
      [
        'tag',
        [],
        'img',
        {
          'x-if': 'mainImage',
          id: '_er_main_pic',
          class: '__er-poi-main-img',
          src: '{{ mainImage }}',
        },
      ],
      [
        'tag',
        [],
        'div',
        {
          'x-else': '',
          class: '__er-poi-main-backup',
        },
      ],
      [
        'tag',
        [
          ['tag', [], 'Basic'],
          ['tag', [], 'Shelf'],
        ],
        'div',
        {
          class: '__er-poi-base-info',
          style: 'background: {{ baseInfo.background}};color:{{baseInfo.color}};',
        },
      ],
    ],
    'div',
    {
      class: '__er-poi',
    },
  ],
];
