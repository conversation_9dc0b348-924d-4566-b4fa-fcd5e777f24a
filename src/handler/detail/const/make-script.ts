import { streamWinKey_FspCacheViewReplaceStatus } from './stream';

// 构造版本修正脚本，运行时遍历link标签，渐进式修改版本号
export function getVersionFormatScripts(groupName, version) {
  return format(`(function (){
  try {
      var replaceCss = ${makeReplaceFn()};
      var scripts = document.getElementsByTagName('script');
      var reg = new RegExp('/${groupName}/([0-9.]+)/');
      var version = '${version}';
      var target = '/${groupName}/${version}/';
      loop(function (){
        var links = document.getElementsByTagName('link');
        for (var j = 0, len = links.length; j < len; j++) {
          var link = links[j];
          if (link.href && link.getAttribute('rel') == 'stylesheet') {
            var m = link.href.match(reg);
            if (m && m[1] && m[1] !== version) {
              var nextUrl = link.href.replace(reg,target);
              replaceCss(link, nextUrl);
            }
          }
        }
      }, 3);

      function loop (handler, times, gap = 500) {
        if (times > 0) {
          handler();
          setTimeout(function (){loop(handler, times - 1, gap);}, gap);
        }
      }
    } catch (err) {}
  })()`);
}

export function getLinkReplaceScripts(removeList, addList) {
  try {
    return format(`(function(){
    try {
      var removeList = ${JSON.stringify(removeList)};
      var addList = ${JSON.stringify(addList)};
      var firstEl = null;
      var needRemoveFirst = true;
      var links = document.getElementsByTagName('link');
      for (var i = 0, len = links.length; i < len; i++) {
        var link = links[i];
        if (link.getAttribute('rel') == 'stylesheet' && removeList.indexOf(link.href)>-1) {
          if (!firstEl){
            firstEl=link;
          } else {
            link.remove();
          }
        }
      }

      if (!firstEl){
        firstEl = links[0];
        needRemoveFirst = false;
      }

      for(var j=0,len=addList.length; j<len; j++) {
        var nEl = document.createElement('link');
        nEl.setAttribute('rel', 'stylesheet');
        nEl.setAttribute('data-replace', '1');
        nEl.setAttribute('href', addList[j]);
        if (firstEl) {
          firstEl.after(nEl);
        }
      }
      if (firstEl&&needRemoveFirst){
        firstEl.remove();
      }
    }catch(err){
    }
  })()`);
  } catch (err) {
    return '';
  }
}

// 查找真实元素替换所有预留的占位
export function getStaticHtmlReplaceScripts(isDebug) {
  return format(`(function (){
  try {
    var root = document.querySelector('body>#root');
    var root2 = document.getElementById('fsp-cache-secondssr');
    if (!root || !root2) {
      return;
    }
    var reallist = root2.querySelectorAll('[data-cacheholder]');
    var success = false;
    for(var i = 0, len = reallist.length; i < len; i++) {
      var real = reallist[i];
      var hold = root.querySelector('[data-cacheholder="'+ real.dataset.cacheholder +'"]');
      if (hold && real) {
        success = true;
        if (hold.replaceWith) {
          hold.replaceWith(real)
        } else {
          hold.parentNode.replaceChild(real,hold);
        }
      }
    }
    if (success) {
      ${streamWinKey_FspCacheViewReplaceStatus}=Date.now();
    }
    if (!${isDebug}) {
      root2.remove();
    }
  } catch(err) {}
  })()
  `);
}

function format(scriptString) {
  return scriptString
    .split('\n')
    .map(n => n.trim())
    .join('');
}

function makeReplaceFn() {
  return `
  (function (){
    var temp = {};
    return replaceCss;
    function replaceCss(el, url) {
      if (temp[url]) {
        return;
      }
      var nEl = document.createElement('link');
      nEl.setAttribute('rel', 'stylesheet');
      nEl.setAttribute('data-replace', '1');
      var tag = el.getAttribute('tag');
      if (tag) {
        nEl.setAttribute('tag', tag);
      }
      var type = el.getAttribute('type');
      if (type) {
        nEl.setAttribute('type', type);
      }
      nEl.setAttribute('href', url);
      el.after(nEl);
      nEl.onload = function () {
        if (el && el.remove) {
          el.remove();
        }
      };
      nEl.onerror = function () {
        if (nEl && nEl.remove) {
          nEl.remove();
        }
        temp[url] = 0;
      };
      temp[url] = 1;
    }
  })()
  `;
}
