import { streamWinKey_FspCacheViewReplaceStatus } from './stream';

export function getVersionFormatScripts (groupName) {
  // 以备流式html头部的样式版本和最终ssr的js版本不一致时对齐
  // 常见于新老版本样式冲突
  return format(`(function (){
  try {
      var scriptVersion = '';
      var scripts = document.getElementsByTagName('script');
      var reg = new RegExp('/${groupName}/([0-9.]+)/');
      var linkCache = {};
      for (var i = 0, len = scripts.length; i < len; i++) {
        var script = scripts[i];
        if (script.src) {
          var m = script.src.match(reg);
          if (m && m[1]) {
            scriptVersion = m[1];
            break;
          }
        }
      }
      if (!scriptVersion) {
        return;
      }
      loop(function (){
        var links = document.getElementsByTagName('link');
        for (var j = 0, len = links.length; j < len; j++) {
          var link = links[j];
          if (link.href && link.getAttribute('rel') == 'stylesheet') {
            var m = link.href.match(reg);
            if (m && m[1] && m[1] !== scriptVersion) {
              var nextUrl = link.href.replace(
                reg,
                '/${groupName}/' + scriptVersion + '/',
              );
              replaceCss(link, nextUrl);
            }
          }
        }
      }, 3);

      function loop (handler, times, gap = 500) {
        if (times > 0) {
          handler();
          setTimeout(function (){loop(handler, times - 1, gap);}, gap);
        }
      }
    } catch (err) {
    }
    function replaceCss (el, newLinkUrl) {
      if (linkCache[newLinkUrl]) {
        return;
      }
      var newLinkEl = document.createElement('link');
      newLinkEl.setAttribute('rel', 'stylesheet');
      newLinkEl.setAttribute('data-replace', '1');
      var tag = el.getAttribute('tag');
      if (tag) {
        newLinkEl.setAttribute('tag', tag);
      }
      var type = el.getAttribute('type');
      if (type) {
        newLinkEl.setAttribute('type', type);
      }
      newLinkEl.setAttribute('href', newLinkUrl);
      el.after(newLinkEl);
      newLinkEl.onload = function () {
        if (el && el.remove) {
          el.remove();
        }
      };
      linkCache[newLinkUrl] = 1;
    }})()
    `);
}


export function getStaticHtmlReplaceScripts(isDebug) {
  return format(`(function (){
  try {
    var root = document.querySelector('body>#root');
    var root2 = document.getElementById('fsp-cache-secondssr');
    if (!root || !root2) {
      return;
    }
    var reallist = root2.querySelectorAll('[data-cacheholder]');
    var success = false;
    for(var i = 0, len = reallist.length; i < len; i++) {
      var real = reallist[i];
      var hold = root.querySelector('[data-cacheholder="'+ real.dataset.cacheholder +'"]');
      if (hold && real) {
        success = true;
        if (hold.replaceWith) {
          hold.replaceWith(real)
        } else {
          hold.parentNode.replaceChild(real,hold);
        }
      }
    }
    if (success) {
      ${streamWinKey_FspCacheViewReplaceStatus}=Date.now();
    }
    if (!${isDebug}) {
      root2.remove();
    }
  } catch(err) {}
  })()
  `);
}

function format(scriptString) {
  return scriptString.split('\n').map(n => n.trim()).join('');
}
