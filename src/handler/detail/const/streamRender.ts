import detailFcpToken from './fcp-token/detail';

// 流式渲染页面级配置
// https://aliyuque.antfin.com/trip_plat/pu6xpg/qx32pc3h0vv9b872
export const streamRenderConfig = {
  '/app/trip/rx-travel-detail/pages/index': {
    // --- FCP模板配置
    enableFcpLazyCss: true,
    // fcp渲染内容
    // remoteCacheUrl: 'https://traveldetail.feizhu.com/detailsnapshot',
    fcpCacheKey: 'detailsnapshot',
    // 动态渲染模板，@ali/mpi-detailer-screen-parse的编译结果
    templateToken: detailFcpToken,
    // --- FSP数据配置
    // 每次访问的数据缓存根据url什么字段区分存储，
    cacheKey: ['id', 'itemId', 'itemid', 'item_id'],
    // remoteCacheHtmlUrl: 'https://traveldetail.feizhu.com/htmlcache_detail',
    fspCacheKey: 'htmlcache_detail',
    fcpEndScript: 'https://o.alicdn.com/mpi/detail-fcp-prepare/index.js',
    // 兜底模板数据
    fcpDefaultData: {
      action: [
        ['客服', 'https://gw.alicdn.com/imgextra/i1/O1CN011eO3fb23lcO6TstkM_!!6000000007296-2-tps-144-144.png_60x60.jpg'],
        ['店铺', 'https://gw.alicdn.com/imgextra/i1/O1CN01HFXFtC1Q4KImxU4gi_!!6000000001922-2-tps-63-63.png'],
        ['收藏', 'https://gw.alicdn.com/imgextra/i4/O1CN01UFdeG01Ha52oy6Hh4_!!6000000000773-2-tps-84-84.png'],
      ],
    },
    secondSsrTimeout: 300,
    // 在
    fspEndScriptString:
      "(function () {try {var fcpImage = document.getElementById('_ermpic');if (!fcpImage) {return;}var mainPic = fcpImage.getAttribute('src');if (!mainPic) {message('nopic');return;}var cssList = ['.td-banner__erholder{background-image:url(' + mainPic + ')}'];if (fcpImage.previousElementSibling) {var mainPic64 = fcpImage.previousElementSibling.getAttribute('src');if (mainPic64) {cssList.push('.td-banner__erholder64{background-image:url(' + mainPic64 + ')}');}}var styleEl = document.createElement('style');document.body.appendChild(styleEl);styleEl.setAttribute('name', 'er-fcp-end');styleEl.innerHTML = cssList.join('')} catch (err) {}})()",
  },
};

// 重点的项目以及其对应的kv配置的页面，当任意流式走_fli_prepare预加载时会拉取一次重点配置缓存
export const projectKvMap = {
  // 门票频道
  '/app/trip/rx-trip-ticket/pages/home': 'rx-trip-ticket_home',
  // poi
  '/app/trip/rx-trip-ticket/pages/detail': 'rx-trip-ticket_detail',
  // 跟团游频道
  '/app/trip/rx-channels-2023/pages/group-tour': 'rx-channels-2023_group-tour',
  // 出境游频道
  '/app/trip/rx-channels-2023/pages/abroad': 'rx-channels-2023_abroad',
  // 订单详情
  '/app/trip/rx-travel-order-detail/pages/detail': 'rx-travel-order-detail_detail',
  // 境外上网频道
  '/app/trip/rx-visawifi/pages/wifi': 'rx-visawifi_wifi',
  // 签证频道
  '/app/trip/rx-trip-visa-channel/pages/home': 'rx-trip-visa-channel_home',
};

// 进入度假流式的白名单项目
// 度假核心页面：https://aliyuque.antfin.com/trip_plat/pu6xpg/qflp3bkv3d6lvxkv?singleDoc#
export const projectWhiteMap = {
  // 频道
  'rx-channels-2023': 1,
  'rx-channels-2024': 1,
  // 签证
  'rx-trip-visa-channel': 1,
  // 包车
  'rx-trip-vehicle': 1,
  // 小搜
  'rx-travel-search': 1,
  // 线路
  'rx-spu': 1,
  'rx-trip-guide': 1,
  // 门票
  'rx-trip-ticket': 1,
  // 通讯
  'rx-visawifi': 1,
  // 邮轮
  'rx-cruise-channel': 1,
  // 境外交通
  'rx-euro-railway': 1,
  // 店铺
  'rx-shop-fone': 1,
};

// 兜底路由
export const defaultStreamConfig = {
  __default: true,
  cacheKey: false
};

