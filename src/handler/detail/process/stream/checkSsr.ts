import { assert, raceTasks, checkOptionMatch } from '../../tools';
import { FAAS_HOST_VACATION_ONLINE, FAAS_HOST_VACATION_PRE } from '../../const';
import { SSR_LOCAL_PAGE } from '../../../../constant';

/**
 *
 * @param ctx
 * @returns
 */
export async function getOnlineSsr(ctx, { optionContext }) {
  const addLog = ctx.log.collect('renderSSR');
  try {
    const ssrServerConfig = checkSsrServer(ctx, { optionContext, log: addLog });
    if (ctx.slsLogData) {
      ctx.slsLogData.ssr_host = ssrServerConfig.host;
      ctx.slsLogData.ssr_pathname = ssrServerConfig.pathname;
    }
    const { pathname, host, server } = ssrServerConfig;
    // 真实请求faas函数的地址
    const faasRequestUrl = ctx.getRequestFaasUrl({
      pathname,
      host,
    });
    const releasePerf = ctx.perf.stage('loadFaasHtml');
    ctx.perf.set('faasserver', getLogName(ssrServerConfig));
    addLog('serverUse', ssrServerConfig);
    addLog('requestUrl', faasRequestUrl);
    assert(!ctx.debugSsrFail, {
      message: 'debugssrfail',
      code: 'test',
    });
    const timeout = 'faastimeout';
    const htmlText = await raceTasks(
      [
        fetch(faasRequestUrl, {
          headers: ctx.requestFaasHeadersInstance,
        }).then(res => res.text()),
      ],
      ctx.isDevMode ? 5000 : 2000,
      timeout
    );
    if (htmlText) {
      addLog('length', htmlText.length);
      ctx.perf.set('faaslength', htmlText.length);
    }
    releasePerf();
    return htmlText;
  } catch (err) {
    addLog('error', err.message);
    return `realssr(${err.message})`;
  }
}

export function checkStreamCSR(ctx, { optionContext }) {
  const addLog = ctx.log.collect('csr');
  if (ctx.pageConfig.csrPage) {
    return result({
      result: true,
      message: 'csrPage',
    });
  }
  if (!!ctx.debugCsr) {
    return result({
      result: true,
      message: 'debug',
    });
  }
  if (ctx.urcPreloadType) {
    return result({
      result: false,
      message: 'urcPreload',
    });
  }
  const csrConfig = ctx.pageConfig.csr;
  if (!csrConfig) {
    return result({
      result: false,
      message: 'missrule',
    });
  }
  const matchInfo = checkOptionMatch(csrConfig, optionContext);
  return result(matchInfo);

  function result(e) {
    addLog('config', e);
    return {
      enable: !!e.result,
    };
  }
}

export function checkStreamSSG(ctx, { fcpResult }) {
  const addLog = ctx.log.collect('ssg');
  const cfg = ctx.pageConfig.ssg;
  if (ctx.urcPreloadType || !cfg) {
    if (!cfg) {
      addLog('config', {
        result: false,
        message: 'missrule',
      });
    } else {
      addLog('config', {
        result: false,
        message: 'urcPreload',
      });
    }
    return {
      enableAll: false,
      enable: false,
    };
  }

  const params = {
    featrue: fcpResult?.data?.featrue || {},
    query: ctx.query,
    env: ctx.env,
    location: ctx.location,
  };
  const matchInfo = checkOptionMatch(cfg, params);

  const e = {
    enableAll: cfg.all,
    enable: !!matchInfo.result,
  };
  addLog('config', {
    ...matchInfo,
    enableAll: cfg.all,
  });
  return e;
}

export function checkSsrServer(ctx, { optionContext, log }) {
  const addLog = typeof log == 'function' ? log : (key, val) => {};
  const routeConfig = ctx.pageConfig.ssrRoute;

  if (ctx.isDevMode && ctx.query.bundleUrl) {
    return {
      host: ctx.isPre && !ctx.query._faas_online ? FAAS_HOST_VACATION_PRE : FAAS_HOST_VACATION_ONLINE,
      server: 'dev',
      pathname: `/pre${SSR_LOCAL_PAGE}`,
    };
  }

  if (routeConfig && routeConfig.length) {
    for (let i = 0, len = routeConfig.length; i < len; i++) {
      const cfg = routeConfig[i];
      if (cfg.server) {
        const matchInfo = checkOptionMatch(cfg, optionContext);
        addLog(`serverMatch${i}`, {
          ...matchInfo,
          server: cfg.server,
        });
        if (matchInfo.result) {
          return {
            host: cfg.host || FAAS_HOST_VACATION_ONLINE,
            server: cfg.server,
            pathname: `/${cfg.server}${ctx.pathname}`,
          };
        }
      }
    }
  }

  // 线上走独立ssr，预发回通用的
  let defaultConfig = {
    host: FAAS_HOST_VACATION_ONLINE,
    server: 'default',
    pathname: ctx.pathname,
  };

  if (ctx.isPre && !ctx.query._faas_online) {
    defaultConfig.host = FAAS_HOST_VACATION_PRE;
    defaultConfig.pathname = `/prepub${ctx.pathname}`;
  }

  addLog('serverMatchDefault', defaultConfig);
  return defaultConfig;
}

export function getLogName(cfg) {
  try {
    const { pathname, host, server } = cfg;
    const [first] = host.split('.');
    return `${first}_${server}`;
  } catch (err) {}
}
