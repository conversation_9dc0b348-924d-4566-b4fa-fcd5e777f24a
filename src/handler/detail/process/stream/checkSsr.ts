import { assert, raceTasks, checkOptionMatch } from '../../tools';
import { FAAS_PATHNAME_ALIAS, FAAS_HOST_PRE, FAAS_HOST_ONLINE, FAAS_HOST_VOCATION_ONLINE } from '../../const';
import { SSR_LOCAL_PAGE } from '../../../../constant';

/**
 *
 * @param ctx
 * @returns
 */
export async function getOnlineSsr(ctx, { fcpResult }) {
  const addLog = ctx.log.collect('loadFirstSsr');
  try {
    const ssrServerConfig = checkSsrServer(ctx, { fcpResult });
    if (ctx.slsLogData) {
      ctx.slsLogData.ssr_host = ssrServerConfig.host;
      ctx.slsLogData.ssr_pathname = ssrServerConfig.pathname;
    }
    const { pathname, host, server } = ssrServerConfig;
    // 真实请求faas函数的地址
    const faasRequestUrl = ctx.getRequestFaasUrl({
      pathname,
      host,
    });
    const releasePerf = ctx.perf.stage('loadFaasHtml');
    ctx.perf.set('faasserver', getLogName(ssrServerConfig));
    addLog('server', ssrServerConfig);
    addLog('ssrUrl', faasRequestUrl);
    assert(!ctx.debugSsrFail, {
      message: 'debugssrfail',
      code: "test"
    });
    const htmlText = await raceTasks(
      [
        fetch(faasRequestUrl, {
          headers: ctx.requestFaasHeadersInstance,
        }).then(res => res.text()),
      ],
      ctx.isDevMode ? 5000 : 2000,
      'faastimeout'
    );
    ctx.perf.set('faaslength', htmlText && htmlText.length);
    releasePerf();
    return htmlText;
  } catch (err) {
    addLog('error', err.message);
    return `onlinessr(${err.message})`;
  }
}

export async function getSecondSsr(ctx, { fcpResult }) {
  const addLog = ctx.log.collect('loadSecondSsr');
  try {
    const ssrServerConfig = checkSsrServer(ctx, { fcpResult, exParams: { secondSsr: '1' } });
    const { pathname, host, server } = ssrServerConfig;
    // 真实请求faas函数的地址
    const faasRequestUrl = ctx.getRequestFaasUrl({
      pathname,
      host,
      query: {
        _cache_replace_: 1,
      },
    });
    addLog('server', ssrServerConfig);
    const releasePerf = ctx.perf.stage('loadFaasHtml2');
    ctx.perf.set('faasserver2', getLogName(ssrServerConfig));
    addLog('ssrUrl', faasRequestUrl);
    assert(!ctx.debugSsrFail2, {
      message: 'debugssrfail2',
    });
    const tasks = [
      fetch(faasRequestUrl, {
        headers: ctx.requestFaasHeadersInstance,
      }).then(res => res.text()),
    ];
    const htmlText = await raceTasks(tasks, ctx.isDevMode ? 5000 : ctx.pageConfig.secondSsrTimeout || 500, 'timeout');
    releasePerf();
    // 没有可替换的元素，无效渲染
    if (!htmlText || htmlText.indexOf('data-cacheholder') < 0) {
      addLog('error', 'invalid');
      return '';
    }
    ctx.perf.set('faaslength2', htmlText && htmlText.length);
    releasePerf();
    return htmlText;
  } catch (err) {
    addLog('error', err.message);
    return '';
  }
}

export function checkSecondSsr(ctx, { fcpResult }) {
  const addLog = ctx.log.collect('checkSecondSsr');
  if (ctx.debugSceondSsr) {
    addLog('debug', 1);
    return true;
  }
  const rule = ctx.pageConfig.secondssr;
  if (!rule) {
    addLog('noconfig', 1);
    return;
  }
  const matchInfo = checkOptionMatch(rule, {
    ...(ctx.query || {}),
    ...(fcpResult?.data?.featrue || {}),
    ...ctx.env,
  });
  addLog('match', matchInfo);
  return matchInfo.result;
}

export function checkSsrDegrade(ctx, { fcpResult }) {
  const addLog = ctx.log.collect('checkCsr');
  if (ctx.urcPreloadType) {
    addLog('disable', 'fliggy');
    return {
      enable: false,
    };
  }

  const e = {
    enable: !!ctx.debugCsr || checkCsrConfig(),
  };
  addLog('result', e);
  return e;

  function checkCsrConfig() {
    const csrConfig = ctx.pageConfig.csr;
    if (!csrConfig) {
      addLog('match', 'noconfig');
      return false;
    }
    const params = {
      ...(ctx.query || {}),
      ...(fcpResult?.data?.featrue || {}),
      ...ctx.env,
    };
    const matchInfo = checkOptionMatch(csrConfig, params);
    addLog('match', matchInfo.message);
    return matchInfo.result;
  }
}

function checkSsrServer(ctx, { fcpResult, exParams = {} }) {
  const addLog = ctx.log.collect('checkSsrServer');
  const routeConfig = ctx.pageConfig.ssrRoute;

  if (ctx.isDevMode && ctx.query.bundleUrl) {
    return {
      host: ctx.isPre && !ctx.query._faas_online ? FAAS_HOST_PRE : FAAS_HOST_ONLINE,
      server: 'dev',
      pathname: SSR_LOCAL_PAGE,
    };
  }

  if (routeConfig && routeConfig.length) {
    const params = {
      ...(ctx.query || {}),
      ...(fcpResult?.data?.featrue || {}),
      ...ctx.env,
      ...exParams,
    };
    for (let i = 0, len = routeConfig.length; i < len; i++) {
      const cfg = routeConfig[i];
      if (cfg.server) {
        const matchInfo = checkOptionMatch(cfg, params);
        addLog(`match${i}`, {
          ...matchInfo,
          server: cfg.server,
        });
        if (matchInfo.result) {
          return {
            host: cfg.host || FAAS_HOST_VOCATION_ONLINE,
            server: cfg.server,
            pathname: `/${cfg.server}${ctx.pathname}`,
          };
        }
      }
    }
  }
  const host = ctx.isPre && !ctx.query._faas_online ? FAAS_HOST_PRE : FAAS_HOST_ONLINE;
  return {
    host,
    server: host.split('.')[0],
    pathname: FAAS_PATHNAME_ALIAS[ctx.pathname] || ctx.pathname,
  };
}


function getLogName(cfg) {
  try {
    const { pathname, host, server } = cfg;
    const [first] = host.split('.');
    return `${first}_${server}`;
  } catch (err) {}
}
