import { assert, raceTasks, checkOptionMatch } from '../../tools';
import { FAAS_HOST_PRE, FAAS_HOST_ONLINE, FAAS_HOST_VOCATION_ONLINE } from '../../const';
import { SSR_LOCAL_PAGE } from '../../../../constant';

/**
 *
 * @param ctx
 * @returns
 */
export async function getOnlineSsr(ctx, { optionContext }) {
  const addLog = ctx.log.collect('renderSSR');
  try {
    const ssrServerConfig = checkSsrServer(ctx, { optionContext, logNameSpace: 'renderSSR' });
    if (ctx.slsLogData) {
      ctx.slsLogData.ssr_host = ssrServerConfig.host;
      ctx.slsLogData.ssr_pathname = ssrServerConfig.pathname;
    }
    const { pathname, host, server } = ssrServerConfig;
    // 真实请求faas函数的地址
    const faasRequestUrl = ctx.getRequestFaasUrl({
      pathname,
      host,
    });
    const releasePerf = ctx.perf.stage('loadFaasHtml');
    ctx.perf.set('faasserver', getLogName(ssrServerConfig));
    addLog('serverUse', ssrServerConfig);
    addLog('requestUrl', faasRequestUrl);
    assert(!ctx.debugSsrFail, {
      message: 'debugssrfail',
      code: 'test',
    });
    const timeout = 'faastimeout';
    const htmlText = await raceTasks(
      [
        fetch(faasRequestUrl, {
          headers: ctx.requestFaasHeadersInstance,
        }).then(res => res.text()),
      ],
      ctx.isDevMode ? 5000 : 2000,
      timeout
    );
    if (htmlText) {
      addLog('length', htmlText.length);
      ctx.perf.set('faaslength', htmlText.length);
    }
    releasePerf();
    return htmlText;
  } catch (err) {
    addLog('error', err.message);
    return `onlinessr(${err.message})`;
  }
}

export async function getSecondSsr(ctx, { optionContext }) {
  const addLog = ctx.log.collect('renderSecondSSR');
  try {
    const ssrServerConfig = checkSsrServer(ctx, { optionContext: { ...optionContext, secondSsr: '1' }, logNameSpace: 'renderSecondSSR' });
    const { pathname, host, server } = ssrServerConfig;
    // 真实请求faas函数的地址
    const faasRequestUrl = ctx.getRequestFaasUrl({
      pathname,
      host,
      query: {
        _cache_replace_: 1,
      },
    });
    addLog('server', ssrServerConfig);
    const releasePerf = ctx.perf.stage('loadFaasHtml2');
    ctx.perf.set('faasserver2', getLogName(ssrServerConfig));
    addLog('requestUrl', faasRequestUrl);
    assert(!ctx.debugSsrFail2, {
      message: 'debugssrfail2',
    });
    const tasks = [
      fetch(faasRequestUrl, {
        headers: ctx.requestFaasHeadersInstance,
      }).then(res => res.text()),
    ];
    const htmlText = await raceTasks(tasks, ctx.isDevMode ? 5000 : ctx.pageConfig.secondSsrTimeout || 500, 'timeout');
    releasePerf();
    // 没有可替换的元素，无效渲染
    if (!htmlText || htmlText.indexOf('data-cacheholder') < 0) {
      addLog('responseCheck', 'invalid');
      return '';
    }
    ctx.perf.set('faaslength2', htmlText && htmlText.length);
    releasePerf();
    return htmlText;
  } catch (err) {
    addLog('requestError', err.message);
    return '';
  }
}

export function checkSecondSsr(ctx, { optionContext }) {
  const addLog = ctx.log.collect('renderSecondSSR');
  if (ctx.debugSceondSsr) {
    addLog('config', {
      result: true,
      message: 'debug',
    });
    return true;
  }
  const rule = ctx.pageConfig.secondssr;
  if (!rule) {
    addLog('config', {
      result: false,
      message: 'missrule',
    });
    return;
  }
  const matchInfo = checkOptionMatch(rule, optionContext);
  addLog('config', matchInfo);
  return matchInfo.result;
}

export function checkStreamCSR(ctx, { optionContext }) {
  const addLog = ctx.log.collect('csr');
  if (ctx.pageConfig.csrPage) {
    return result({
      result: true,
      message: "csrPage"
    })
  }
  if (!!ctx.debugCsr) {
    return result({
      result: true,
      message: 'debug',
    });
  }
  if (ctx.urcPreloadType) {
    return result({
      result: false,
      message: 'urcPreload',
    });
  }
  const csrConfig = ctx.pageConfig.csr;
  if (!csrConfig) {
    return result({
      result: false,
      message: 'missrule',
    });
  }
  const matchInfo = checkOptionMatch(csrConfig, optionContext);
  return result(matchInfo);

  function result(e) {
    addLog('config', e);
    return {
      enable: !!e.result,
    };
  }
}

export function checkStreamSSG(ctx, { fcpResult }) {
  const addLog = ctx.log.collect('ssg');
  const cfg = ctx.pageConfig.ssg;
  if (ctx.urcPreloadType || !cfg) {
    if (!cfg) {
      addLog('config', {
        result: false,
        message: 'missrule',
      });
    } else {
      addLog('config', {
        result: false,
        message: 'urcPreload',
      });
    }
    return {
      enableAll: false,
      enable: false,
    };
  }

  const params = {
    featrue: fcpResult?.data?.featrue || {},
    query: ctx.query,
    env: ctx.env,
    location: ctx.location,
  };
  const matchInfo = checkOptionMatch(cfg, params);

  const e = {
    enableAll: cfg.all,
    enable: !!matchInfo.result,
  };
  addLog('config', {
    ...matchInfo,
    enableAll: cfg.all,
  });
  return e;
}

function checkSsrServer(ctx, { optionContext, logNameSpace}) {
  const addLog = ctx.log.collect(logNameSpace);
  const routeConfig = ctx.pageConfig.ssrRoute;

  if (ctx.isDevMode && ctx.query.bundleUrl) {
    return {
      host: ctx.isPre && !ctx.query._faas_online ? FAAS_HOST_PRE : FAAS_HOST_ONLINE,
      server: 'dev',
      pathname: SSR_LOCAL_PAGE,
    };
  }

  if (routeConfig && routeConfig.length) {
    for (let i = 0, len = routeConfig.length; i < len; i++) {
      const cfg = routeConfig[i];
      if (cfg.server) {
        const matchInfo = checkOptionMatch(cfg, optionContext);
        addLog(`serverMatch${i}`, {
          ...matchInfo,
          server: cfg.server,
        });
        if (matchInfo.result) {
          return {
            host: cfg.host || FAAS_HOST_VOCATION_ONLINE,
            server: cfg.server,
            pathname: `/${cfg.server}${ctx.pathname}`,
          };
        }
      }
    }
  }

  // 线上走独立ssr，预发回通用的
  const host = ctx.isPre && !ctx.query._faas_online ? FAAS_HOST_PRE : FAAS_HOST_VOCATION_ONLINE;
  const result = {
    host,
    server: host.split('.')[0],
    pathname: `/vacation${ctx.pathname}`,
  };
  addLog('serverMatchDefault', result);
  return result;
}

function getLogName(cfg) {
  try {
    const { pathname, host, server } = cfg;
    const [first] = host.split('.');
    return `${first}_${server}`;
  } catch (err) {}
}
