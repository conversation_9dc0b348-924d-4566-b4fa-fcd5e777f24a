import { assert, raceTasks, getVersionFromHtml, checkStyleMiss } from '../../tools';
import {
  streamRootStartReg,
  streamWinKey_CsrRender,
  streamScript_CsrRenderStart,
  streamWinKey_PerfInfo,
  streamWinKey_Hook_FspCsrStart,
} from '../../const';
import { checkHtmlBase } from './checkHtmlBase';

export async function renderCsrOnline(ctx, { writeHtml, fcpResult, csrDependencies = [], type = 'csr_config' }) {
  const addLog = ctx.log.collect('renderCsr');
  const csrHtmlString = await getCsrHtml(ctx);
  // fcp渲染成功，截取部分ssr数据拼接渲染
  const [rootTag] = csrHtmlString.match(streamRootStartReg) || [];

  assert(!!rootTag && !ctx.debugCsrHtml, {
    code: 'csrfail',
    detail: csrHtmlString || 'emptyhtml',
  });

  // 文本开始位置
  const contentStartPosition = csrHtmlString.indexOf(rootTag);
  const scripts = [
    `${streamWinKey_PerfInfo}=${ctx.perf.toJsonString()}`,
    `if(typeof ${streamWinKey_Hook_FspCsrStart} == 'function'){${streamWinKey_Hook_FspCsrStart}();}`,
  ];
  // 区分fcp渲染成功与否，没有fcp直接全量输出
  if (fcpResult) {
    scripts.push(`${streamWinKey_CsrRender}="${type}"`);
    scripts.push(streamScript_CsrRenderStart);
    const htmlString = [
      `<script name="render-csr">${scripts.join(';')}</script>`,
      ...csrDependencies,
      checkStyleMiss(ctx, fcpResult && fcpResult.html, csrHtmlString.slice(0, contentStartPosition)),
      csrHtmlString.slice(contentStartPosition),
    ];
    await writeHtml(htmlString.join(''), 'rendercsr');
    addLog('type', 'merge_fcp');
  } else {
    scripts.push(`${streamWinKey_CsrRender}="${type}"`);
    const htmlString = [
      csrHtmlString.slice(0, contentStartPosition),
      `<script name="render-csr">${scripts.join(';')}</script>`,
      ...csrDependencies,
      csrHtmlString.slice(contentStartPosition),
    ];
    // 没有fcp，全量回退csr
    await writeHtml(htmlString.join(''), 'renderallcsr');
    addLog('type', 'all_csr');
  }
  if (ctx.pageConfig.csrPage) {
    checkHtmlBase(ctx, csrHtmlString, true);
  }
}

/**
 *
 * @param ctx
 * @returns
 */
export async function getCsrHtml(ctx) {
  try {
    assert(!ctx.debugCsrFail, {
      message: 'debugcsrFail',
    });
    const releasePerf = ctx.perf.stage('loadCsrHtml');
    // 静态文件300都没返回，基本凉了
    const htmlText = await raceTasks([loadCsr()], 200);
    releasePerf();
    return htmlText;
  } catch (err) {
    return 'timeout';
  }

  function loadCsr() {
    return fetch(ctx.requestCsrUrlString, {
      cdnProxy: true, // 必须是接入阿里cdn的域名才有加速
    }).then(res => res.text());
  }
}
