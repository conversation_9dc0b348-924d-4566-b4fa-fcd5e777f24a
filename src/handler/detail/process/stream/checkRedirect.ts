import { checkOptionMatch } from '../../tools';

export function checkRedirect(requestContext, pageConfig) {
  const { redirect = [] } = pageConfig;
  if (!redirect || !redirect.length) {
    return false;
  }

  const optionParams = {
    cookie: requestContext.requestCookie,
    query: requestContext.requestUrlInstance.query,
    env: requestContext.env,
  };
  for (let i = 0, len = redirect.length; i < len; i++) {
    const e = redirect[i];
    // 没有规则
    if (!e || !e.rule) {
      continue;
    }

    const match = checkOptionMatch(e, optionParams);
    if (!match.result) {
      return false;
    }

    const { csr, host = requestContext.requestUrlInstance.host, pathname, query = {} } = e.rule;

    // 直接降级csr
    if (csr) {
      return requestContext.fallBack('redirect', query, true);
    }

    if (!pathname) {
      return false;
    }

    return requestContext.redirect(
      {
        host,
        pathname,
        query,
      },
      'byrule',
      true
    );
  }

  return false;
}
