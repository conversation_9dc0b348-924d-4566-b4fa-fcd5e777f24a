import { streamWinKey_DebugInfo, BUILD_VERSION } from '../../const';
import { dingdingwarning } from '../../tools';
import { loadPageConfig } from './checkConfig';
import { checkRedirect } from './checkRedirect';
import startStreamRender from './render';
import makeRenderContext from './context';

export default async function checkStreamRender(requestContext, startTime, fallBackCommonSSR) {
  const { event, isDebug, requestUrlInstance, env = {} } = requestContext;

  const callHelp = err => dingdingwarning(event, err, [`userId：${env.userId}`, `osName：${env.osName}`, `appName：${env.appName}`]);

  try {
    // 确认页面配置，分内置/远程拉取/url配置
    const pageConfig = await loadPageConfig(requestContext);

    if (!pageConfig || pageConfig.enable === false) {
      const fallbackMessage = `${!pageConfig ? 'pageConfigNotExist' : 'pageConfigDisable'}`;
      if (isDebug) {
        return fallbackMessage;
      }
      return fallBackCommonSSR();
    }

    const redirectResponse = checkRedirect(requestContext, pageConfig);
    if (redirectResponse) {
      return redirectResponse;
    }

    // 基于请求解析结果，准备流式渲染所需的变量
    const renderContext = makeRenderContext({
      startTime,
      pageConfig,
      requestContext,
    });

    if (!renderContext) {
      return false;
    }

    const { writable, readable } = new TransformStream();
    // 流式的拼接html文本
    const writer = writable.getWriter();
    const encoder = new TextEncoder();

    const writeHtml = async (e, source) => {
      try {
        await writer.write(encoder.encode(e));
      } catch (err) {
        if (err) {
          // 定位哪里调用异常
          err.message = `${source}:${err.message}`;
        }
        throw err;
      }
    };

    const renderUtils = {
      writeHtml,
      event: requestContext.event,
    };

    if (requestUrlInstance.query._debug_dingding) {
      callHelp({ message: 'testdingdingwran' });
    }

    const onEnd = async function () {
      if (isDebug) {
        const debugInfo = `<script name="msg-${BUILD_VERSION}">${streamWinKey_DebugInfo}=${renderContext.log.toJsonString()}</script></body></html>`;
        await writer.write(encoder.encode(debugInfo));
      }
      requestContext.recordEnd(perfMapRaxState(renderContext));
      writer.close();
    };

    event.waitUntil(
      startStreamRender(renderContext, renderUtils)
        .catch(e => {
          renderContext.log.add('uncaughtError', e.message);
          callHelp(e);
        })
        .then(onEnd)
    );

    return new Response(readable, {
      headers: {
        'er-code-v': BUILD_VERSION,
        'content-type': 'text/html; charset=utf-8',
        'use-stream': '1.0.8',
        'Transfer-Encoding': 'chunked',
        'streaming-parser': 'open',
      },
      status: 200,
    });
  } catch (e) {
    if (isDebug) {
      return `checkStreamRender error:${e.message}`;
    }
    requestContext.recordError(e, 'checkStreamRender');
    callHelp(e);
  }

  return false;
}

// 商详自定义性能指标转化成通用ssr能直接查日志的
function perfMapRaxState(ctx) {
  const perfState = ctx.perf.getState();
  const slsLogData = ctx.slsLogData || {};
  return {
    ...perfState,
    ...slsLogData,
    faas_duration: perfState.loadFaasHtmlCost,
  };
}
