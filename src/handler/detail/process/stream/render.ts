import { assert, parseSsrHtml, getKvCacheJson } from '../../tools';
import { streamKvConfigAll } from '../../const';
import { checkHtmlBase } from './checkHtmlBase';
import { checkFcpData } from './checkFcpData';
import { getOnlineSsr, getSecondSsr, checkStreamCSR, checkSecondSsr, checkStreamSSG } from './checkSsr';
import { getFspCache, checkFspCacheEnable } from './checkFspCache';
import { renderEndOnline, renderFspCache, renderEndWitchCache, renderFspCacheSecondSsr } from './renderFsp';
import { renderCsrOnline } from './renderCsr';
import { loadMiniprogramDependencies } from '../../../miniprogram';
import renderFcp from './renderFcp';
import checkPrepare from './checkPrepare';

/**
 * 注意！流式下不允许有未补捉的promise异常，会导致c端请求200但程序中断的错误：
 * 线上问题：https://aliyuque.antfin.com/trip_plat/pu6xpg/wqy8k6inec9fqatv
 */
export default async function renderStreamPage(ctx, { writeHtml, event }) {
  let fcpResult = null;
  // 特定环境渲染依赖
  const csrDependencies = [];
  const beginTime = new Date().getTime();

  // 套壳场景，有额外的异步依赖
  // 套壳都走流式csr，稳定且效率
  const isMiniProgramH5 = ctx.env.isXhsMiniAppH5 || ctx.env.isWeChatMiniProgramH5 || ctx.env.isByteDanceMicroApp;

  try {
    // 异常时降级csr渲染
    let remoteConfig = null;
    // --- 前置检查
    const releaseWaitStartSsr = ctx.perf.stage('wait2ssr');
    // 预热cdn资源
    const prepareTask = checkPrepare(ctx, [getRemoteConfig]);
    if (prepareTask) {
      const result = await prepareTask;
      await writeHtml(result);
      return;
    }

    ctx.perf.time('start');
    // 异步拉fspcache，在config拉到后再进一步await判断是否启用
    const fspCacheTask = getFspCache(ctx);
    const tasks = [renderFcp(ctx, { writeHtml }), getRemoteConfig(ctx)];

    if (isMiniProgramH5) {
      tasks.push(loadMiniprogramDependencies(event, beginTime));
    }

    // --- (1)输出首段fcp，并行拉er配置项
    const results = await Promise.all(tasks);
    [fcpResult, remoteConfig] = results;
    ctx.perf.time('all');

    if (isMiniProgramH5) {
      const asyncTaskResult = results[2];
      // 断言成功，拉取依赖失败则302兜底页
      assert(!ctx.debugProxyDepend && asyncTaskResult && asyncTaskResult.success, {
        code: 'dependFail',
      });
      csrDependencies.push(asyncTaskResult.htmlString);
      if (ctx.debugProxyDependData) {
        ctx.log.add('dependencies', () => {
          return encodeURIComponent(JSON.stringify(csrDependencies));
        });
      }
    }

    if (ctx.debugFcpOnlyView) {
      await writeHtml('</body></html>');
      return;
    }

    // 通用开关的上下文
    const optionContext = {
      featrue: fcpResult?.data?.featrue || {},
      query: ctx.query,
      env: ctx.env,
      location: ctx.location,
    };

    ctx.log.add('config', remoteConfig);
    const csrConfig = checkStreamCSR(ctx, { optionContext });

    if (csrConfig.enable || isMiniProgramH5) {
      await renderCsrOnline(ctx, { writeHtml, fcpResult, csrDependencies });
      return;
    }
    releaseWaitStartSsr();
    const enableSecondSsr = checkSecondSsr(ctx, { optionContext });
    const enableWaitSecondSsr = ctx.debugSecondSsrWait || ctx.pageConfig.waitSecondSsr;

    // 异步一段ssr
    const onlineSsrTask = getOnlineSsr(ctx, { optionContext });
    // 异步二段ssr
    const secondSsrTask = enableSecondSsr ? getSecondSsr(ctx, { optionContext }) : '';

    // --- (2)输出静态fsp
    // 判断是否启用fspcache模式
    const enable = checkFspCacheEnable(ctx, { optionContext });
    let fspCacheInfo = null;
    if (enable) {
      const fspHtmlCacheString = await fspCacheTask;
      ctx.perf.time('fspcache');
      const parsedCacheHtml = parseSsrHtml(fspHtmlCacheString);
      // 输出静态缓存时，收尾的Html（script部分）需要在ssr调用结果后决定是启用
      fspCacheInfo = await renderFspCache(ctx, { writeHtml, parsedCacheHtml, fcpResult });
    }

    // 只看fspcache的html
    if (ctx.debugFspCacheHtml && fspCacheInfo) {
      await writeHtml('</body></html>');
      return;
    }

    // --- (3)在静态fsp基础上输出二段ssr
    if (fspCacheInfo && secondSsrTask) {
      await renderFspCacheSecondSsr(ctx, {
        writeHtml,
        secondSsrTask,
        fspCacheInfo,
      });
      ctx.perf.time('ssr2_cache');
    }

    const configSsg = checkStreamSSG(ctx, { fcpResult });
    // 直接用缓存渲染场景，配置下发或缓存命中
    /**
     * ssg配置灰度结合html内容的meta标签进行灰度和可控关闭
     */
    const isSSG = fspCacheInfo && (ctx.debugSSG || configSsg.enableAll || (configSsg.enable && fspCacheInfo.cacheDataEnable));
    let onlineSsrHtmlString = '';
    let dropedSSR = false;
    // 用缓存渲染则没必要等ssr返回了（但仍要请求ssr，不然缓存怎么更新）
    if (!ctx.debugFspCacheEnd && !isSSG) {
      // 等待ssr渲染结果
      onlineSsrHtmlString = await onlineSsrTask;
      ctx.perf.time('ssr1');
      if (ctx.debugFspSsrHtml) {
        ctx.log.collect('renderSSR')('htmlString', () => {
          return encodeURIComponent(onlineSsrHtmlString || '');
        });
      }
    } else {
      dropedSSR = true;
      ctx.perf.time('dropssr1');
    }
    // 解析一段ssr返回值值
    const parsedSsrHtml = parseSsrHtml(onlineSsrHtmlString);

    ctx.log.time('startEnd');
    if (fspCacheInfo) {
      ctx.perf.time('end_cache');
      // --- (4.a)存在fsp缓存时，一段ssr失败也可以继续渲染，可选拼接ssr数据
      await renderEndWitchCache(ctx, { parsedSsrHtml, writeHtml, fspCacheInfo, isSSG });
    } else {
      // --- (4.b1)结果校验不通过，走异常降级302
      assert(!!parsedSsrHtml && !ctx.debugHtmlValid, {
        code: checkSsrFailType(onlineSsrHtmlString),
        detail: onlineSsrHtmlString || 'emptyhtml',
      });

      let parsedSsrHtml2 = null;
      if (enableWaitSecondSsr && secondSsrTask) {
        const secondSsrHtmlString = await secondSsrTask;
        ctx.perf.time('ssr2_ssr1');
        parsedSsrHtml2 = parseSsrHtml(secondSsrHtmlString);
      }

      ctx.perf.time('end_ssr1');
      // --- (4.b2)一段ssr渲染，穿插二段ssr内容
      await renderEndOnline(ctx, { parsedSsrHtml, parsedSsrHtml2, writeHtml, fcpResult });
    }

    // 走ssg抛弃了ssr，虽然不参与渲染，但还是需要截取数据缓存
    if (dropedSSR) {
      onlineSsrHtmlString = await onlineSsrTask;
    }

    // 5.ssr数据正常场景时的资源缓存
    if (onlineSsrHtmlString) {
      const afterRenderTask = [
        // 校验ssr页面版本是否需要更新
        checkHtmlBase(ctx, fcpResult, onlineSsrHtmlString),
        // 缓存从ssr模版里读取的fcp实时数据
        checkFcpData(ctx, onlineSsrHtmlString),
      ];
      await Promise.all(afterRenderTask);
      ctx.log.time('afterRender');
    }
  } catch (renderError) {
    const errorCode = (renderError && renderError.code) || 'error';
    const errorMessage = (renderError && renderError.message) || renderError;
    const errorDetailMessage = renderError && renderError.detailMessage;
    // // 以下场景允许以csr渲染兜底，注意dependFail/csrfail事件走302
    switch (errorCode) {
      case 'secure':
      case '302':
      case 'ssrfail':
      case 'faastimeout':
      case 'debug':
        try {
          await renderCsrOnline(ctx, { writeHtml, fcpResult, type: `csr_${errorCode}` });
          ctx.log.time('faillbackCsr');
          return;
        } catch (err) {
          ctx.log.add('fail2csrError', err.message);
        }
    }

    const fallBackUrl = ctx.getFallBackUrl({ _er_failback: errorCode });
    ctx.recordError(renderError, errorDetailMessage || 'renderStreamPage');
    // 可能上面都写html写一半了，已经不能302了，所以要输出个脚本让html执行去降级
    await writeHtml(
      `<script name="render-error">if(${!ctx.isDebug}){location.replace("${fallBackUrl}");console.error("${errorMessage}")}</script></body></html>`,
      'onerr'
    );
    ctx.log.add('fail', () => {
      return {
        detail: encodeURIComponent(errorDetailMessage),
        code: errorCode,
        message: errorMessage,
      };
    });
  }
}

function checkSsrFailType(ssrHtmlString) {
  if (!ssrHtmlString) {
    return 'ssrempty';
  }
  if (ssrHtmlString.indexOf('_____tmd_____') > -1) {
    return 'secure';
  }
  if (ssrHtmlString.indexOf('Redirecting') > -1) {
    return '302';
  }
  if (ssrHtmlString.indexOf('debugssrfail') > -1) {
    return 'debug';
  }
  if (ssrHtmlString.indexOf('faastimeout') > -1) {
    return 'faastimeout';
  }
  return 'ssrfail';
}

/**
 * 远程通用配置和页面自己的配置合并，全局控制的优先级高于页面自身
 * remote
 *  rule1: {
 *     [pageA]: configA
 *  }
 *
 * [pageA]:{
 *  rule1: configB
 *  rule2: configC
 * }
 *
 * ==>
 * ctx.pageConfig = {
 *   rule1: configA
 *   rule2: configC
 * }
 */
async function getRemoteConfig(ctx) {
  // 全局配置
  const result = await getKvCacheJson(ctx.isPre ? `${streamKvConfigAll}_pre` : streamKvConfigAll, ctx.isPre ? 500 : 100);
  const publickCfg = result.data || {};
  if (result.cost) {
    publickCfg._cost = result.cost;
  }
  Object.keys(publickCfg).forEach(ruleName => {
    const rulePageConfig = publickCfg[ruleName];
    const pageRule = rulePageConfig[ctx.pathname];
    if (pageRule) {
      ctx.pageConfig[ruleName] = pageRule;
    }
  });
  return publickCfg;
}
