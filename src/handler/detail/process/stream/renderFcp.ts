import { streamBaseTemplate, streamTagName_preloadcss, streamWinKey_FcpEndTimestamp, streamTagName_removecss, streamScript_FcpRenderEnd, streamTagId_fcp, streamWinKey_FspSuccess, streamWinKey_FeatrueData } from '../../const';
import { initTokenRender, getVersionFromHtml, getCssTagList, checkDevHost } from '../../tools';
import { getHtmlBase } from './checkHtmlBase';
import { getFcpData, getFcpTemplate } from './checkFcpData';

// 首chunk渲染，先传最小兜底，cdn渲染完后再增量渲染
export default async function renderFcp (ctx, { writeHtml }) {
  // 预加载不做渲染fcp，不可不渲染，会导致urc加载异常

  const releaseFirstPerf = ctx.perf.stage('renderFirst');
  // 最小最优避免白屏渲染 + 并行构造基于快照的渲染（快照数据源于ssr）
  let [baseHtmlString, fcpRenderData, fcpTemplateToken] = await Promise.all([renderBasic(ctx), getFcpData(ctx), getFcpTemplate(ctx)]);

  // 获取fcp纯视图html，urc下就不渲染实际fcp内容加速fsp展示
  const fcpViewString = getFcpView(ctx, fcpRenderData, fcpTemplateToken)

  // 完整的fcp渲染结果
  const fcpHtmlList = [];

  // 有基础html才开始渲染fcp
  if (!!baseHtmlString) {
    const htmlVersion = getVersionFromHtml(baseHtmlString);
    // 没有版本号，不是预期项目
    if (!htmlVersion) {
      return
    }

    // 将业务css延后至fcp视图可见后加载,urc场景或没有fcp的就不搞了
    const enableLazyLoadCss = !ctx.debugFcpStyle && !ctx.urcPreloadType && !!fcpViewString && ctx.pageConfig.enableFcpLazyCss && ctx.env.isIOS;

    // 将head内的css改为preload模式，避免阻塞fcp视图渲染
    const cssInfoList = getCssTagList(baseHtmlString);
    if (enableLazyLoadCss) {
      cssInfoList.forEach(info => {
        baseHtmlString = baseHtmlString.replace(info.tag,
          `<link name="${streamTagName_preloadcss}" href="${info.url}" ${info.tag.indexOf("combocss") > -1 ? 'tag="combocss"' : ''} type="text/css" ref="preload" as="style" crossorigin="anonymous" />`
        )
      });
    }

    // 1。插入 <html><head>..</head><body>
    fcpHtmlList.push(baseHtmlString);

    // 2. 插入 <首帧渲染内容/>
    if (fcpViewString) {
      // urc场景，渲染但不可见，因为内容可能后续需要用到
      const style = ctx.urcPreloadType ? 'display:none;' : '';
      fcpHtmlList.push(`<div id="${streamTagId_fcp}" style="${style}">${streamBaseTemplate}${fcpViewString}</div>`);
    }

    // 3. 插入 <fcp控制逻辑/>
    const fcpScriptString = getFcpEndScript(fcpRenderData)
    fcpHtmlList.push(`<script name="fcp-info">${fcpScriptString}</script>`);

    // 添加同步加载css，阻塞fsp可见，避免样式错乱
    if (enableLazyLoadCss) {
      cssInfoList.forEach(n => {
        fcpHtmlList.push(n.tag.replace('<link', `<link name="${streamTagName_removecss}" `))
      });
    }

    if (enableLazyLoadCss) {
      // 添加还原脚本，避免css加载顺序问题导致样式混乱
      fcpHtmlList.push(`<script name="fcp-end">${streamScript_FcpRenderEnd}</script>`);
    }

    // urc场景不执行远程脚本
    if (ctx.pageConfig.fcpEndScript && !ctx.urcPreloadType) {
      fcpHtmlList.push(`<script name="fcp-end-hook" src="${checkDevHost(ctx.pageConfig.fcpEndScript, !!ctx.debugFcpEndScript)}"></script>`)
    }
    fcpHtmlList.push('</body></html>')

    const htmlString = fcpHtmlList.join('');
    await writeHtml(htmlString, 'renderfcp');
    ctx.perf.set('firstlength', htmlString.length);
    releaseFirstPerf();
    return {
      data: fcpRenderData,
      html: htmlString,
      htmlVersion
    };
  }

  return null;
}

async function renderBasic (ctx) {
  const releasePerf = ctx.perf.stage('renderBasic');
  const basic = await getHtmlBase(ctx);
  if (basic) {
    ctx.log.add('renderBasic', function () {
      return {
        html: basic.length,
      };
    });
    const fallBackUrl = ctx.getFallBackUrl({ _er_failback: 'fsptimeout' });
    const enableRedirect = ctx.isDebug ? 'false' : 'true';
    const backupScript = `<script name="backhandler">setTimeout(function (){if (!${streamWinKey_FspSuccess}&&${enableRedirect}){location.replace("${fallBackUrl}");}},4000)</script>`;
    releasePerf();
    return [
      basic,
      backupScript
    ].join('');
  }
  return basic;
}

/**
 * 渲染fcp可见视图
 */
function getFcpView (ctx, renderData, remoteFcpTemplate) {
  const tokenRender = initTokenRender();
  const templateToken = ctx.pageConfig.templateToken || remoteFcpTemplate;
  if (templateToken) {
    try {
      const fcpHtmlString = tokenRender.renderHtmlString(templateToken, renderData);
      return fcpHtmlString;
    } catch (err) {
      ctx.log.add('parseTokenFail', err.message);
    }
  }
  return '';
}

/**
 * 构造fcp信息透传
 */
function getFcpEndScript (fcpRenderData) {
  const groups = [
    // 埋点耗时
    `${streamWinKey_FcpEndTimestamp}=Date.now()`
  ];

  // 插入fcp透传信息
  if (fcpRenderData && fcpRenderData.featrue) {
    try {
      groups.push(`${streamWinKey_FeatrueData}=${JSON.stringify(fcpRenderData.featrue)}`)
    } catch (err) {
    }
  }

  return groups.join(';')
}
