import { setJsonCache, getJsonCache, getBeiJingDateNow, getMeta<PERSON>son, raceTasks } from '../../tools';
import { streamStyle_BottomFit, streamMetaName_FcpData } from '../../const';

// 特殊数据字段，定制处理base64图片，优化首帧大面积白屏问题
const base64Key = 'pic64';
const base64Temp = {};
const cacheBase64 = function (namespace, data) {
  if (!namespace || !data || !data[base64Key]) {
    return;
  }
  base64Temp[namespace] = data[base64Key];
};
const formatBase64 = function (namespace, data) {
  if (!namespace || !data || !base64Temp[namespace]) {
    return false;
  }
  data[base64Key] = base64Temp[namespace];
  return true;
};

export async function checkFcpData(ctx, ssrHtmlString) {
  if (!ssrHtmlString) {
    return;
  }
  const log = ctx.log.collect('renderFcp');
  try {
    let errorMessgae = [];
    const data = getMetaJson(ssrHtmlString, streamMetaName_FcpData);
    let replaceBase64 = false;
    // 最多尝试2次解码
    if (data) {
      replaceBase64 = formatBase64(ctx.fcpDataLocalUrl, data);
      const e = await setJsonCache(ctx.fcpDataLocalUrl, data, 60 * 60 * 24 * 30);
      errorMessgae.push(e.errorMessage);
    }
    log('checkData', () => ({
      data,
      errorMessgae,
      replaceBase64,
    }));
  } catch (err) {
    log('checkData', {
      errorMessgae: err.message,
    });
  }
}

/**
 * 1. 同时请求本地和远程，谁有数据用谁
 * 2. 当本地没有数据就等远程返回直到超时
 * 3. 当没有远程配置时，本地也失败时应该尽快返回不要傻等
 * @param ctx
 * @returns object
 */
export async function getFcpData(ctx) {
  if (ctx.debugFcpNoData || ctx.fcpDisableData) {
    return;
  }
  let hasReturn = false;
  let hasRemote = ctx.fcpDateRemoteUrl && !ctx.debugFcpRemote;
  const addLog = ctx.log.collect('renderFcp');
  let renderData = null;
  try {
    const task = [];
    if (!ctx.debugFcpLocal) {
      task.push(loadLocal());
    }
    if (hasRemote) {
      task.push(loadRemote());
    }
    if (task.length) {
      renderData = await raceTasks(task, 200);
      hasReturn = true;
    }
  } catch (err) {
    addLog('loadError', err.message);
  }

  // 附加一些环境变量
  const e = formatData(renderData);
  addLog('loadSuccess', e);
  return e;

  async function loadRemote() {
    const releasePerf = ctx.perf.stage('loadRemoteData');
    const result = await fetch(ctx.fcpDateRemoteUrl, {
      cdnProxy: true, // 必须是接入阿里cdn的域名才有加速
    })
      .then(res => res && res.json())
      .catch(err => {
        addLog('remoteErrorMessage', err.message);
        return '';
      });
    addLog('remote', result);
    // 替换一下本地缓存里的base64图
    cacheBase64(ctx.fcpDataLocalUrl, result);
    if (!result) {
      return null;
    }
    releasePerf();
    if (!hasReturn) {
      addLog('source', 'remote');
    }
    return result;
  }

  // 本地有数据就释放，无数据就一直Hold
  function loadLocal() {
    return new Promise(res => {
      // cdn本地缓存
      const releasePerf = ctx.perf.stage('loadLocalData');
      getJsonCache(ctx.fcpDataLocalUrl).then(cacheResult => {
        releasePerf();
        addLog('local', cacheResult);
        if (cacheResult.data) {
          if (!hasReturn) {
            addLog('source', 'local');
          }
          res(cacheResult.data);
        } else if (!hasRemote) {
          // 避免傻等超时
          res('');
        }
      });
    });
  }

  // 根据是否是ios系统，给予额外的入参控制底部按钮高度
  function formatData(data) {
    data = data || {};
    const immersiveState = ctx.immersiveState || {};
    const exParams: any = {
      urctype: ctx.urcPreloadType,
      urcpage: ctx.urcPreloadPage,
      statusBarHeight: immersiveState.enable ? immersiveState.statusBarHeight : 0,
    };

    // -- 环境定制，只有命中时携带
    if (ctx.env.isIOS || ctx.env.isHarmony) {
      exParams.bottomBar = `padding-bottom:${streamStyle_BottomFit};`;
      exParams.iosPadding = streamStyle_BottomFit;
    }
    if (ctx.env.isXhsMiniAppH5) {
      exParams.isXhsMiniAppH5 = 1;
    }
    if (ctx.env.isWeChatMiniProgramH5) {
      exParams.isWeChatMiniProgramH5 = 1;
    }
    // --
    const featrue = data.featrue || {};
    const { e, ...others } = data;
    // 渲染fcp的模版上下文数据
    let renderContext = {
      ...(ctx.pageConfig.fcpDefaultData || {}),
      ...others,
      ...exParams,
      // 特殊字段，会带去window上
      featrue: {
        ...featrue,
        ...exParams,
      },
    };

    try {
      if (ctx.debugFcpMoreData) {
        const moreData = JSON.parse(ctx.debugFcpMoreData);
        renderContext = {
          ...renderContext,
          ...(moreData || {}),
        };
      }
    } catch (e) {}

    if (data.expire > 0 && e) {
      const current = getBeiJingDateNow();
      // 过期删除固定字段 e
      if (current > data.expire) {
        ctx.log.add('expireData', e);
        return renderContext;
      }
    }
    // 未过期加回
    renderContext.e = e;
    return renderContext;
  }
}
