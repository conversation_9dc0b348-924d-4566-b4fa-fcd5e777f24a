import { parseSsrHtml, getVersionFromHtml, getMetaContent, getBeiJingDateNow, getJsonObjectString } from '../../tools';
import {
  streamBaseTemplate,
  streamTagId_fspcachestyle,
  streamScript_CloseFcp,
  streamScript_FspCacheEnd,
  streamScript_FspValidTime,
  streamMetaName_ExpireTime,
  streamMetaName_CacheTime,
  streamWinKey_PerfInfo,
  streamWinKey_FspCacheViewEndTime,
  streamWinKey_FspCacheDataEnable,
  streamWinKey_Hook_FspCacheEnd,
  streamWinKey_Hook_FspOnlineEnd,
  streamWinKey_Hook_FspCacheDataEnd,
  streamWinKey_FspCacheSuccess,
  streamCss_FspCacheStart,
  streamStyle_BottomFit,
  streamBizScriptStartReg,
  getVersionFormatScripts,
  getStaticHtmlReplaceScripts,
} from '../../const';

// 渲染原始ssr
export async function renderFspOnline(ctx, { parsedSsrHtml, parsedSsrHtml2, writeHtml, fcpResult }) {
  const runFspEndHook = `if(typeof ${streamWinKey_Hook_FspOnlineEnd} == 'function'){${streamWinKey_Hook_FspOnlineEnd}();}`;

  // 在ssr视图可见之后，ssr数据赋值之前，插入耗时计算并隐藏首段流式
  const scripts = [
    streamScript_FspValidTime, // fsp埋点
    streamScript_CloseFcp, // 关闭fcp视图
    `${streamWinKey_PerfInfo}=${ctx.perf.toJsonString()}`, // 传递性能数据
    runFspEndHook, // 渲染钩子调用
  ];

  // 页面自定义脚本
  if (ctx.pageConfig.fspEndScriptString) {
    scripts.push(ctx.pageConfig.fspEndScriptString);
  }

  // 一段ssr展示内容
  const mainViewList = [
    fcpResult ? '' : parsedSsrHtml.beforeView, // 有fcp的时候，不需要再渲染body以上部分
    parsedSsrHtml.beforeData, // 一段ssr视图部分
    `<script name="fsp-end-real">${scripts.join(';')}</script>`, // er脚本
  ];

  // asap输出一段纯展示内容
  await writeHtml(mainViewList.join(''), 'beforesecond');

  // 如果有二段ssr，插入二段内容
  if (parsedSsrHtml2) {
    if (ctx.isDevMode || parsedSsrHtml2.version == parsedSsrHtml.version) {
      const secondSsrString = getSecondSsrContent(ctx, parsedSsrHtml2);
      await writeHtml(secondSsrString, 'fspsecond');
    }
  }

  const remainViewList = [
    parsedSsrHtml.beforeEnd, // 一段ssr数据及业务js加载
  ];

  if (fcpResult) {
    // 有fcp时需插入css版本修复，避免head内的css版本和js不一致
    const fixScript = checkEndScript(ctx, fcpResult, parsedSsrHtml);
    if (fixScript) {
      remainViewList.push(fixScript);
    }
    await writeHtml(remainViewList.join(''), 'fsponline');
    return;
  }

  // 纯单独ssr渲染
  ctx.log.add('backSSR', '1');
  // 没有fcp，全量回退SSR数据
  await writeHtml(remainViewList.join(''), 'backSSR');
}

// 当已经渲染了fsp缓存时，结合实时ssr结果做兼容
export async function renderFspOnlineWithCache(ctx, { parsedSsrHtml, writeHtml, fspCacheInfo, fcpResult }) {
  const addLog = ctx.log.collect('renderFspWithCache');

  // 「缓存的数据+脚本」没有ssr数据
  if (!parsedSsrHtml) {
    await render(fspCacheInfo.beforeEnd, true);
    addLog('directcache', 'emptyssr');
    return;
  }

  // 「输出缓存的数据+脚本」实时ssr与缓存的js版本不一致
  if (!ctx.isDevMode && parsedSsrHtml.version !== fspCacheInfo.version) {
    await render(fspCacheInfo.beforeEnd, true);
    addLog('directcache', `ssrV(${parsedSsrHtml.version})!==cacheV(${fspCacheInfo.version})`);
    return;
  }

  // 「输出实时ssr的数据+脚本」
  // 用ssr的数据结合静态的首屏渲染
  await render(parsedSsrHtml.beforeEnd, false);
  addLog('success', '1');

  async function render(beforeEnd, isCache) {
    const renderScripts = [streamScript_FspValidTime];
    if (isCache) {
      renderScripts.push(`${streamWinKey_FspCacheDataEnable}=true;`);
    }
    renderScripts.push(`${streamWinKey_PerfInfo}=${ctx.perf.toJsonString()}`);

    const renderContent = [`<script name="fsp-end-${isCache ? 'cache' : 'real'}">${renderScripts.join(';')}</script>`];

    // 查找业务脚本执行之前，ssr数据之后的位置，插入钩子执行逻辑
    let insertIndex = -1;
    const [bizScriptTag] = beforeEnd.match(streamBizScriptStartReg) || [];
    if (bizScriptTag) {
      insertIndex = beforeEnd.indexOf(bizScriptTag);
    }
    if (insertIndex > -1) {
      renderContent.push(beforeEnd.slice(0, insertIndex));
      const runAfterDataHook = `if(typeof ${streamWinKey_Hook_FspCacheDataEnd} == 'function'){${streamWinKey_Hook_FspCacheDataEnd}();}`;
      renderContent.push(`<script name="fsp-cache-afterdata">${runAfterDataHook}</script>`);
      renderContent.push(beforeEnd.slice(insertIndex));
    } else {
      renderContent.push(beforeEnd);
    }

    // 删除采用缓存视图时，注入的样式等
    renderContent.push(`<script name="fsp-cache-fix">${streamScript_FspCacheEnd}</script>`);
    const fixScript = checkEndScript(ctx, fcpResult, parsedSsrHtml);
    if (fixScript) {
      renderContent.push(fixScript);
    }
    await writeHtml(renderContent.join(''), 'fspwithcache');
  }
}

// 渲染静态ssr结果，需结合实时ssr结果进一步渲染
export async function renderFspCache(ctx, { writeHtml, parsedCacheHtml, fcpResult }) {
  const release = ctx.perf.stage('renderFspCache');
  const addLog = ctx.log.collect('renderFspCache');
  // --- html基础结构检测
  if (!parsedCacheHtml) {
    addLog('fail', 'invalidhtml');
    return false;
  }

  // --- 版本一致性检测
  const fspCacheVersion = getVersionFromHtml(parsedCacheHtml.htmlText);
  if (!fspCacheVersion) {
    return false;
  }
  // 当有fcp时应该和fcp保持一致，否则样式有错乱风险（css是在head里加载的）
  if (!ctx.isDevMode && fcpResult && fcpResult.htmlVersion !== fspCacheVersion && !ctx.debugFspCacheRender) {
    addLog('fail', `fcpV(${fcpResult.htmlVersion})!==cacheV(${fspCacheVersion})`);
    return false;
  }
  addLog('version', fspCacheVersion);

  // --- 超时检测
  const updateTime = +getMetaContent(parsedCacheHtml.htmlText, streamMetaName_CacheTime);
  const now = getBeiJingDateNow();
  const timepass = now - updateTime;
  const max = ctx.debugFspCacheTime ? +ctx.debugFspCacheTime : 1000 * 60 * 60 * 24;
  if (timepass > max && !ctx.debugFspCacheRender) {
    addLog('fail', `now(${now})-update(${updateTime})>${max}`);
    return false;
  }
  addLog('update', updateTime);

  // --- 过期检测
  const expireTime = getMetaContent(parsedCacheHtml.htmlText, streamMetaName_ExpireTime);
  if (expireTime > 0 && now > expireTime && !ctx.debugFspCacheRender) {
    addLog('fail', `expire(${expireTime})>now(${now})`);
    return false;
  }

  // -- 正式截取渲染
  const group = [];
  // 无fcp场景，头部也需要输出
  if (!fcpResult) {
    group.push(parsedCacheHtml.beforeView);
    // 记得输出触发流式上屏的内容
    group.push(streamBaseTemplate);
    ctx.perf.set('fspCacheNoFcp', '1');
    addLog('tip', 'nofcp');
  }
  // 启缓存视图时注入约定样式
  group.push(`<style id="${streamTagId_fspcachestyle}">${makeFspCacheStyle()}</style>`);
  group.push(parsedCacheHtml.beforeData);
  const params = getJsonObjectString(ctx.pageConfig.fspCacheHookParams);
  // 插入缓存结尾脚本，关闭fcp
  const scripts = [
    streamScript_CloseFcp,
    `${streamWinKey_FspCacheSuccess}=true`,
    `${streamWinKey_FspCacheViewEndTime}=Date.now()`,
    `if(typeof ${streamWinKey_Hook_FspCacheEnd} == 'function'){${streamWinKey_Hook_FspCacheEnd}(${params})}`,
  ];
  if (ctx.pageConfig.fspEndScriptString) {
    scripts.push(ctx.pageConfig.fspEndScriptString);
  }
  group.push(`<script name="fsp-cache">${scripts.join(';')}</script>`);
  const htmlString = group.join('');
  await writeHtml(htmlString, 'renderfspcache');
  ctx.perf.set('fspCacheUseLength', htmlString.length);
  addLog('length', htmlString.length);
  release();

  return {
    beforeEnd: parsedCacheHtml.beforeEnd,
    version: fspCacheVersion,
  };

  function makeFspCacheStyle() {
    const styles = [
      // 基础约定css，隐藏/屏蔽一些模块
      streamCss_FspCacheStart,
    ];

    // 状态栏高度
    const { _fli_barHeight } = ctx.requestCookie;
    const { isIOS, isHarmony, isTaobao, isTrip } = ctx.env;
    // 不同机型底部bar适配
    styles.push(`.td-erbottom{padding-bottom:${isIOS || isHarmony ? streamStyle_BottomFit : '0'} !important; }`);

    // 以飞猪优先
    styles.push(`.tb-ertaobao{opacity: ${isTaobao ? '1' : '0'} }`);
    styles.push(`.tb-erfliggy{opacity: ${isTrip || !isTaobao ? '1' : '0'} }`);
    if (_fli_barHeight && _fli_barHeight > 0) {
      styles.push(`.td-erstatusbar{ height:${_fli_barHeight}px; }`);
    }

    return styles.join('');
  }
}

function checkEndScript(ctx, fcpResult, parsedSsrHtml) {
  // 版本一致，无需修正
  if (fcpResult && fcpResult.htmlVersion && parsedSsrHtml && parsedSsrHtml.version == fcpResult.htmlVersion) {
    return '';
  }
  if (ctx.groupname) {
    const scripts = getVersionFormatScripts(ctx.groupname);
    if (!scripts) {
      return;
    }
    return `<script name="fsp-css-fix">${scripts}</script>`;
  }
  return '';
}

export async function renderFspCacheSecondSsr(ctx, { writeHtml, fspCacheInfo, secondSsrTask }) {
  const addLog = ctx.log.collect('renderFspCacheSecond');
  try {
    const htmlSrring = await secondSsrTask;

    const parsed = parseSsrHtml(htmlSrring);
    if (!parsed) {
      addLog('fail', 'invalidhtml');
      return;
    }
    addLog('htmlString', htmlSrring.length);

    // 当有fcp时应该和fcp保持一致，否则样式有错乱风险（css是在head里加载的）
    if (!ctx.isDevMode && fspCacheInfo.version !== parsed.version) {
      addLog('fail', `fspV(${fspCacheInfo.version})!==ssrV(${parsed.version})`);
      return;
    }

    await writeHtml(getSecondSsrContent(ctx, parsed), 'fspcachesecond');
  } catch (err) {
    addLog('error', err.message);
  }
}

function getSecondSsrContent(ctx, parsed) {
  const group = [
    // 二段主体隐藏，内部模块基于dom去替换
    '<div id="fsp-cache-secondssr" style="display:none;">',
    parsed.beforeData,
    '</div>',
    `<script name="second-replace">${getStaticHtmlReplaceScripts(!!ctx.isDebug || ctx.isDevMode)}</script>`,
  ];

  if (parsed.dataScript) {
    group.push(
      parsed.dataScript.replace('window.__INITIAL_DATA__', 'window.__SECOND_DATA__').replace(/data-from=['"]server['"]/, 'data-from="server2"')
    );
  }
  return group.join('');
}
