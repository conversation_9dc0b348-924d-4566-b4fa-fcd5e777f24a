import {
  parseSsrHtml,
  getVersionFromHtml,
  getMetaContent,
  getBeiJingDateNow,
  getJsonObjectString,
  checkStyleMiss,
  makeAsyncQueue,
} from '../../tools';
import {
  streamBaseTemplate,
  streamTagId_fspcachestyle,
  streamScript_CloseFcp,
  streamScript_FspCacheEnd,
  streamScript_FspValidTime,
  streamWinKey_FSP,
  streamMetaName_ExpireTime,
  streamMetaName_CacheTime,
  streamMetaName_FspCacheDataEnable,
  streamWinKey_PerfInfo,
  streamWinKey_FspCacheViewEndTime,
  streamWinKey_FspCacheDataEnable,
  streamWinKey_Hook_FspCacheEnd,
  streamWinKey_Hook_FspOnlineEnd,
  streamWinKey_Hook_FspCacheDataEnd,
  streamWinKey_FspCacheSuccess,
  streamWinKey_SsgRender,
  streamCss_FspCacheStart,
  streamStyle_BottomFit,
  streamBizScriptStartReg,
  getStaticHtmlReplaceScripts,
} from '../../const';
import { TAOBAO_PERF_SCRIPT } from '../../../../utils';

// 结合fcp渲染状态，追加原始ssr内容
export async function renderEndOnline(ctx, { parsedSsrHtml, multiSsrTaskList, writeHtml, fcpResult }) {
  const runFspEndHook = `if(typeof ${streamWinKey_Hook_FspOnlineEnd} == 'function'){${streamWinKey_Hook_FspOnlineEnd}();}`;

  // 在ssr视图可见之后，ssr数据赋值之前，插入耗时计算并隐藏首段流式
  const fspEndScripts = [
    // fsp埋点
    streamScript_FspValidTime,
    // 关闭fcp视图
    streamScript_CloseFcp,
    // 淘宝独有的埋点脚本，插入fsp视图之后
    ctx.env.isTaobao || ctx.debugTaobaoPerf ? TAOBAO_PERF_SCRIPT.fspEnd({ bizStage: 'puressr' }) : '',
    // 传递性能数据
    `${streamWinKey_PerfInfo}=${ctx.perf.toJsonString()}`,
    // 渲染钩子调用
    runFspEndHook,
    // 页面自定义脚本
    ctx.pageConfig.fspEndScriptString ? ctx.pageConfig.fspEndScriptString : '',
  ];

  // 一段ssr展示内容
  const mainViewList = [
    // 有fcp的时候，不需要再渲染body以上部分
    fcpResult ? '' : parsedSsrHtml.beforeView,
    // 校验是否需要提前修正css
    checkStyleMiss(ctx, fcpResult && fcpResult.html, parsedSsrHtml.beforeView),
    // 一段ssr视图部分
    parsedSsrHtml.beforeData,
    `<script name="fsp-end-real">${fspEndScripts.join(';')}</script>`, // er脚本
  ];

  // asap输出一段纯展示内容
  await writeHtml(mainViewList.join(''), 'beforesecond');

  const enableWaitAllSsr = ctx.debugSsrWait || ctx.pageConfig.waitAllSsr;
  // 如果有多段ssr，且配置强制合并
  if (multiSsrTaskList.length && enableWaitAllSsr) {
    await renderMergeMultiSsr(ctx, {
      writeHtml,
      multiSsrTaskList,
      renderVersion: parsedSsrHtml.version,
    });
  }

  const remainViewList = [
    parsedSsrHtml.beforeEnd, // 一段ssr数据及业务js加载
  ];

  // 没有fcp，全量回退SSR数据
  await writeHtml(remainViewList.join(''), 'backSSR');
}

export async function renderEndOnPrepubError(writeHtml, onlineSsrHtmlString) {
  await writeHtml(onlineSsrHtmlString, 'prepubError');
}

// 命中fsp缓存场景，合并ssr实时数据和业务js
export async function renderEndWitchCache(ctx, { parsedSsrHtml, writeHtml, fspCacheInfo, isSSG }) {
  const addLog = ctx.log.collect('renderFspWithCache');

  // 「缓存的数据+脚本」没有ssr数据
  if (!parsedSsrHtml) {
    await render(fspCacheInfo.beforeEnd, true);
    addLog('directcache', 'emptyssr');
    return;
  }

  // 「输出缓存的数据+脚本」实时ssr与缓存的js版本不一致
  if (!ctx.isDevMode && parsedSsrHtml.version !== fspCacheInfo.version) {
    await render(fspCacheInfo.beforeEnd, true);
    addLog('directcache', `ssrV(${parsedSsrHtml.version})!==cacheV(${fspCacheInfo.version})`);
    return;
  }

  // 「输出实时ssr的数据+脚本」
  // 用ssr的数据结合静态的首屏渲染
  await render(parsedSsrHtml.beforeEnd, false);
  addLog('success', '1');

  async function render(beforeEnd, isCache) {
    const renderScripts = [streamScript_FspValidTime];
    if (isCache) {
      renderScripts.push(`${streamWinKey_FspCacheDataEnable}=true;`);
    }
    if (isSSG) {
      renderScripts.push(`${streamWinKey_SsgRender}=true;`);
    }
    renderScripts.push(`${streamWinKey_PerfInfo}=${ctx.perf.toJsonString()}`);

    const renderContent = [`<script name="fsp-end-${isCache ? 'cache' : 'real'}">${renderScripts.join(';')}</script>`];

    // 查找业务脚本执行之前，ssr数据之后的位置，插入钩子执行逻辑
    let insertIndex = -1;
    const [bizScriptTag] = beforeEnd.match(streamBizScriptStartReg) || [];
    if (bizScriptTag) {
      insertIndex = beforeEnd.indexOf(bizScriptTag);
    }
    if (insertIndex > -1) {
      renderContent.push(beforeEnd.slice(0, insertIndex));
      const runAfterDataHook = `if(typeof ${streamWinKey_Hook_FspCacheDataEnd} == 'function'){${streamWinKey_Hook_FspCacheDataEnd}();}`;
      renderContent.push(`<script name="fsp-cache-afterdata">${runAfterDataHook}</script>`);
      renderContent.push(beforeEnd.slice(insertIndex));
    } else {
      renderContent.push(beforeEnd);
    }

    // 在fsp缓存结束,业务js执行成功后删除er兼容样式
    if (!ctx.debugFspCacheEndStyle) {
      // 删除采用缓存视图时，注入的样式等
      renderContent.push(`<script name="fsp-cache-fix">${streamScript_FspCacheEnd}</script>`);
    }
    await writeHtml(renderContent.join(''), 'fspwithcache');
  }
}

// 命中fsp缓存场景，输出缓存的fsp内容
export async function renderFspCache(ctx, { writeHtml, parsedCacheHtml, fcpResult }) {
  const release = ctx.perf.stage('renderFspCache');
  const addLog = ctx.log.collect('renderFspCache');
  // --- html基础结构检测
  if (!parsedCacheHtml) {
    addLog('renderFail', 'invalidhtml');
    return false;
  }

  // --- 版本一致性检测
  const fspCacheVersion = getVersionFromHtml(parsedCacheHtml.htmlText);
  if (!fspCacheVersion) {
    return false;
  }

  // 当有fcp时应该和fcp保持一致，否则样式有错乱风险（css是在head里加载的）
  if (!ctx.isDevMode && fcpResult && fcpResult.htmlVersion !== fspCacheVersion && !ctx.debugFspCacheRender) {
    addLog('renderFail', `fcpV(${fcpResult.htmlVersion})!==cacheV(${fspCacheVersion})`);
    return false;
  }
  addLog('version', fspCacheVersion);

  // --- 超时检测
  const updateTime = +getMetaContent(parsedCacheHtml.htmlText, streamMetaName_CacheTime);
  const now = getBeiJingDateNow();
  const timepass = now - updateTime;
  const max = ctx.debugFspCacheTime ? +ctx.debugFspCacheTime : 1000 * 60 * 60 * 24;
  if (!ctx.isDevMode && timepass > max && !ctx.debugFspCacheRender) {
    addLog('renderFail', `now(${now})-update(${updateTime})>${max}`);
    return false;
  }
  addLog('update', updateTime);

  // --- 过期检测
  const expireTime = getMetaContent(parsedCacheHtml.htmlText, streamMetaName_ExpireTime);
  if (!ctx.isDevMode && expireTime > 0 && now > expireTime && !ctx.debugFspCacheRender) {
    addLog('renderFail', `expire(${expireTime})>now(${now})`);
    return false;
  }

  // -- 正式截取渲染
  const group = [];
  // 无fcp场景，头部也需要输出
  if (!fcpResult) {
    group.push(parsedCacheHtml.beforeView);
    // 记得输出触发流式上屏的内容
    group.push(streamBaseTemplate);
    ctx.perf.set('fspCacheNoFcp', '1');
    addLog('tip', 'nofcp');
  } else {
    // 校验是否样式和fcp里的一致
    group.push(checkStyleMiss(ctx, fcpResult && fcpResult.html, parsedCacheHtml.beforeView));
  }
  // 启缓存视图时注入约定样式
  group.push(`<style id="${streamTagId_fspcachestyle}">${makeFspCacheStyle()}</style>`);
  group.push(parsedCacheHtml.beforeData);
  const params = getJsonObjectString(ctx.pageConfig.fspCacheHookParams);
  const fspEndScripts = [
    // 插入缓存结尾脚本，关闭fcp
    streamScript_CloseFcp,
    // 淘宝独有的埋点脚本，插入fsp视图之后
    ctx.env.isTaobao || ctx.debugTaobaoPerf ? TAOBAO_PERF_SCRIPT.fspEnd({ bizStage: 'fspcache' }) : '',
    `${streamWinKey_FspCacheSuccess}=true`,
    `${streamWinKey_FSP}=Date.now()`,
    `${streamWinKey_FspCacheViewEndTime}=${streamWinKey_FSP}`,
    `if(typeof ${streamWinKey_Hook_FspCacheEnd} == 'function'){${streamWinKey_Hook_FspCacheEnd}(${params})}`,
    ctx.pageConfig.fspEndScriptString ? ctx.pageConfig.fspEndScriptString : '',
  ];
  group.push(`<script name="fsp-cache">${fspEndScripts.join(';')}</script>`);
  const htmlString = group.join('');
  await writeHtml(htmlString, 'renderfspcache');
  ctx.perf.set('fspCacheUseLength', htmlString.length);
  addLog('renderSuccess', htmlString.length);
  release();

  const metaConfig = !!getMetaContent(parsedCacheHtml.htmlText, streamMetaName_FspCacheDataEnable);

  // fsp缓存一般仅用视图，可指定完全弃用ssr沿用缓存
  return {
    beforeEnd: parsedCacheHtml.beforeEnd,
    cacheDataEnable: !!metaConfig,
    version: fspCacheVersion,
  };

  function makeFspCacheStyle() {
    const styles = [
      // 基础约定css，隐藏/屏蔽一些模块
      streamCss_FspCacheStart,
    ];

    // 状态栏高度
    const immersiveState = ctx.immersiveState;
    const { isIOS, isHarmony, isTaobao, isTrip } = ctx.env;
    // 不同机型底部bar适配
    styles.push(`.td-erbottom{padding-bottom:${isIOS || isHarmony ? streamStyle_BottomFit : '0'} !important; }`);

    // 以飞猪优先
    styles.push(`.td-ertaobao{opacity: ${isTaobao ? '1' : '0'} }`);
    styles.push(`.td-erfliggy{opacity: ${isTrip ? '1' : '0'} }`);
    addLog('statusHeight', immersiveState.statusBarHeight);
    // 匹配各端沉侵头
    if (immersiveState.statusBarHeight > -1) {
      // 适配多种头的组织方式
      styles.push(`.td-erstatusbar{ padding-top:${immersiveState.statusBarHeight}px !important;  }`);
      styles.push(`.td-erstatusbar-height{ height:${immersiveState.statusBarHeight}px !important;  }`);
    }
    if (immersiveState.enable) {
      styles.push(`.td-erimmersive{ display:flex !important; }`);
    } else {
      styles.push(`.td-erimmersive{ display:none !important; }`);
    }

    return styles.join('');
  }
}

export async function renderFspCacheWithMultiSsr(ctx, { writeHtml, fspCacheInfo, multiSsrTaskList }) {
  await renderMergeMultiSsr(ctx, { writeHtml, multiSsrTaskList, renderVersion: fspCacheInfo.version });
}

export async function renderMergeMultiSsr(ctx, { writeHtml, multiSsrTaskList, renderVersion }) {
  const addLog = ctx.log.collect('renderMultiSsr');
  const queue = makeAsyncQueue();
  return Promise.all(multiSsrTaskList.map(renderPart));

  async function renderPart(requestTask, index) {
    const log = (k, v) => addLog(`ssr${index}/${k}`, v);
    try {
      const result = await requestTask;
      if (!result || !result.htmlString) {
        log('mergeFail', result.message);
        return;
      }
      const parsed = parseSsrHtml(result.htmlString);
      if (!parsed) {
        log('mergeFail', 'invalidhtml');
        return;
      }
      if (!ctx.isDevMode && renderVersion !== parsed.version) {
        log('mergeFail', `renderV(${renderVersion})!==ssrV(${parsed.version})`);
        return;
      }
      log('mergeLength', result.htmlString.length);
      const contentString = getContent(parsed, index, result.requestKey);
      await queue.start(() =>
        writeHtml(contentString, `mergeMultiSsr_${index}`).catch(() => {
          // do nothing
        })
      );
    } catch (err) {
      log('mergeError', err.message);
    }
  }

  function getContent(parsed, taskIndex, requestKey) {
    // 第几个ssr，从1开始
    const ssrIndex = taskIndex + 2;
    // 兼容历史
    const trackSuffix = taskIndex == 0 ? '' : ssrIndex;
    const rootId = `_fsp_task_${taskIndex}`;
    const group = [
      // 二段主体隐藏，内部模块基于dom去替换
      `<div id="${rootId}" style="display:none;">`,
      parsed.beforeData,
      '</div>',
      `<script name="${rootId}">${getStaticHtmlReplaceScripts(!!ctx.isDebug || ctx.isDevMode, rootId, trackSuffix, taskIndex)}</script>`,
    ];

    if (parsed.dataScript) {
      const names = [`window.__INITIAL_DATA__${ssrIndex}`];
      // 兼容线上独立二段
      if (taskIndex == 0) {
        names.push('window.__SECOND_DATA__');
      }
      if (requestKey && requestKey != ssrIndex) {
        names.push(`window.__INITIAL_DATA__${requestKey}`);
      }
      group.push(
        parsed.dataScript.replace('window.__INITIAL_DATA__', names.join('=')).replace(/data-from=['"]server['"]/, `data-from="server${ssrIndex}"`)
      );
    }
    return group.join('');
  }
}
