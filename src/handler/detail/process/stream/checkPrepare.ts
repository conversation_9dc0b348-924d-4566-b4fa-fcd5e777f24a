import { getFspCache } from './checkFspCache';
import { getHtmlBase } from './checkHtmlBase';
import { getFcpData } from './checkFcpData';
import { getCsrHtml } from './renderCsr';

export default function (ctx, tasks = []) {
  if (ctx.isPrepare) {
    return Promise.all([
      ...tasks.map(n => n(ctx)),
      // 基础Html
      getHtmlBase(ctx),
      // 对应页面的fcp数据
      getFcpData(ctx),
      // 对应页面的fsp缓存
      getFspCache(ctx),
      // 对应页面的csr资源
      getCsrHtml(ctx)
    ]).then(e => 'window.__er_prepare_success=1').catch(err => err.message);
  }
}
