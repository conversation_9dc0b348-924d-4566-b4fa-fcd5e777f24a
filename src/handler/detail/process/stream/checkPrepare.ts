import { getFspCache } from './checkFspCache';
import { getHtmlBase } from './checkHtmlBase';
import { getFcpData } from './checkFcpData';
import { getCsrHtml } from './renderCsr';
import { projectKvMap } from '../../const';
import { getKvCacheJson } from '../../tools';

export default function (ctx, tasks = []) {
  if (ctx.isPrepare) {
    const finalTaskList = [
      ...tasks.map(n => n(ctx)),
      // 基础Html
      getHtmlBase(ctx),
      // 对应页面的fcp数据
      getFcpData(ctx),
      // 对应页面的fsp缓存
      getFspCache(ctx),
      // 对应页面的csr资源
      getCsrHtml(ctx)
    ];
    // 最多加载多少条，避免太多达到cdn请求上限32（预留3个）
    let maxLength = 29 - finalTaskList.length;
    Object.keys(projectKvMap).forEach(key => {
      if (maxLength > 0) {
        maxLength--;
        finalTaskList.push(
          getKvCacheJson(projectKvMap[key])
        )
      }
    });

    return Promise.all(finalTaskList).then(e => 'window.__er_prepare_success=1').catch(err => err.message);
  }
}
