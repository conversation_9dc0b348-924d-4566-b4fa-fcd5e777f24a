import { assert, raceTasks, checkOptionMatch } from '../../tools';
import { checkSsrServer, getLogName } from './checkSsr';
import { getMultiSsrPreloadQuery } from '../../const';

// 多段并行SSR
export function getMultiSsrTask(ctx, { optionContext }) {
  const addLog = ctx.log.collect('renderMultiSsr');
  const isPreload = ctx.isPreload;
  const list = ctx.pageConfig.multiSsr || [];
  const taskList = [];
  try {
    fitSecondSsr(ctx, list);
    for (let i = 0, len = list.length; i < len; i++) {
      const rule = list[i];
      // 默认在预加载阶段，仅执行核心SSR
      if (isPreload && !rule.preSsr) {
        addLog(`ssr${0}/match`, { result: false, message: 'preloaddisable' });
        continue;
      }
      const matchInfo = checkOptionMatch(rule, optionContext);
      addLog(`ssr${0}/match`, matchInfo);
      // 不启用
      if (!matchInfo.result) {
        continue;
      }
      const task = makeSsrTask(ctx, {
        index: i,
        rule,
        optionContext,
        addLog(key, val) {
          addLog(`ssr${i}/${key}`, val);
        },
      });
      taskList.push(task);
    }
  } catch (err) {
    addLog('requestError', err.message);
  }

  return taskList;
}

// 兼容历史二段ssr配置
function fitSecondSsr(ctx, list) {
  const rule = ctx.pageConfig.secondssr;
  if (rule) {
    list.unshift({
      ...rule,
      timeout: ctx.pageConfig.secondSsrTimeout || 500,
      query: {
        _cache_replace_: 1,
      },
    });
  }
}

async function makeSsrTask(ctx, { index, rule, addLog, optionContext }) {
  try {
    const requestKey = rule.requestKey || `${index + 2}`;
    const ssrServerConfig = checkSsrServer(ctx, {
      optionContext: { ...optionContext, _er_multi_ssr: requestKey },
      log: addLog,
    });
    if (ctx.debugSsrMulti > -1 && +ctx.debugSsrMulti === index) {
      throw new Error(`test_multi_${index}`);
    }
    // 真实请求faas函数的地址
    const addtionQuery = {
      ...(rule.query || {}),
      _er_multi_ssr: requestKey,
      ...getMultiSsrPreloadQuery(ctx.query, index),
    };
    const faasRequestUrl = ctx.getRequestFaasUrl({
      pathname: ssrServerConfig.pathname,
      host: ssrServerConfig.host,
      query: addtionQuery,
    });
    addLog(`requestUrl`, faasRequestUrl);

    const start = Date.now();
    ctx.perf.set(`faasserver${index + 2}`, getLogName(ssrServerConfig));
    const ssrRequest = fetch(faasRequestUrl, {
      headers: ctx.requestFaasHeadersInstance,
    }).then(res => res.text());

    const timeout = ctx.isDevMode ? 5000 : rule.timeout || 500;
    const htmlText = await raceTasks([ssrRequest], timeout, 'timeout');
    ctx.perf.set(`loadSSR${index + 2}`, Date.now() - start);
    if (!htmlText || htmlText.indexOf('data-cacheholder') < 0) {
      addLog(`requestFail`, 'invalid');
      return {
        message: 'invalid',
        htmlString: null,
      };
    }
    return {
      requestKey,
      htmlString: htmlText,
    };
  } catch (err) {
    return {
      message: err.message,
      htmlString: null,
    };
  }
}
