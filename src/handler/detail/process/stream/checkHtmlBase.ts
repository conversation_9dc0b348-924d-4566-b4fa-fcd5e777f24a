import {
  getVersionFromHtml,
  setJsonCache,
  getJsonCache,
  raceTasks,
  parseSsrHtml,
} from '../../tools';

// 缓存htmlBase
export async function checkHtmlBase (ctx, fcpResult, ssrHtmlString) {
  const addLog = ctx.log.collect('checkHtmlBase');
  const fcpVersion = fcpResult && fcpResult.htmlVersion;
  // 没有首屏渲染或ssr版本更新了后，更新缓存的基础html
  if (fcpVersion) {
    const parseVersion = getVersionFromHtml(ssrHtmlString);
    const needUpdate = fcpVersion !== parseVersion || ctx.debugUpdateBase;
    addLog({
      fcpVersion,
      parseVersion,
      needUpdate
    })
    if (fcpVersion !== parseVersion || ctx.debugUpdateBase) {
      await cacheBasicHtml();
    }
    return;
  }
  addLog({
    noBasic: '1'
  })
  await cacheBasicHtml();

  // 获取一屏拼接所需的基础html，添加对css和js的preload
  function getFirstScreenBaseHtml () {
    const parsed = parseSsrHtml(ssrHtmlString);
    if (!parsed) {
      return ''
    }
    const { beforeView, beforeEnd } = parsed;
    // 基于浏览器标签对后续的js做预加载
    const scripts = [...beforeEnd.matchAll(/<script.+?src='((?:https:)?\/\/g\.alicdn\.com[^']+)'/g)];
    const preloadTags = scripts.reduce((acc, cur) => {
      if (cur && cur[1]) {
        acc += `<link rel="preload" href="${cur[1]}" as="script" crossorigin="anonymous" />`;
      }
      return acc;
    }, '');

    const group = [];
    if (preloadTags) {
      const headEndPosition = beforeView.indexOf('</head>');
      // 兜兜兜..
      if (headEndPosition > -1) {
        group.push(...[beforeView.slice(0, headEndPosition), preloadTags, beforeView.slice(headEndPosition)]);
      }
    } else {
      group.push(beforeView);
    }

    return group.join('');
  }

  async function cacheBasicHtml () {
    try {
      const htmlText = getFirstScreenBaseHtml();
      if (!htmlText) {
        addLog('cachefail', '1')
        return;
      }
      // 二次访问会自更新，er节点更新会清缓存，能有多长缓存多长
      const cacheTime = 60 * 60 * 24 * 30;
      const e = await setJsonCache(
        ctx.fcpBaseCacheKey,
        {
          htmlText,
        },
        cacheTime
      );
      addLog({
        cacheVersion: getVersionFromHtml(htmlText),
        errorMessage: e.errorMessage
      })
    } catch (err) {
      addLog('errorMessage', err.message)
    }
  }
}

// 读取htmlBase
export async function getHtmlBase (ctx): Promise<string> {
  const addLog = ctx.log.collect('loadHtmlBase');
  let hasReturn = false;
  let htmlBaseString = '';
  if (ctx.debugFcpNoBase) {
    return htmlBaseString;
  }
  const hasRemote = !!ctx.renderBaseHtmlUrl;
  const tasks = [loadLocal()];
  // 没配置远程地址，就不要加入任务了，不然race先返回本地也没用到
  if (hasRemote) {
    tasks.push(loadRemote())
  }
  try {
    htmlBaseString = await raceTasks(
      tasks,
      ctx.isDevMode ? 2000 : 100
    )
    hasReturn = true
    const v = getVersionFromHtml(htmlBaseString);
    if (!v) {
      htmlBaseString = ''
      addLog({
        errorMessage: 'missversion'
      })
    }
    return htmlBaseString;
  } catch (err) {
    addLog({
      errorMessage: err.message
    })
    return '';
  }


  // 本地有数据就释放，无数据就一直Hold
  function loadLocal () {
    return new Promise(function (res) {
      const releasePerf = ctx.perf.stage('loadLocalBase');
      getJsonCache(ctx.fcpBaseCacheKey).then(result => {
        const e = (result.data || {}).htmlText;
        addLog('local', function () {
          return {
            url: ctx.fcpBaseCacheKey,
            length: e && e.length,
          };
        })
        if (e) {
          releasePerf();
          if (!hasReturn) {
            addLog({
              source: 'local'
            })
          }
          res(e);
        } else if (!hasRemote) {
          // 避免傻等超时
          res('')
        }
      });
    })
  }

  async function loadRemote () {
    const releasePerf = ctx.perf.stage('loadRemoteBase');
    const e = await fetch(ctx.renderBaseHtmlUrl, { cdnProxy: true })
      .then(res => res.text())
      .catch(() => '');
    releasePerf();
    addLog('remote', function () {
      return {
        url: ctx.renderBaseHtmlUrl,
        length: e && e.length
      };
    })
    if (!hasReturn) {
      addLog({
        source: 'remote'
      })
    }
    return e;
  }
}
