import { getVersionFromHtml, setJsonCache, getJsonCache, raceTasks, parseSsrHtml, parseCsrHtml } from '../../tools';

// 缓存htmlBase
export async function checkHtmlBase(ctx, htmlString, isCsr = false) {
  const addLog = ctx.log.collect('renderBase');
  try {
    const htmlText = getFirstScreenBaseHtml();
    if (!htmlText) {
      addLog('updateFail', 'nohtml');
      return;
    }
    // 二次访问会自更新，er节点更新会清缓存，能有多长缓存多长
    const cacheTime = 60 * 60 * 24 * 30;
    const e = await setJsonCache(
      ctx.fcpBaseCacheKey,
      {
        htmlText,
      },
      cacheTime
    );
    addLog('updateResult', {
      cacheVersion: getVersionFromHtml(htmlText),
      errorMessage: e.errorMessage,
    });
  } catch (err) {
    addLog('updateFail', err.message);
  }

  // 获取一屏拼接所需的基础html，添加对css和js的preload
  function getFirstScreenBaseHtml() {
    const parsed = isCsr ? parseCsrHtml(htmlString) : parseSsrHtml(htmlString);
    if (!parsed) {
      return '';
    }
    const { beforeView, beforeEnd } = parsed;
    if (!beforeView || !beforeEnd) {
      return '';
    }
    // 基于浏览器标签对后续的js做预加载
    const scripts = [...beforeEnd.matchAll(/<script.+?src='((?:https:)?\/\/g\.alicdn\.com[^']+)'/g)];
    const preloadTags = scripts.reduce((acc, cur) => {
      if (cur && cur[1]) {
        acc += `<link rel="preload" href="${cur[1]}" as="script" crossorigin="anonymous" />`;
      }
      return acc;
    }, '');

    const group = [];
    if (preloadTags) {
      const headEndPosition = beforeView.indexOf('</head>');
      // 兜兜兜..
      if (headEndPosition > -1) {
        group.push(...[beforeView.slice(0, headEndPosition), preloadTags, beforeView.slice(headEndPosition)]);
      }
    } else {
      group.push(beforeView);
    }

    return group.join('');
  }
}

// 读取htmlBase
export async function getHtmlBase(ctx): Promise<string> {
  const addLog = ctx.log.collect('renderBase');
  let hasReturn = false;
  let htmlBaseString = '';
  if (ctx.debugFcpNoBase) {
    return htmlBaseString;
  }
  const hasRemote = !!ctx.renderBaseHtmlUrl;
  const tasks = [loadLocal()];
  // 没配置远程地址，就不要加入任务了，不然race先返回本地也没用到
  if (hasRemote) {
    tasks.push(loadRemote());
  }
  try {
    htmlBaseString = await raceTasks(tasks, ctx.isDevMode ? 2000 : 50);
    hasReturn = true;
    const v = getVersionFromHtml(htmlBaseString);
    if (!v) {
      htmlBaseString = '';
      addLog('loadFail', {
        errorMessage: 'missversion',
      });
    }
    return htmlBaseString;
  } catch (err) {
    addLog('loadFail', {
      errorMessage: err.message,
    });
    return '';
  }

  // 本地有数据就释放，无数据就一直Hold
  function loadLocal() {
    return new Promise(function (res) {
      const releasePerf = ctx.perf.stage('loadLocalBase');
      getJsonCache(ctx.fcpBaseCacheKey).then(result => {
        const e = (result.data || {}).htmlText;
        addLog('loadLocal', function () {
          return {
            url: ctx.fcpBaseCacheKey,
            length: e && e.length,
          };
        });
        if (e) {
          releasePerf();
          if (!hasReturn) {
            addLog('dataSource', 'local');
          }
          res(e);
        } else if (!hasRemote) {
          // 避免傻等超时
          res('');
        }
      });
    });
  }

  async function loadRemote() {
    const releasePerf = ctx.perf.stage('loadRemoteBase');
    return new Promise(function (res) {
      fetch(ctx.renderBaseHtmlUrl, { cdnProxy: true })
        .then(res => res.text())
        .catch(() => '')
        .then(e => {
          releasePerf();
          addLog('loadRemote', function () {
            return {
              url: ctx.renderBaseHtmlUrl,
              length: e && e.length,
            };
          });
          if (!hasReturn) {
            addLog('dataSource', 'remote');
          }
          if (e) {
            res(e);
          }
        });
    });
  }
}
