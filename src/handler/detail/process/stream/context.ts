import { streamBasicUrl, BUILD_VERSION, streamOssHost } from '../../const';
import { initPerf, initLog, monkyCipher, parseCacheKey } from '../../tools';
import { addUrlQuery } from '../../../../utils';

export default function makeContext({ startTime, pageConfig, requestContext }) {
  const {
    isPre,
    isDebug,
    isPreload,
    getRequestFaasUrl,
    requestUrlInstance,
    csrPageUrlString,
    csrHtmlUrlString,
    requestHeaders,
    requestCookie,
    recordError,
    projectInfo,
  } = requestContext;
  const query = {
    ...requestUrlInstance.query,
  };

  const isDevMode = (!!isPre && !!query.bundleUrl) || (isDebug && !!query._er_dev);

  // 获取本次请求的缓存key
  const fileCachePath = matchFileCacheKey({ requestContext, pageConfig, isDevMode });
  // 不存在则返回false回退降级
  if (!fileCachePath) {
    return false;
  }

  const log = initLog(isDebug);
  log.add('buildVersion', BUILD_VERSION);
  const perf = initPerf(startTime);
  const pathSuffix = isPre ? '_pre' : '';
  const renderContext = {
    env: requestContext.env,
    location: requestContext.location,
    immersiveState: requestContext.immersiveState,
    pathname: requestUrlInstance.pathname,
    groupname: projectInfo.groupname,
    servicename: projectInfo.servicename,
    query,
    requestCookie,
    // 页面自定义配置
    pageConfig,
    // er远程公共开关
    renderBaseHtmlUrl: `${streamBasicUrl}${pathSuffix}/${pageConfig.remoteKey || parseCacheKey(projectInfo.groupname, projectInfo.servicename)}.html`,
    // fcp渲染所需数据，远程地址
    fcpDateRemoteUrl: getFcpDataUrl(),
    // fcp渲染所需数据，本地缓存key
    fcpDataLocalUrl: `${requestUrlInstance.pathname}${pathSuffix}/${fileCachePath}.json`,
    // fcp构造基础
    fcpBaseCacheKey: `${requestUrlInstance.pathname}${pathSuffix}/base`,
    // fsp静态化的ssr缓存地址
    fspHtmlRemoteUrl: getFspHtmlUrl(),
    // 请求ssr faas服务的请求头
    requestFaasHeadersInstance: requestHeaders,
    // 根据入参生成faas请求url
    getRequestFaasUrl,
    // 请求csr的url
    requestCsrUrlString: csrHtmlUrlString,
    isPre,
    // 本地edith调试
    isDevMode,
    isPreload,
    // 飞猪端内页面上游预加载html，https://aliyuque.antfin.com/fliggy_user/unicorn/xrirfxvntx32veyb#tshSS
    urcPreloadType: query._fli_from_update ? 'true' : query._fli_preload_from || '',
    urcPreloadPage: query._fl_auto_preload_spm,
    // 前置页面预热cdn资源，不是真正渲染ssr
    isPrepare: !!query._fli_prepare,
    perf,
    log,
    timeLog(key) {
      perf.time(key);
      log.time(key);
    },
    slsLogData: {},
    // 获取降级csr的链接
    getFallBackUrl,
    // 上报异常
    recordError,
    // --- 多端多机型测试标记 ---
    ...getDebugContext(),
  };

  const logPageConfig = () => {
    // token太大了，不看了
    const { templateToken, ...others } = pageConfig;
    return {
      ...others,
      templateTokenLength: (templateToken || []).length,
    };
  };
  log.add('context', () => {
    let headersData = {};
    for (let key of requestHeaders.keys()) {
      headersData[key] = requestHeaders.get(key);
    }
    return {
      ...renderContext,
      pageConfig: logPageConfig(),
      requestFaasHeadersInstance: headersData,
      csrPageUrlString,
    };
  });

  log.add('pageConfig', () => {
    return logPageConfig();
  });

  // 测试上下文异常
  if (isDebug && query._debug_ctx_fail) {
    return false;
  }

  return renderContext;

  function getFallBackUrl(exParams) {
    return addUrlQuery(csrPageUrlString, exParams);
  }

  function getDebugContext() {
    if (!isDebug) {
      return {
        isDebug,
      };
    }

    const debugCheck = {
      ...query,
      ...requestCookie,
    };
    return {
      // 禁止兜底和重定向，展示错误信息
      isDebug,
      // 测试如果ssr服务异常
      debugSsrFail: debugCheck._debug_ssr_fail,
      // 测试如果html不合格
      debugHtmlValid: debugCheck._debug_html_valid,
      // 测试只看fcp内容
      debugFcpOnlyView: debugCheck._debug_fcp_view,
      // 测试fcpbase，强制更新
      debugUpdateBase: !!debugCheck._debug_fcp_updatebase,
      // 测试fcpbase，空值
      debugFcpNoBase: debugCheck._debug_fcp_nobase,
      // 测试如果fcp无数据
      debugFcpNoData: debugCheck._debug_fcp_nodata,
      // 基于从url上获取额外上下文参数模拟特定环境
      debugFcpMoreData: debugCheck._debug_fcp_moredata,
      // 测试fcp中调整样式加载顺序
      debugFcpStyle: debugCheck._debug_fcp_style,
      // 测试如果fcp远程数据异常
      debugFcpRemote: debugCheck._debug_fcp_remote,
      // 测试如果fcp本地数据异常
      debugFcpLocal: debugCheck._debug_fcp_local,
      // 测试预发环境脚本
      debugFcpEndScript: debugCheck._debug_fcp_end_script,
      // 测试打印原始html
      debugFspSsrHtml: debugCheck._debug_fsp_ssrhtml,
      // 测试启用fsp缓存
      debugFspCache: debugCheck._debug_fsp_cache,
      // 测试用fsp缓存渲染，绕过校验
      debugFspCacheRender: debugCheck._debug_fsp_cache_render,
      // 测试远程拉fsp缓存失败
      debugFspCacheFail: debugCheck._debug_fsp_fail,
      // 测试fsp缓存超时阈值
      debugFspCacheTime: debugCheck._debug_fsp_time,
      // 测试fsp缓存返回即结束（纯看fspcachehtml），无fcp
      debugFspCacheHtml: debugCheck._debug_fsp_html,
      // 测试fsp缓存，直接使用，不等实时ssr结果
      debugFspCacheEnd: debugCheck._debug_fsp_end,
      // 测试fsp缓存结尾的操作，禁止删除css等
      debugFspCacheEndStyle: debugCheck._debug_fsp_end_style,
      // 强制开启ssg，当然得有fspcache命中配合
      debugSSG: debugCheck._debug_ssg,
      // 测试fsp缓存，串行二段ssr渲染
      debugSsrWait: debugCheck._debug_ssr_wait,
      // 测试阻塞多段ssr其中一个
      debugSsrMulti: debugCheck._debug_ssr_multi,
      // 测试直接渲染csr
      debugCsr: debugCheck._debug_csr,
      // 测试拉取csr的html失败了
      debugCsrFail: debugCheck._debug_csr_fail,
      // 测试csr的html不合格
      debugCsrHtml: debugCheck._debug_csr_html,
      // 测试套壳场景自定义流程
      debugProxyDepend: debugCheck._debug_proxy_depend,
      // 测试三方依赖的原始数据
      debugProxyDependData: debugCheck._debug_proxy_depend_data,
      // 测试淘宝独有的埋点脚本
      debugTaobaoPerf: debugCheck._debug_taobao_perf,
    };
  }

  function getFcpDataUrl() {
    if (pageConfig.fcpCacheKey) {
      return `${streamOssHost}/${pageConfig.fcpCacheKey}${pathSuffix}/${fileCachePath}.json`;
    }
    if (pageConfig.remoteCacheUrl) {
      return `${pageConfig.remoteCacheUrl}${pathSuffix}/${fileCachePath}.json`;
    }
    return '';
  }

  function getFspHtmlUrl() {
    if (pageConfig.fspCacheKey) {
      return `${streamOssHost}/${pageConfig.fspCacheKey}${pathSuffix}/${fileCachePath}.html`;
    }
    if (pageConfig.remoteCacheHtmlUrl) {
      return `${pageConfig.remoteCacheHtmlUrl}${pathSuffix}/${fileCachePath}.html`;
    }
    return '';
  }
}

// 根据页面当前配置的缓存维度，获取最终文件路径
/**
 * 规则：
 * cacheKey = ["id", "itemId"]
 * filePath = ['cityCode', 'appName']
 * fileName = ['userId']
 * singleApp = ['taobao']
 * 结果：cityCode/taobao/ccc_abc
 *
 * case1
 * 杭州用户ccc在淘宝访问：xxx?id=abc
 * 匹配：31010/taobao/ccc_abc
 *
 * case2
 * 未知地区用户bb在支付宝访问：xxx?itemId=eee
 * 匹配：defaultApp/bb_eee
 *
 */
function matchFileCacheKey({ requestContext, pageConfig, isDevMode }) {
  // 获取本次请求的key
  const queryMatchValue = getQueryMatch(pageConfig.cacheKey, requestContext.requestUrlInstance.query, isDevMode, pageConfig.cacheKeyDefault);
  if (!queryMatchValue) {
    return false;
  }

  const { filePath = [], fileName = [], singleApp = [] } = pageConfig.cacheRule || {};
  const pathList = [];
  const nameList = [];

  filePath.forEach(ruleKey => {
    const value = getRuleValue(ruleKey);
    if (value) {
      pathList.push(value);
    }
  });

  fileName.forEach(ruleKey => {
    const value = getRuleValue(ruleKey);
    if (value) {
      nameList.push(value);
    }
  });
  nameList.push(queryMatchValue);
  pathList.push(nameList.join('_'));

  // aaa
  // aaa/bb
  const finalPath = pathList.join('/');
  if (pageConfig.useMonky > 0) {
    // aaa/bb --> cdefg
    return monkyCipher(filePath, pageConfig.useMonky);
  }
  return finalPath;

  function getRuleValue(ruleKey) {
    const { env, location, requestCookie } = requestContext;
    switch (ruleKey) {
      case 'cityCode':
        return location.requestCityCode;
      case 'userId':
        return env.userId;
      case 'abCookie':
        if (!pageConfig.abCookieKey) {
          return pageConfig.abDefaultValue;
        }
        return requestCookie[pageConfig.abCookieKey] || pageConfig.abDefaultValue;
      case 'appName':
        if (!singleApp || !singleApp.length) {
          return 'defaultApp';
        }

        return checkEnvMatch(env, singleApp) || 'defaultApp';
    }
  }

  function checkEnvMatch(env, singleApp) {
    const SINGLE_APP_NAME = {
      TAOBAO: 'taobao',
      // 淘宝轻应用
      TAOBAO_LITE: 'taobao_lite',
      TRIP: 'trip',
      ALIPAY: 'alipay',
      WECHAT: 'wechat',
      XHS: 'xhs',
    };
    const whiteMap = singleApp.reduce((memo, key) => {
      memo[key] = 1;
      return memo;
    }, {});
    const orderList = [
      // 轻应用也在淘宝里，要优先判断
      [env.isTaobaoLiteApp, SINGLE_APP_NAME.TAOBAO_LITE],
      [env.isTaobao, SINGLE_APP_NAME.TAOBAO],
      [env.isTrip, SINGLE_APP_NAME.TRIP],
      [env.isAlipay, SINGLE_APP_NAME.ALIPAY],
      [env.isWeChatMiniProgramH5 || env.isWeixin, SINGLE_APP_NAME.WECHAT],
      [env.isXhsMiniAppH5, SINGLE_APP_NAME.XHS],
    ];
    for (let i = 0, len = orderList.length; i < len; i++) {
      const [match, key] = orderList[i];
      // 是设置的白名单，同时也命中了环境
      if (match && whiteMap[key]) {
        return key;
      }
    }
    return '';
  }
}

// 流式渲染，从query中匹配唯一的数据缓存Key
function getQueryMatch(cacheKey, query, isDevMode, defaultVal) {
  if (cacheKey === false) {
    return 'defaultKey';
  }
  const keys = cacheKey || [];
  for (let i = 0, len = keys.length; i < len; i++) {
    const data = query[keys[i]];
    if (data) {
      return data;
    }
  }

  return isDevMode ? 'devkey' : defaultVal || null;
}
