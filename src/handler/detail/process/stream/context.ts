import { streamRemoteConfigPath, streamBasicUrl } from '../../const';
import { initPerf, initLog, monkyCipher } from '../../tools';
import { addUrlQuery } from '../../../../utils';

export default function makeContext({ isPre, startTime, isDebug, pageConfig, requestContext }) {
  const { getRequestFaasUrl, requestUrlInstance, csrPageUrlString, csrHtmlUrlString, requestHeaders, requestCookie, recordError, projectInfo } =
    requestContext;
  const query = {
    ...requestUrlInstance.query,
  };

  const isDevMode = (!!isPre && !!query.bundleUrl) || (isDebug && !!query._er_dev);

  // 获取本次请求的key
  const cacheKeyValue = getDataCacheKey(pageConfig.cacheKey);
  if (!cacheKeyValue) {
    return false;
  }
  const log = initLog(isDebug);
  const perf = initPerf(startTime);

  const getConfigUrl = key => `${streamRemoteConfigPath}${key}${isPre ? '_pre' : ''}.json`;
  const renderContext = {
    env: requestContext.env,
    immersiveState: requestContext.immersiveState,
    pathname: requestUrlInstance.pathname,
    groupname: projectInfo.groupname,
    servicename: projectInfo.servicename,
    query,
    requestCookie,
    // 页面自定义配置
    pageConfig,
    // er远程开关
    remotePublicConfigUrl: getConfigUrl('erstream'),
    renderBaseHtmlUrl: pageConfig.remoteBasicHtmlUrl || (pageConfig.remoteKey ? `${streamBasicUrl}${isPre ? '_pre' : ''}/${pageConfig.remoteKey}.html` : ''),
    // fcp渲染所需数据，远程地址
    fcpDateRemoteUrl: pageConfig.remoteCacheUrl ? `${pageConfig.remoteCacheUrl}${isPre ? '_pre' : ''}/${cacheKeyValue}.json` : '',
    // fcp渲染所需数据，本地缓存key
    fcpDataCacheKey: `${requestUrlInstance.pathname}?key=${cacheKeyValue}${isPre ? '&pre=1' : ''}`,
    // fcp构造基础
    fcpBaseCacheKey: `${requestUrlInstance.pathname}/base${isPre ? '?pre=1' : ''}`,
    // fsp静态化的ssr结果
    fspHtmlRemoteUrl: getFspCacheUrl(),
    // 请求ssr faas服务的请求头
    requestFaasHeadersInstance: requestHeaders,
    // 根据入参生成faas请求url
    getRequestFaasUrl,
    // 请求csr的url
    requestCsrUrlString: csrHtmlUrlString,
    isPre,
    // 本地edith调试
    isDevMode,
    // 飞猪端内页面上游预加载html，https://aliyuque.antfin.com/fliggy_user/unicorn/xrirfxvntx32veyb#tshSS
    urcPreloadType: query._fli_from_update ? 'true' : query._fli_preload_from || '',
    urcPreloadPage: query._fl_auto_preload_spm,
    // 前置页面预热cdn资源，不是真正渲染ssr
    isPrepare: !!query._fli_prepare,
    perf,
    log,
    slsLogData: {},
    // 获取降级csr的链接
    getFallBackUrl,
    // 上报异常
    recordError,
    // --- 多端多机型测试标记 ---
    ...getDebugContext(),
  };

  log.add('context', () => {
    let headersData = {};
    for (let key of requestHeaders.keys()) {
      headersData[key] = requestHeaders.get(key);
    }
    // token太大了，不看了
    const { templateToken, ...others } = pageConfig;
    return {
      ...renderContext,
      pageConfig: others,
      requestFaasHeadersInstance: headersData,
      csrPageUrlString,
    };
  });

  // 测试上下文异常
  if (isDebug && query._debug_ctx_fail) {
    return false;
  }

  return renderContext;

  function getFallBackUrl(exParams) {
    return addUrlQuery(csrPageUrlString, exParams);
  }

  // 流式渲染，获取本次请求的数据缓存Key
  function getDataCacheKey(cacheKey) {
    if (cacheKey === false) {
      return 'noNeedCacheKey';
    }
    const keys = cacheKey || [];
    for (let i = 0, len = keys.length; i < len; i++) {
      const data = query[keys[i]];
      if (data) {
        return data;
      }
    }

    return isDevMode ? 'devkey' : null;
  }

  function getFspCacheUrl() {
    const { userId } = requestContext.env;
    const { remoteCacheHtmlUrl, useLogin, useMonky } = pageConfig;
    if (!remoteCacheHtmlUrl) {
      return '';
    }

    const basePath = `${remoteCacheHtmlUrl}${isPre ? '_pre' : ''}/`;
    const paths = [];
    if (useLogin) {
      paths.push(userId);
    }
    paths.push(cacheKeyValue);

    let filePath = paths.join('/');
    if (useMonky > 0) {
      filePath = monkyCipher(filePath, useMonky);
    }

    return `${basePath}${filePath}.html`;
  }

  function getDebugContext() {
    if (!isDebug) {
      return {
        isDebug,
      };
    }

    const debugCheck = {
      ...query,
      ...requestCookie,
    };
    return {
      // 禁止兜底和重定向，展示错误信息
      isDebug,
      // 测试如果ssr服务异常
      debugSsrFail: debugCheck._debug_ssr_fail,
      // 测试如果html不合格
      debugHtmlValid: debugCheck._debug_html_valid,
      // 测试只看fcp内容
      debugFcpOnlyView: debugCheck._debug_fcp_view,
      // 测试fcpbase，强制更新
      debugUpdateBase: !!debugCheck._debug_fcp_updatebase,
      // 测试fcpbase，空值
      debugFcpNoBase: debugCheck._debug_fcp_nobase,
      // 测试如果fcp无数据
      debugFcpNoData: debugCheck._debug_fcp_nodata,
      // 基于从url上获取额外上下文参数模拟特定环境
      debugFcpMoreData: debugCheck._debug_fcp_moredata,
      // 测试fcp中调整样式加载顺序
      debugFcpStyle: debugCheck._debug_fcp_style,
      // 测试如果fcp远程数据异常
      debugFcpRemote: debugCheck._debug_fcp_remote,
      // 测试如果fcp本地数据异常
      debugFcpLocal: debugCheck._debug_fcp_local,
      // 测试预发环境脚本
      debugFcpEndScript: debugCheck._debug_fcp_end_script,
      // 测试打印原始html
      debugFspSsrHtml: debugCheck._debug_fsp_ssrhtml,
      // 测试启用fsp缓存
      debugFspCache: debugCheck._debug_fsp_cache,
      // 测试用fsp缓存渲染，绕过校验
      debugFspCacheRender: debugCheck._debug_fsp_cache_render,
      // 测试远程拉fsp缓存失败
      debugFspCacheFail: debugCheck._debug_fsp_fail,
      // 测试fsp缓存超时阈值
      debugFspCacheTime: debugCheck._debug_fsp_time,
      // 测试fsp缓存返回即结束（纯看fspcachehtml），无fcp
      debugFspCacheHtml: debugCheck._debug_fsp_html,
      // 测试fsp缓存，直接使用，不等实时ssr结果
      debugFspCacheEnd: debugCheck._debug_fsp_end,
      // 强制开启二段ssr
      debugSceondSsr: debugCheck._debug_ssr2,
      // 测试fsp缓存，串行二段ssr渲染
      debugSecondSsrWait: debugCheck._debug_ssr2_wait,
      // 测试如果ssr第2段异常
      debugSsrFail2: debugCheck._debug_ssr2_fail,
      // 测试直接渲染csr
      debugCsr: debugCheck._debug_csr,
      // 测试拉取csr的html失败了
      debugCsrFail: debugCheck._debug_csr_fail,
      // 测试csr的html不合格
      debugCsrHtml: debugCheck._debug_csr_html,
      // 测试套壳场景自定义流程
      debugProxyDepend: debugCheck._debug_proxy_depend,
      // 测试三方依赖的原始数据
      debugProxyDependData: debugCheck._debug_proxy_depend_data
    };
  }
}
