import { streamKvConfigAll, streamRenderConfig, defaultStreamConfig, projectWhiteMap, streamConfigUrl } from '../../const';
import { getKvCacheJson, parseConfigKey, formatPath, loadRemoteJson } from '../../tools';

/**
 * 远程通用配置和页面自己的配置合并，全局控制的优先级高于页面自身
 * remote
 *  rule1: {
 *     [pageA]: configA
 *  }
 *
 * [pageA]:{
 *  rule1: configB
 *  rule2: configC
 * }
 *
 * ==>
 * ctx.pageConfig = {
 *   rule1: configA
 *   rule2: configC
 * }
 */
export async function mergeRemoteConfig(renderContext) {
  // 全局配置，兼容提前请求
  const task = renderContext.__preloadAll || getAllPageConfig(renderContext.isPre);
  const result = await task;
  const publickCfg = result || {};
  if (renderContext.__preloadAll) {
    publickCfg._source = 'prepare';
  }
  mergetConfig(renderContext.pathname, renderContext.pageConfig, publickCfg);
  return publickCfg;
}

function getAllPageConfig(isPre) {
  return loadConfig(isPre, streamKvConfigAll);
}

function mergetConfig(pagePathName, singlePageConfig, allPageConfig) {
  const kvName = parseConfigKey(pagePathName);
  Object.keys(allPageConfig).forEach(ruleName => {
    const rulePageConfig = allPageConfig[ruleName];
    const pageRule = rulePageConfig[pagePathName] || rulePageConfig[kvName];
    if (pageRule) {
      singlePageConfig[ruleName] = pageRule;
    }
  });
}

/**
 * 加载单页面维度的配置项
 * @param requestContext
 * @returns
 */
export async function loadPageConfig(requestContext) {
  const { isPre, isDebug, requestUrlInstance } = requestContext;
  let pageConfig = streamRenderConfig[requestUrlInstance.pathname];

  // 无内置配置则尝试取url上配置名
  // 即使有内置配置，是dev模式时也强制取url上配置名
  let kvName = requestUrlInstance.query._use_vstream || '';

  // 有内置配置场景，在debug模式下仅支持url设值，避免永远走不到内置配置的debug场景
  if (pageConfig) {
    if (!isDebug || !kvName) {
      return pageConfig;
    }
  }

  // 无内置配置场景，优先读url配置，其次解析kv
  if (!kvName) {
    kvName = parseConfigKey(requestUrlInstance.pathname);
  }

  if (kvName) {
    requestContext.__preloadAll = getAllPageConfig(isPre);
    pageConfig = await loadConfig(isPre || !!requestUrlInstance.query._debug_pre_config, kvName);
  }

  // 如果没有加载到页面独立配置，按白名单走最小配置，与全局配置合并走兜底逻辑
  if (!pageConfig) {
    const { groupname } = formatPath(requestUrlInstance.pathname);
    if (projectWhiteMap[groupname] || kvName == 'default') {
      pageConfig = defaultStreamConfig;
    }
  }

  return pageConfig;
}

/**
 * 预发ER无法加载KV，走实时的json配置
 * 线上以KV优先
 */
async function loadConfig(isPre, configKey) {
  let response: any = {
    data: null,
  };
  if (isPre) {
    response = await loadRemoteJson(`${streamConfigUrl}/erpre/${configKey}.json`, 2000, false);
  } else {
    response = await getKvCacheJson(configKey, 100);
  }

  if (response.data) {
    response.data.__key = configKey;
    response.data.__cost = response.cost;
  }
  return response.data;
}
