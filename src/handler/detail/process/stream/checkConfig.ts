import { streamKvConfigAll, streamRenderConfig } from '../../const';
import { getKvCacheJson, parseConfigKey } from '../../tools';

/**
 * 远程通用配置和页面自己的配置合并，全局控制的优先级高于页面自身
 * remote
 *  rule1: {
 *     [pageA]: configA
 *  }
 *
 * [pageA]:{
 *  rule1: configB
 *  rule2: configC
 * }
 *
 * ==>
 * ctx.pageConfig = {
 *   rule1: configA
 *   rule2: configC
 * }
 */
export async function mergeRemoteConfig(ctx) {
  // 全局配置
  const result = await getAllPageConfig(ctx.isPre, 'merge');
  const publickCfg = result.data || {};
  if (result.cost) {
    publickCfg._cost = result.cost;
  }
  mergetConfig(ctx.pathname, ctx.pageConfig, publickCfg);
  return publickCfg;
}

let lastConfigLoad = null;
export async function getAllPageConfig(isPre, type) {
  if (!lastConfigLoad) {
    lastConfigLoad = load();
  }
  const e = await lastConfigLoad;
  return e;
  async function load() {
    // 全局配置
    const result: any = await getKvCacheJson(isPre ? `${streamKvConfigAll}_pre` : streamKvConfigAll, isPre ? 500 : 100);
    if (result.data) {
      result.data._source = type;
    }
    return result;
  }
}

function mergetConfig(pagePathName, singlePageConfig, allPageConfig) {
  Object.keys(allPageConfig).forEach(ruleName => {
    const rulePageConfig = allPageConfig[ruleName];
    const pageRule = rulePageConfig[pagePathName];
    if (pageRule) {
      singlePageConfig[ruleName] = pageRule;
    }
  });
}

/**
 * 加载单页面维度的配置项
 * @param ctx
 * @returns
 */
export async function loadPageConfig(ctx) {
  const { isPre, isDebug, requestUrlInstance } = ctx;
  let pageConfig = streamRenderConfig[requestUrlInstance.pathname];

  // 无内置配置则尝试取url上配置名
  // 即使有内置配置，是dev模式时也强制取url上配置名
  let kvName = requestUrlInstance.query._use_vstream || '';

  // 有内置配置场景，在debug模式下仅支持url设值，避免永远走不到内置配置的debug场景
  if (pageConfig) {
    if (!isDebug || !kvName) {
      return pageConfig;
    }
  }

  // 无内置配置场景，优先读url配置，其次解析kv
  if (!kvName) {
    kvName = parseConfigKey(requestUrlInstance.pathname);
  }

  if (kvName) {
    getAllPageConfig(isPre, 'preload');
    const kvJson = await getKvCacheJson(isPre ? `${kvName}_pre` : kvName, isDebug || isPre ? 1000 : 50);
    pageConfig = kvJson.data;
    if (pageConfig) {
      pageConfig._cost = kvJson.cost;
    }
  }

  return pageConfig;
}
