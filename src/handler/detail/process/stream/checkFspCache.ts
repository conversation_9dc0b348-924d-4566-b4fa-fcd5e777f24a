import {
  raceTasks,
  assert,
  checkOptionMatch,
} from '../../tools';

export async function getFspCache (ctx) {
  const addLog = ctx.log.collect('loadFspCache');
  try {
    assert(!ctx.debugFspCacheFail, {
      detail: 'testFspCacheTimeout'
    })
    // 同时请求本地和远程，谁有数据用谁
    // 当本地没有数据就等远程返回直到超时
    const htmlString = await raceTasks(
      [loadRemote()],
      200
    )
    addLog('length', () => htmlString && htmlString.length)
    return htmlString;
  } catch (err) {
    addLog('errorMessage', err.message)
    return '';
  }

  async function loadRemote () {
    if (!ctx.fspHtmlRemoteUrl) {
      addLog('nourl', 1)
      return null;
    }
    addLog('url', ctx.fspHtmlRemoteUrl)
    const releasePerf = ctx.perf.stage('loadHtmlCache');
    const result = await fetch(ctx.fspHtmlRemoteUrl, {
      cdnProxy: true, // 必须是接入阿里cdn的域名才有加速
    })
      .then(res => res.text())
      .catch(err => {
        addLog('remoteErrorMessage', err.message)
        return '';
      });

    if (!result) {
      return null;
    }
    releasePerf();
    return result;
  }

}


export function checkFspCacheEnable (ctx, { fcpResult }) {
  const addLog = ctx.log.collect('checkFspCache');
  // 飞猪预加载场景禁用fsp缓存
  if (ctx.urcPreloadType) {
    addLog('disable', 'fliggy');
    return false;
  }
  const result = !!ctx.debugFspCache || !!ctx.debugFspCacheRender || checkFspCacheGray()
  addLog('result', result);
  return result;

  function checkFspCacheGray () {
    const fspCacheRule = ctx.pageConfig.fspCacheRule;
    if (!fspCacheRule) {
      addLog('match', 'noconfig');
      return false;
    }
    const matchInfo = checkOptionMatch(fspCacheRule, {
      ...(ctx.query || {}),
      ...(fcpResult?.data?.featrue || {}),
      ...ctx.env,
    });
    addLog('match', matchInfo.message)
    return matchInfo.result;
  }
}

