import { raceTasks, assert, checkOptionMatch } from '../../tools';

export async function getFspCache(ctx) {
  const addLog = ctx.log.collect('renderFspCache');
  try {
    assert(!ctx.debugFspCacheFail, {
      detail: 'testFspCacheTimeout',
    });
    // 同时请求本地和远程，谁有数据用谁
    // 当本地没有数据就等远程返回直到超时
    const htmlString = await raceTasks([loadRemote()], 300);
    if (htmlString) {
      addLog('loadSuccess', htmlString.length);
    }
    return htmlString;
  } catch (err) {
    addLog('loadError', err.message);
    return '';
  }

  async function loadRemote() {
    if (!ctx.fspHtmlRemoteUrl) {
      addLog('loadError', 'noUrl');
      return null;
    }
    addLog('loadUrl', ctx.fspHtmlRemoteUrl);
    const releasePerf = ctx.perf.stage('loadHtmlCache');
    const result = await fetch(ctx.fspHtmlRemoteUrl, {
      cdnProxy: true, // 必须是接入阿里cdn的域名才有加速
    })
      .then(res => res.text())
      .catch(err => {
        addLog('loadRemoteError', err.message);
        return '';
      });

    if (!result) {
      return null;
    }
    releasePerf();
    return result;
  }
}

export function checkFspCacheEnable(ctx, { optionContext }) {
  const addLog = ctx.log.collect('renderFspCache');
  // 飞猪预加载场景禁用fsp缓存
  if (ctx.urcPreloadType) {
    addLog('match', { result: false, message: 'isUrc' });
    return false;
  }
  const result = !!ctx.debugFspCache || !!ctx.debugFspCacheRender || checkFspCacheGray();
  addLog('enable', result);
  return result;

  function checkFspCacheGray() {
    const fspCacheRule = ctx.pageConfig.fspCacheRule;
    if (!fspCacheRule) {
      addLog('match', { result: false, message: 'noconfig' });
      return false;
    }
    const matchInfo = checkOptionMatch(fspCacheRule, optionContext);
    addLog('match', matchInfo);
    return matchInfo.result;
  }
}
