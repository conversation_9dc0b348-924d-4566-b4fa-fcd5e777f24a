export default function (requestContext) {
  const { requestUrlInstance, event } = requestContext;
  const { request } = event;
  const headerCookie = request.headers.get('cookie') || '';
  // 获取userid
  const matchesReg = headerCookie.match(/unb=(\d+);/);
  let headerCookieUserId = '';
  if (matchesReg && matchesReg.length > 1) {
    headerCookieUserId = matchesReg[1];
    if (headerCookieUserId === '2212969160059') {
      // 安全扫描账号直接降级
      return requestContext.fallBack('safe');
    }
  }


  const requestQuery = requestUrlInstance.query;
  // [debug] For Test 302 测试
  if (requestQuery.bypassRedirect) {
    return requestContext.fallBack('test');
  }
  // [debug] For Test 失败测试
  if (requestQuery.bypassFail) {
    throw new Error('bypass fail');
  }
  // [debug] For Test 获取headers测试
  if (requestQuery.bypassGetHeaders) {
    let headersData = {};
    for (let key of request.headers.keys()) {
      headersData[key] = request.headers.get(key);
    }

    return JSON.stringify(headersData);
  }
}
