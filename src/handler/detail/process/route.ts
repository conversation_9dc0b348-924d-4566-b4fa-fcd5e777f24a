
const shopMap = {

}

const categoryMap = {
  50016161 (detail) {
    return {
      pathname: '/app/trip/rx-hotel-detail/pages/detail',
      query: {
        itemid: detail.itemId
      }
    }
  }
}

export const REGION_ID_JAPAN = '900085';
export const REGION_ID_EUROPE = '200007';
const OVERSEAS_RAILWAY_ITEM_MAP = [
  {
    regionId: REGION_ID_JAPAN,
    ftid: 'ftid(031004)',
    itemList: [
      '934131315399', '940534282622', '949356927355', '951009732412', '949364699360',
      '949403419568', '951013116228', '949830838512', '949394311674', '951028548198',
      '951046352919', '951042252548', '951021176710', '951057980669', '949836234310',
      '950470745623', '950486249750', '949868062852', '951011672542', '950488257294',
      '950488797193', '950492461400', '951031376242', '949400115682', '959870545434'
    ]
  },
  {
    regionId: REGION_ID_EUROPE,
    ftid: 'ftid(030504)',
    itemList: ['941163049099', '940232391364']
  }
];

export default function route (requestContext) {
  const { requestUrlInstance, csrHost, isPre } = requestContext;
  const requestQuery = requestUrlInstance.query;
  const categoryId = requestQuery.categoryId || requestQuery.category;
  const shopId = requestQuery.shopId || requestQuery.shop;
  const itemId = requestQuery.id || requestQuery.itemId || requestQuery.itemid;
  const detail = {
    categoryId,
    shopId,
    itemId
  }
  if (categoryMap[categoryId]) {
    return doRedirect(categoryMap[categoryId](detail));
  }

  if (shopMap[shopId]) {
    return doRedirect(shopMap[shopId](detail))
  }

  const overseasRailwayItem = OVERSEAS_RAILWAY_ITEM_MAP.find(item => item.itemList.includes(itemId));
  if(overseasRailwayItem) {
    const { regionId, ftid } = overseasRailwayItem;
    return doRedirect({
      host: csrHost, // 兼容多端环境的csr域名
      pathname: '/app/trip/rx-euro-railway/pages/searchlist',
      query: {
        spm: requestQuery?.spm,
        fpt: ftid,
        itemId,
        region: regionId,
        titleBarHidden: 2,
        disableNav: 'YES',
      }
    })
  }

  function doRedirect (e) {
    const { host, pathname, query = {} } = e;
    if (!pathname) {
      return
    }
    const target = requestUrlInstance.clone();
    if(host) {
      target.host = host;
    }
    // 重定向到的页面
    target.pathname = pathname;
    // 复制参数
    target.query = {
      ...target.query,
      ...query,
      _er_redirect: 'detail'
    }
    return Response.redirect(target.toString());
  }
}

