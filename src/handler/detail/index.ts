import { SlsLog } from '../../utils/rax';
import { CSR_HOST_MAP, PRE_HOST } from './const';
import { dingdingwarning, isVacationStreamPage } from './tools';
import { simpleParseUrl, addUrlQuery } from '../../utils';
import makeRequestContext from './context';
import detailRoute from './process/route';
import startStreamRender from './process/stream';
import checkInterrupt from './process/interrupt';
import handleRaxRequest from '../rax';

export { isVacationStreamPage };

export default async function ({ event, startTime }) {
  let requestContext = null;
  try {
    requestContext = makeRequestContext({ event, startTime });
  } catch (err) {
    return redirectCsr(event, err);
  }

  try {
    // --- 直接降级场景
    const needInterrupt = checkInterrupt(requestContext);
    if (needInterrupt) {
      return await needInterrupt;
    }

    // --- 套壳宝贝路由
    const redirectPage = detailRoute(requestContext);
    if (redirectPage) {
      return redirectPage;
    }

    // 海外流量直降csr更快比faas回源国内快
    if (requestContext.location.oversea && requestContext.env.isTaobao) {
      return requestContext.fallBack('oversea');
    }

    // --- 商详流式渲染
    const result = await startStreamRender(requestContext, startTime, fallBackCommonSSR);
    if (result !== false) {
      return result;
    }
  } catch (e) {
    requestContext.recordError(e, 'entry');
    return requestContext.fallBack('eerr');
  }

  return requestContext.fallBack('invalid');

  function fallBackCommonSSR() {
    return handleRaxRequest(event);
  }
}

// 只用于兜底302，主动降级请走requestContext.fallBack
function redirectCsr(event, err) {
  const { request } = event;
  if (err) {
    const e = simpleParseUrl(request.url);
    SlsLog.sendLog({
      event,
      pathname: e.pathname,
      logConfig: {
        logName: 'er_stream_log',
        logType: 'error',
        logData: {
          slsNew: true,
          reqUrl: request.url,
          errorMsg: `unexpectError:${err.message}:${err.stack}`,
        },
      },
    });
    dingdingwarning(event, err);
  }

  if (PRE_HOST.some(host => request.url.indexOf(host) > -1)) {
    return `${err ? `${err.message}:${err.stack}` : ''}`;
  }

  const hosts = Object.keys(CSR_HOST_MAP);
  for (let i = 0, len = hosts.length; i < len; i++) {
    const h = hosts[i];
    if (request.url.indexOf(h) > 0) {
      const fallbackUrl = addUrlQuery(request.url.replace(h, CSR_HOST_MAP[h]), {
        _er_failback: 'unexpect',
      });
      return Response.redirect(fallbackUrl);
    }
  }

  const fallbackUrl = addUrlQuery(
    request.url
      .replace('https://outfliggys.m.taobao.com', 'https://market.m.taobao.com')
      .replace('https://proxy-er.feizhu.com', 'https://proxy.feizhu.com'),
    {
      _er_failback: 'unexpect',
    }
  );
  return Response.redirect(fallbackUrl);
}
