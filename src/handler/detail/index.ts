import { SlsLog } from '../../utils/rax';
import { CSR_HOST_MAP, PRE_HOST } from './const';
import { dingdingwarning } from './tools';
import { simpleParseUrl, addUrlQuery } from '../../utils';
import makeRequestContext from './context';
import detailRoute from './process/route';
import startStreamRender from './process/stream';
import checkInterrupt from './process/interrupt';
import handleRaxRequest from '../rax';

export default async function ({ event, startTime }) {
  let requestContext = null;
  try {
    requestContext = makeRequestContext({ event, startTime });
  } catch (err) {
    return unexpectError(event, err);
  }

  try {
    // --- 直接降级场景
    const needInterrupt = checkInterrupt(requestContext);
    if (needInterrupt) {
      return needInterrupt;
    }

    // --- 套壳宝贝路由
    const redirectPage = detailRoute(requestContext);
    if (redirectPage) {
      return redirectPage;
    }

    // --- 商详流式渲染
    const result = await startStreamRender(requestContext, startTime, fallBackCommonSSR);
    if (result !== false) {
      return result;
    }
  } catch (e) {
    requestContext.recordError(e, 'entry');
    return requestContext.fallBack('eerr');
  }

  return requestContext.fallBack('invalid');

  function fallBackCommonSSR() {
    return handleRaxRequest(event);
  }
}

function unexpectError(event, err) {
  const { request } = event;
  const e = simpleParseUrl(request.url);
  SlsLog.sendLog({
    event,
    pathname: e.pathname,
    logConfig: {
      logName: 'er_stream_log',
      logType: 'error',
      logData: {
        slsNew: true,
        reqUrl: request.url,
        errorMsg: `unexpectError:${err.message}:${err.stack}`,
      },
    },
  });
  dingdingwarning(event, err);

  if (PRE_HOST.some(host => request.url.indexOf(host) > -1)) {
    return err.message;
  }

  const hosts = Object.keys(CSR_HOST_MAP);
  for (let i = 0, len = hosts.length; i < len; i++) {
    const h = hosts[i];
    if (request.url.indexOf(h) > 0) {
      const fallbackUrl = addUrlQuery(request.url.replace(h, CSR_HOST_MAP[h]), {
        _er_fallback: 1,
      });
      return Response.redirect(fallbackUrl);
    }
  }

  const fallbackUrl = addUrlQuery(request.url.replace('https://outfliggys.m.taobao.com', 'https://market.m.taobao.com').replace('https://proxy-er.feizhu.com','https://proxy.feizhu.com'), {
    _er_fallback: 1,
  });
  return Response.redirect(fallbackUrl);
}
