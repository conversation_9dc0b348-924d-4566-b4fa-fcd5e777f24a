import { SlsLog } from '../../utils/rax';
import {
  IMPORTANT_QUERY,
  BUILD_VERSION,
  FAAS_HOST_VACATION_ONLINE,
  CSR_HOST_ONLINE,
  CSR_HOST_PRE,
  CSR_HOST_MAP,
  PRE_HOST,
  FAAS_PRELOAD_QUERY,
  isPreloadFaas,
} from './const';
import { simpleParseUrl, addUrlQuery } from '../../utils';
import { parseRequestEnv, parseImmersiveState } from '../../utils/env-parse';
import { parseCookie, getLocation, formatPath } from './tools';

/**
 * 严禁使用 searchParams，会出现无法decode时的报错（https://aliyuque.antfin.com/trip_plat/pu6xpg/sf9gg5t8ugzggegs）
 *
 */
export default function makeRequestContext({ event, startTime }) {
  const { request } = event;
  /*
  原生URL各种踩坑，反正cdn上的都是标准url，还是自己字符串解析吧
  * 链接解析报错：https://aliyuque.antfin.com/trip_plat/pu6xpg/sf9gg5t8ugzggegs
  * 链接超长报错：https://aliyuque.antfin.com/trip_plat/pu6xpg/tds6kbq3n4i7y22y
  * 链接超长 + 重要参数后置：https://aliyuque.antfin.com/trip_plat/pu6xpg/ycsmlziv45ggwpug
  */
  const parsedUrl = simpleParseUrl(request.url, {
    queryOrder: IMPORTANT_QUERY,
  });

  const isPre = PRE_HOST.includes(parsedUrl.host);

  const requestHeaders = formatHeaders(event, parsedUrl);
  const cookieString = requestHeaders.get('cookie');
  const requestCookie: any = parseCookie(cookieString);
  formatRequestCookie('cookie', requestHeaders, requestCookie, false);
  formatRequestCookie('s-cookie', requestHeaders, null, !!cookieString);
  const isDebug = parsedUrl.query._debug_er || `${requestCookie._er_debug}` == '1';
  const csrHost = CSR_HOST_MAP[parsedUrl.host] || (isPre ? CSR_HOST_PRE : CSR_HOST_ONLINE);
  const csrUrlInfo = getCsrUrl(csrHost, parsedUrl);
  const env: any = parseRequestEnv(requestHeaders);
  const immersiveState = parseImmersiveState({
    query: parsedUrl.query,
    cookie: requestCookie,
    env,
  });
  const projectInfo = formatPath(parsedUrl.pathname);
  const requestContext: any = {
    event,
    isPre,
    isDebug,
    isPreload: isPreloadFaas(parsedUrl.query),
    fallBack,
    redirect,
    env,
    location: getLocation(event, parsedUrl.query, requestCookie),
    immersiveState,
    recordError,
    recordEnd,
    // -- 访问实例
    requestHeaders,
    requestCookie,
    // 多种环境下访问时对应的csr域名
    csrHost,
    // 降级csr时跳转地址
    csrPageUrlString: csrUrlInfo.pageUrl,
    // 加载csr文件的访问地址
    csrHtmlUrlString: csrUrlInfo.htmlUrl,
    // 切流faas请求地址，在默认请求之上替换pathname/域名
    getRequestFaasUrl,
    requestUrlInstance: parsedUrl,
    projectInfo,
  };

  recordPv();

  return requestContext;

  function fallBack(type = 'default', addtionQuery = {}, checkDebug) {
    const next = addUrlQuery(requestContext.csrPageUrlString, {
      ...addtionQuery,
      _er_dv: encodeURIComponent(BUILD_VERSION),
      _er_failback: type,
    });
    if (checkDebug && isDebug) {
      return `willFallBack：${next}`;
    }
    return Response.redirect(next);
  }

  function redirect(e, type = 'default', checkDebug) {
    const { host, pathname, query = {} } = e;
    if (!pathname) {
      return;
    }
    const target = parsedUrl.clone();
    if (host) {
      target.host = host;
    }
    // 重定向到的页面
    target.pathname = pathname;
    // 复制参数
    target.query = {
      ...target.query,
      ...query,
      _er_redirect: type,
    };
    if (checkDebug && isDebug) {
      return `willRedirect：${target.toString()}`;
    }
    return Response.redirect(target.toString());
  }

  function recordError(err, detailMessage) {
    if (isPre) {
      return;
    }
    const errorTip = [BUILD_VERSION];
    if (detailMessage) {
      errorTip.push(detailMessage);
    }
    const errorMessage = typeof err == 'string' ? err : (err && err.message) || JSON.stringify(err);
    errorTip.push(errorMessage);
    if (err && err.stack && typeof err.stack == 'string') {
      const stackMessage = err.stack.split('\n').slice(0, 8).join('\n');
      errorTip.push(stackMessage);
    }
    const logData = {
      slsNew: true,
      reqUrl: parsedUrl.toString(),
      errorMsg: errorTip.join('|'),
      clientType: env.clientType,
    };
    try {
      if (logData.errorMsg.indexOf('header size') > -1) {
        let headerLength = {};
        // @ts-ignore
        for (let key of requestHeaders.keys()) {
          headerLength[key] = requestHeaders.get(key);
        }
        // @ts-ignore
        logData.headerLength = headerLength;
      }
    } catch (err) {
      // @ts-ignore
      logData.headerLength = err.message;
    }
    SlsLog.sendLog({
      event,
      pathname: parsedUrl.pathname,
      logConfig: {
        logName: 'er_stream_log',
        logType: 'error',
        logData,
      },
    });
  }

  function recordPv() {
    // 打入口日志
    SlsLog.sendLog({
      event,
      pathname: parsedUrl.pathname,
      logConfig: {
        logData: {
          type: 'entry',
          timeStamp: {
            client: env.clientType,
            er_entry: true,
            er_start: startTime,
            erRequestUrl: request.url,
          },
        },
      },
    });
  }

  function recordEnd(logData: any = {}) {
    // 打出口日志
    const now = Date.now();
    SlsLog.sendLog({
      event,
      pathname: parsedUrl.pathname,
      logConfig: {
        logData: {
          type: 'leave',
          timeStamp: {
            client: env.clientType,
            er_start: startTime,
            er_end: now,
          },
          er_duration: now - startTime,
          ...logData,
        },
      },
    });
  }

  // 过滤&去重cookie内容
  // https://aliyuque.antfin.com/trip_plat/pu6xpg/iisk1ltmkq6tc7bh?singleDoc# 《【11.27】单用户高频302降级》
  function formatRequestCookie(headKey, requestHeaders, parsedCookieObj?, harder?) {
    try {
      if (!parsedCookieObj) {
        const val = requestHeaders.get(headKey);
        if (!val) {
          return;
        }
        parsedCookieObj = parseCookie(val);
      }
      const blackMap = {
        tk_trace: 1,
        tkmb: 1,
        // 酒店民宿业务用的
        FLIGGY_BNB_HAS_LOOKUP: 1,
        FLIGGY_BNB_DETAIL_RECOMMEND: 1,
        FLIGGY_BNB_LIST_FILTER_PARAM: 1,
      };
      const whiteMap = {
        // 用户信息相关
        unb: 1,
        _fm_wx_union_id_: 1,
        _fm_wx_open_id_: 1,
        munb: 1,
        tfstk: 1, // 安全需要，放开
        cookie17: 1,
        tracknick: 1,
        _tb_token_: 1,
        sgcookie: 1,
      };
      const kvList = Object.keys(parsedCookieObj).reduce((memo, key) => {
        let val = parsedCookieObj[key];
        if (!blackMap[key] && val) {
          // 严格校验时，只允许白名单内或较小的字段
          if (harder && !whiteMap[key] && val.length > 90) {
            return memo;
          }
          memo.push(`${key}=${encodeURIComponent(val)}`);
        }
        return memo;
      }, []);
      if (kvList.length) {
        requestHeaders.set(headKey, kvList.join('; '));
      }
    } catch (err) {
      recordError(err, `「formatcookiefail_${headKey}」`);
    }
  }

  function getRequestFaasUrl(opt) {
    const { pathname, host, query } = opt;
    const faasUrl = parsedUrl.clone();
    // 过滤一些各渠道不需要给服务端的埋点参数，超长导致奇奇怪怪问题
    const blackMap = {
      referer: 1, // 路由层加的，坑
      utparam: 1, // 手淘埋点参数，服务端不需要
      s_share_url: 1, // 联盟端外推广参数
      eurl: 1, // 手淘端信息流广告埋点，咨询过彧卿，点击时/路由层已经消费过了落地页不需要
      minfo: 1, // 手淘埋点参数
      e: 1, // 广告参数
      // 手淘详情的参数
      headInfo: 1, // 啥玩意
      title: 1,
      pic_path: 1,
      search_keyword: 1,
      list_param: 1,
      detailAlgoParam: 1,
    };
    const whiteMap = {
      // 透传通用faas预加载参数
      ...FAAS_PRELOAD_QUERY,
      // urc标识,应用识别预加载
      _fli_preload_from: 1,
      _fli_from_update: 1,
      _fl_auto_preload_spm: 1,
    };
    // 过滤query字段
    // 1. 首字母是 _ 的各端私有变量
    // 2. 空值 或 值长度过长
    // 3. 一些黑名单参数，与服务端请求无关的
    faasUrl.filterQuery((val, key) => {
      return val && (whiteMap[key] || (!blackMap[key] && key[0] !== '_' && val.length < 500));
    });
    faasUrl.setQuery(query);
    // 指向ssr服务域名
    faasUrl.host = host || FAAS_HOST_VACATION_ONLINE;
    // ssr服务分函数调用
    faasUrl.pathname = pathname || faasUrl.pathname;
    // 强制https，否则http的访问会在aserver路由层耗时过久
    faasUrl.protocol = 'https';
    return faasUrl.toString();
  }
}

function getCsrUrl(csrHost, parsedUrl) {
  const csrURL = parsedUrl.clone();
  // 强制https，否则http的访问会在aserver路由层耗时过久
  csrURL.protocol = 'https';
  // 套壳场景降级时也应该去套壳的csr
  csrURL.host = csrHost;
  const blackMap = {
    referer: 1, // 路由层加的，坑
    eurl: 1, // 手淘端信息流广告埋点，咨询过彧卿，点击时/路由层已经消费过了落地页不需要
  };
  csrURL.filterQuery((val, key) => {
    return !blackMap[key];
  });
  // 避免重定向时会触发客户端url统一化
  csrURL.setQuery({
    _fli_online: true,
    _fli_webview: true,
    hybrid: true,
  });
  const htmlURL = csrURL.clone();
  // html的访问写死回market源站域名
  htmlURL.host = CSR_HOST_ONLINE;
  htmlURL.search = '';

  return {
    pageUrl: csrURL.toString(),
    htmlUrl: htmlURL.toString(),
  };
}

// -------------检测降级地址--------------
function formatHeaders(event, parsedUrl) {
  const { request } = event;
  // --- 本次请求格式化
  // 不清楚这个逻辑，copy过来的
  request.headers.delete('host');
  request.headers.delete('x-client-scheme');
  // 增加请求头告之 SSR Faas 服务是从边缘节点来的
  request.headers.set('fli-bk', '1');

  const headEntries = request.headers.entries();
  const newHeaders = new Headers();
  const usedKey = [];
  const origin = [];
  const whiteList = {
    's-cookie': 1,
    cookie: 1,
    'user-agent': 1,
  };
  let requestHeaders = newHeaders;
  try {
    for (const p of headEntries) {
      const [key, val] = p;
      origin.push(p);
      if (whiteList[key] || val.length < 300) {
        newHeaders.append(key, val);
        usedKey.push(key);
      }
    }
    let originUrl = parsedUrl.toString();
    if (originUrl.length > 1000) {
      originUrl = filterUrlBywhiteList(event.request.url, {
        _er_urlcut: 'formatheaders',
      });
    }
    // 安全拦截后重定向需要，商详自己复写一下，避免请求头部爆炸
    try {
      requestHeaders.set('originurl', originUrl);
    } catch (err) {
      // 失败了还有内容检查兜底
    }
  } catch (err) {
    requestHeaders = request.headers;
    requestHeaders.set('er-head-fail', '1');
  }
  return requestHeaders;
}

/**
 * 只保留必要参数
 */
function filterUrlBywhiteList(urlString, params = {}) {
  const whiteMap = IMPORTANT_QUERY.reduce((memo, key) => {
    memo[key] = 1;
    return memo;
  }, {});
  const parsed = simpleParseUrl(urlString, {
    queryOrder: IMPORTANT_QUERY,
  });
  parsed.filterQuery((val, key) => {
    return !!whiteMap[key];
  });
  parsed.setQuery({
    ...(params || {}),
    _er_urlcut: 'dwhite',
  });
  return parsed.toString();
}
