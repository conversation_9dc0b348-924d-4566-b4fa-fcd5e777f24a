export function initLog(isDebug) {
  const logTask = [];
  const collectMap = {};
  const start = Date.now();
  const stageLog = [];
  logTask.push(['stage', stageLog]);
  return {
    time(key) {
      if (!isDebug) {
        return;
      }
      stageLog.push([key, Date.now() - start]);
    },
    add(key, val) {
      if (!isDebug) {
        return;
      }
      let result = null;
      if (typeof val == 'function') {
        const run = () => {
          try {
            return val({});
          } catch (err) {
            return {
              errorMessage: err.message,
            };
          }
        };
        result = run;
      } else if (typeof val !== 'undefined') {
        result = val;
      } else {
        return;
      }
      logTask.push([key, result]);
    },
    collect(key) {
      let temp = collectMap[key];
      if (!temp) {
        collectMap[key] = {};
        temp = collectMap[key];
        if (isDebug) {
          logTask.push([key, temp]);
        }
      }
      return function addLog(name, val) {
        if (!isDebug) {
          return;
        }
        try {
          if (typeof name == 'object') {
            Object.assign(temp, name);
            return;
          }
          if (typeof val == 'function') {
            temp[name] = val();
          } else if (typeof val !== 'undefined') {
            temp[name] = val;
          }
        } catch (err) {
          temp[name] = {
            errorMessage: err.message,
          };
        }
      };
    },
    toJsonString() {
      stageLog.push(['endTime', Date.now() - start])
      const total = logTask.reduce((memo, task) => {
        const [key, value] = task;
        let result = '';
        try {
          if (typeof value == 'function') {
            result = JSON.stringify(value());
          } else {
            result = JSON.stringify(value);
          }
        } catch (err) {
          result = `"${err.message}"`;
        }
        memo.push(`"${key}":${result}`);
        return memo;
      }, []);
      return `{${total.join(',')}}`;
    },
  };
}
