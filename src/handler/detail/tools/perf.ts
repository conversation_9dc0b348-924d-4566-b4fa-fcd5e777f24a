export function initPerf (requestStartTime) {
  const origin = Date.now();
  const times = [];
  const wait = origin - requestStartTime;
  const state = {
    origin,
    wait,
    stage: ''
  };
  return {
    set (key, val) {
      state[key] = val;
    },
    time(key) {
      const t = Date.now() - requestStartTime
      times.push(`${key}_${t}`);
    },
    stage (key) {
      const statrt = Date.now();
      return () => {
        // state[key] = statrt - origin;
        state[`${key}Cost`] = Date.now() - statrt;
      };
    },
    toJsonString () {
      try {
        if (times.length) {
          state.stage = times.join(',')
        }
        return JSON.stringify(state);
      } catch (err) {
        return '';
      }
    },
    getState(){
      return {
        ...state
      }
    }
  };
}
