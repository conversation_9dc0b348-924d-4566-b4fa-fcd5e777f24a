export function initPerf (requestStartTime) {
  const origin = Date.now();
  const state = {
    origin,
    wait: origin - requestStartTime,
  };
  return {
    setTime(key) {
      state[key] = Date.now() - requestStartTime
    },
    set (key, val) {
      state[key] = val;
    },
    stage (key) {
      const statrt = Date.now();
      return () => {
        // state[key] = statrt - origin;
        state[`${key}Cost`] = Date.now() - statrt;
      };
    },
    toJsonString () {
      try {
        return JSON.stringify(state);
      } catch (err) {
        return '';
      }
    },
    getState(){
      return {
        ...state
      }
    }
  };
}
