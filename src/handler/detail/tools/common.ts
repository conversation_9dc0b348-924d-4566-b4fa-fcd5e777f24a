export function getBeiJingDateNow() {
  // 时差处理，若当前环境不是北京时间，则调整时间到其对应时区时间显示
  const bjOffset = -480;
  // 获取当前环境与0时区的时差 单位是分
  const timeOffset = new Date().getTimezoneOffset() - bjOffset;
  const date = new Date();
  date.setMinutes(date.getMinutes() + timeOffset);
  return date.valueOf();
}

export function raceTasks(asyncTasks = [], timeout = 100, errorCode = 'timeout') {
  return Promise.race([
    ...asyncTasks,
    new Promise((resolve, reject) => {
      setTimeout(() => {
        const err: any = new Error(errorCode);
        err.code = errorCode;
        reject(err);
      }, timeout);
    }),
  ]);
}

export function checkOptionMatch(option, params) {
  const { black = {}, white = {}, user = [], userRatio = 0, enable = false } = option || {};

  if (enable) {
    if (Object.keys(black).length) {
      const b = checkMatchOnce(black, params);
      if (b) {
        return {
          message: "match_all_but_black",
          result: false
        }
      }
    }
    return {
      message: 'match_all',
      result: true,
    };
  }

  if (user.indexOf(params.userId) > -1) {
    return {
      message: 'match_userId',
      result: true,
    };
  }

  if (params.userId && userRatio > 0) {
    const matchNum = parseInt(params.userId.slice(-2));
    const result = matchNum < userRatio;

    if (result) {
      return {
        message: `match_userratio,user(${matchNum})<ratio(${userRatio})`,
        result,
      };
    }
  }

  const w = checkMatchOnce(white, params);
  const b = checkMatchOnce(black, params);
  // 在白名单内且不在黑名单的
  return {
    message: `w:${w},b:${b}`,
    result: !!w && !b,
  };
}

/**
 * 判断params中是否能命中rule中的值一次
 * @param rule { category:[123,456], id: [222] }
 * @param params { category: 123 }命中，{category:111}未命中, { id: 222 } 命中, { id:333, category: 456 }命中
 * @returns
 */
function checkMatchOnce(rule, params) {
  let list = Object.keys(rule);
  for (let i = 0, len = list.length; i < len; i++) {
    const key = list[i];
    // 规则默认是数组，也可以是字符串，反正通过indexOf查找命中
    const val = rule[key] || [];
    // 目标url上有该字段
    if (typeof params[key] !== 'undefined') {
      // 判断是否命中，不命中下一个继续
      const result = checkValMatch(key, val, params);
      if (result) {
        return true;
      }
    }
  }
  return false;

  function checkValMatch(key, val, params) {
    switch (typeof val) {
      case 'string':
        return val.indexOf(params[key]) > -1;
      case 'boolean':
        return !!params[key] == val;
      default:
        if (typeof val.indexOf == 'function') {
          return val.indexOf(params[key]) > -1;
        }
    }
    return false;
  }
}

export function checkDevHost(url, isDev) {
  const hosts = [['o.alicdn.com', 'dev.o.alicdn.com']];
  for (let i = 0, len = hosts.length; i < len; i++) {
    const [online, dev] = hosts[i];
    const target = isDev ? dev : online;
    if (url.indexOf(online) > -1 || url.indexOf(dev) > -1) {
      return url.replace(online, target).replace(dev, target);
    }
  }
  return url;
}

export function getJsonObjectString(e) {
  try {
    return e ? JSON.stringify(e) : '{}';
  } catch (err) {}
  return '{}';
}

export function parseCookie(cookieString) {
  const result = {};
  if (!cookieString) {
    return result;
  }
  cookieString.split(';').forEach(n => {
    if (!n) {
      return;
    }
    const [key, val] = n.trim().split('=');
    try {
      result[key] = decodeURIComponent(val);
    } catch (err) {}
  });
  return result;
}

export async function loadRemoteJson(url, timeout = 200) {
  let e = null;
  try {
    e = await raceTasks(
      [
        fetch(url, { cdnProxy: true })
          .then(res => res.json())
          .catch(err => null),
      ],
      timeout
    );
  } catch (err) {}
  return e || {};
}

// 简单的模糊化，像猴子乱打的一样
export function monkyCipher(str, shift = 1) {
  if (shift < 0) {
    return monkyCipher(str, shift + 26);
  }

  let output = '';
  const alias = {
    '/': 'z',
    _: 'x',
    '-': 'c',
  };
  const numAlias = 'asdfghjklp'.split('');
  for (let i = 0; i < str.length; i++) {
    let char = str[i];
    char = alias[char] || char;
    if (+char > 0) {
      char = numAlias[char];
    }
    if (char.match(/[a-z]/i)) {
      let code = char.charCodeAt(0);

      // 大写字母
      if (code >= 65 && code <= 90) {
        char = String.fromCharCode(((code - 65 + shift) % 26) + 65);
      }
      // 小写字母
      else if (code >= 97 && code <= 122) {
        char = String.fromCharCode(((code - 97 + shift) % 26) + 97);
      }
    }

    output += char;
  }

  return output;
}


export function defer() {
  let resolve = null;
  let reject = null;
  const promise = new Promise(function (_resolve, _reject){
    resolve = _resolve;
    reject = _reject;
  });

  return {
    promise,
    resolve(e) {
      if (resolve) {
        resolve(e);
      }
    },
    reject(err) {
      if (reject) {
        reject(err);
      }
    }
  }
}
