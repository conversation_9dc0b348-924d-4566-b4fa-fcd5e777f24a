export function getBeiJingDateNow() {
  // 时差处理，若当前环境不是北京时间，则调整时间到其对应时区时间显示
  const bjOffset = -480;
  // 获取当前环境与0时区的时差 单位是分
  const timeOffset = new Date().getTimezoneOffset() - bjOffset;
  const date = new Date();
  date.setMinutes(date.getMinutes() + timeOffset);
  return date.valueOf();
}

export function raceTasks(asyncTasks = [], timeout = 100, errorCode = 'timeout') {
  return Promise.race([
    ...asyncTasks,
    new Promise((resolve, reject) => {
      setTimeout(() => {
        const err: any = new Error(errorCode);
        err.code = errorCode;
        reject(err);
      }, timeout);
    }),
  ]);
}

export function checkOptionMatch(option, params) {
  const { black = {}, white = {}, user = [], userRatio = 0, enable = false } = option || {};

  if (enable) {
    if (Object.keys(black).length) {
      const b = checkMatchOnce(black, params);
      if (b) {
        return {
          message: 'match_all_but_black',
          result: false,
        };
      }
    }
    return {
      message: 'match_all',
      result: true,
    };
  }

  if (user.indexOf(params.userId) > -1) {
    return {
      message: 'match_userId',
      result: true,
    };
  }

  if (params.userId && userRatio > 0) {
    const matchNum = parseInt(params.userId.slice(-2));
    const result = matchNum < userRatio;

    if (result) {
      return {
        message: `match_userratio,user(${matchNum})<ratio(${userRatio})`,
        result,
      };
    }
  }

  // 名单值中只要有一个命中即为命中
  const w = checkMatchOnce(white, params);
  const b = checkMatchOnce(black, params);
  // 在白名单内且不在黑名单的
  return {
    message: `w:${w},b:${b}`,
    result: !!w && !b,
  };
}

/**
 * 判断params中是否能命中rule中的值一次
 * @param rule
 * @param context
 * @returns
 *
 * rule1 = { "env.isTaobao": "1" }
 * rule2 = { "appName": ["tb", "tm"] }
 * context1 = { env: { isTaobao: 1 }, appName: "tb" }
 * context2 = { env: { isTaobao: 0 }, appName: "tm" }
 *
 * checkMatchOnce(rule1, context1) => true
 * checkMatchOnce(rule1, context2) => false
 * checkMatchOnce(rule2, context1) => true
 * checkMatchOnce(rule2, context2) => true
 */
function checkMatchOnce(rule, context) {
  let list = Object.keys(rule);
  for (let i = 0, len = list.length; i < len; i++) {
    const key = list[i];
    // 规则默认是数组，也可以是字符串，反正通过indexOf查找命中
    const ruleVals = rule[key] || [];
    const contextVal = getData(context, key);
    // 目标url上有该字段
    if (typeof contextVal !== 'undefined') {
      // 判断是否命中，不命中下一个继续
      const result = checkValMatch(ruleVals, contextVal);
      if (result) {
        return true;
      }
    }
  }
  return false;

  function checkValMatch(ruleVals, contextVal) {
    switch (typeof ruleVals) {
      case 'string':
        // 规则 "aa.bb": "123,456"，只要contextVal包含其中即可
        return ruleVals.indexOf(contextVal) > -1;
      case 'boolean':
        // 规则 "aa.bb": true，那么只要aa.bb有值就行
        return ruleVals ? !!contextVal : false;
      default:
        // 规则 "aa.bb": ["1", 2]，contextValue需等于 "1" 或 2，注意数字和字符串区别
        if (typeof ruleVals.indexOf == 'function') {
          return ruleVals.indexOf(contextVal) > -1;
        }
    }
    return false;
  }
}

export function checkDevHost(url, isDev) {
  const hosts = [['o.alicdn.com', 'dev.o.alicdn.com']];
  for (let i = 0, len = hosts.length; i < len; i++) {
    const [online, dev] = hosts[i];
    const target = isDev ? dev : online;
    if (url.indexOf(online) > -1 || url.indexOf(dev) > -1) {
      return url.replace(online, target).replace(dev, target);
    }
  }
  return url;
}

export function getJsonObjectString(e) {
  try {
    return e ? JSON.stringify(e) : '{}';
  } catch (err) {}
  return '{}';
}

export function parseCookie(cookieString) {
  const result = {};
  if (!cookieString) {
    return result;
  }
  cookieString.split(';').forEach(n => {
    if (!n) {
      return;
    }
    const [key, val] = n.trim().split('=');
    try {
      result[key] = decodeURIComponent(val);
    } catch (err) {}
  });
  return result;
}

export async function loadRemoteJson(url, timeout = 200, cdnProxy = true) {
  let e = null;
  const start = Date.now();
  try {
    e = await raceTasks(
      [
        fetch(url, { cdnProxy })
          .then(res => res.json())
          .catch(err => null),
      ],
      timeout
    );
  } catch (err) {
    return {
      data: null,
      errorMessage: err && err.message,
      cost: Date.now() - start,
    };
  }
  return {
    data: e,
    cost: Date.now() - start,
  };
}

// 简单的模糊化，像猴子乱打的一样
export function monkyCipher(str, shift = 1) {
  if (shift < 0) {
    return monkyCipher(str, shift + 26);
  }

  let output = '';
  const alias = {
    '/': 'z',
    _: 'x',
    '-': 'c',
  };
  const numAlias = 'asdfghjklp'.split('');
  for (let i = 0; i < str.length; i++) {
    let char = str[i];
    char = alias[char] || char;
    if (+char > 0) {
      char = numAlias[char];
    }
    if (char.match(/[a-z]/i)) {
      let code = char.charCodeAt(0);

      // 大写字母
      if (code >= 65 && code <= 90) {
        char = String.fromCharCode(((code - 65 + shift) % 26) + 65);
      }
      // 小写字母
      else if (code >= 97 && code <= 122) {
        char = String.fromCharCode(((code - 97 + shift) % 26) + 97);
      }
    }

    output += char;
  }

  return output;
}

export function defer() {
  let resolve = null;
  let reject = null;
  const promise = new Promise(function (_resolve, _reject) {
    resolve = _resolve;
    reject = _reject;
  });

  return {
    promise,
    resolve(e) {
      if (resolve) {
        resolve(e);
      }
    },
    reject(err) {
      if (reject) {
        reject(err);
      }
    },
  };
}

export function getData(data, path, defaultValue?) {
  if (data && path) {
    // eslint-disable-next-line no-useless-escape
    const keys = path.match(/([^\.\[\]"']+)/g);

    for (let i = 0, len = keys.length; i < len; i++) {
      data = data[keys[i]];
      if (inValid(data)) {
        return defaultValue;
      }
    }
    return data;
  }
  return defaultValue;

  function inValid(e) {
    return typeof e === 'undefined' || e === null;
  }
}

/**
 * 如write同一时间只能执行一个，必须等上一个await结束才能执行下一个
 * 如果提前执行了，er会直接终止，很坑
 */
export function makeAsyncQueue() {
  const taskList = [];
  let loopCheck = null;

  return {
    async start(task) {
      taskList.push(task);
      if (!loopCheck) {
        loopCheck = startLoop();
      }
      await loopCheck;
      loopCheck = null;
    },
  };

  async function startLoop() {
    let task = taskList.pop();
    while (task) {
      await task();
      task = taskList.pop();
    }
  }
}
