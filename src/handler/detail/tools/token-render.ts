// 流式首屏渲染器v2
export function initTokenRender() {
  const U = initUtil();
  const E = initEnum();
  const NODE_HANDLER = initNodeHandler();
  // 全局上下文
  const CONTEXT = {
    // 注册的组件
    component: {},
  };
  return {
    renderHtmlString,
  };
  function renderHtmlString(arr, data) {
    return parseNodeList(arr, { data: data || {} }).join('');
  }
  function parseNodeList(list, options) {
    if (!list || !list.length) {
      return [];
    }
    // 纠错，可能把node当List传入了
    if (typeof list[0] === 'string') {
      const result = parseSingleNode(list, options);
      if (!result || !result.length) {
        return [];
      }
      return [result];
    }
    const groupState = {};
    return list.reduce((memo, child, index) => {
      const result = parseSingleNode(child, { ...options, index, groupState });
      if (result && result.length) {
        memo.push(...result);
      }
      return memo;
    }, []);

    function parseSingleNode(node, opt) {
      const nodeType = node[0];
      if (NODE_HANDLER[nodeType]) {
        return NODE_HANDLER[nodeType](node, opt);
      }
      return [];
    }
  }
  // 避免污染token，复制一份
  function copyNode(node, replaceState) {
    if (!node) {
      return [];
    }
    replaceState = replaceState || { tagAttr: {}, tagName: E.COMP.Fragment };
    const pureNode = [];
    pureNode[E.TOKEN_VALUE.type] = node[E.TOKEN_VALUE.type];
    pureNode[E.TOKEN_VALUE.children] = replaceState.children || node[E.TOKEN_VALUE.children];
    pureNode[E.TOKEN_VALUE.tagName] = replaceState.tagName || node[E.TOKEN_VALUE.tagName];
    pureNode[E.TOKEN_VALUE.tagAttr] = {
      ...(node[E.TOKEN_VALUE.tagAttr] || {}),
      ...(replaceState.tagAttr || {}),
    };
    return pureNode;
  }
  function readNode(node) {
    // 子标签集合
    const children = node[E.TOKEN_VALUE.children] || [];
    // 标签名，组件得是大写首字母
    const tagName = node[E.TOKEN_VALUE.tagName];
    // 标签属性，复制一份
    const tagAttr = node[E.TOKEN_VALUE.tagAttr] || {};
    return {
      children,
      tagName,
      tagAttr,
    };
  }

  // ----- 节点处理模块 -----
  function initNodeHandler() {
    return {
      text(node, { data }) {
        if (node[1] && node[1].length) {
          return node[1].map(txt => U.parseTemplate(txt, data));
        }
      },
      tag: parseTag,
    };
    function parseTag(node, opt) {
      const { tagName, tagAttr, children } = readNode(node);
      if (!tagName) {
        return;
      }
      // ---- 「标签」Template注册 ----
      if (tagName == E.COMP.template) {
        if (!tagAttr[E.DIRECT.xName]) {
          return;
        }
        CONTEXT.component[tagAttr[E.DIRECT.xName]] = children;
        return;
      }
      // ---- 「指令」 x-if/x-else ----
      if (tagAttr[E.DIRECT.xIf]) {
        const key = tagAttr[E.DIRECT.xIf];
        const revert = key[0] == '!';
        const data = U.getData(opt.data, revert ? key.slice(1) : key);
        const enable = revert ? !data : !!data;
        if (!enable) {
          // 记录最近一次xif判断为false的序号
          opt.groupState.xIfFalseIndex = opt.index;
          return;
        }
      } else if (!U.inValid(tagAttr[E.DIRECT.xElse])) {
        // ---- 「指令」x-else ----
        // 只有上一个xif判断为false，这里才展示
        const enableElse = opt.groupState.xIfFalseIndex > -1 && opt.groupState.xIfFalseIndex === opt.index - 1;
        if (!enableElse) {
          return;
        }
      }

      // ---- 「指令」x-for ----
      if (tagAttr[E.DIRECT.xFor]) {
        const dataList = U.getData(opt.data, tagAttr[E.DIRECT.xFor]);
        if (!dataList || !dataList.length) {
          return;
        }
        // ---- 「指令」x-item ---- 默认$取遍历出的item，可通过x-item设置别名
        const itemKey = tagAttr[E.DIRECT.xItem] || '$';
        return dataList
          .reduce((memo, item, index) => {
            // 除特殊属性外继续解析
            const newNode = copyNode(node, {
              tagAttr: {
                [E.DIRECT.xFor]: null,
                [E.DIRECT.xItem]: null,
              },
            });
            const isArr = U.isArray(item);
            // eslint-disable-next-line no-nested-ternary
            const itemVal = isArr
              ? [...item]
              : typeof item === 'string'
                ? { text: item }
                : {
                  ...(item || {}),
                };
            itemVal.index = index;
            const context = {
              ...opt,
              data: {
                ...opt.data,
                // 约定的特殊字段
                [itemKey]: itemVal,
              },
            };
            checkAttr(readNode(newNode).tagAttr, context.data);
            const groupList = parseNodeList([newNode], context);
            if (groupList.length) {
              memo.push(...groupList);
            }
            return memo;
          }, [])
          .join('');
      }


      checkAttr(tagAttr, opt.data);

      // ---- 「标签」Template使用 ----
      if (CONTEXT.component[tagName]) {
        // 组件本质是一个特殊的Fragement节点，仅渲染内部组件，且仅根据props包含必要的状态
        const newNodeInfo = readNode(
          copyNode(node, {
            tagName: E.COMP.fragment,
            children: CONTEXT.component[tagName],
          })
        );
        // 透传所有状态给组件内部，组件上的props可以改写别名
        const componentState = {
          ...opt.data,
        };
        Object.keys(tagAttr).forEach((key) => {
          // 非特殊指令，是属性传递
          if (!E.DIRECT[key]) {
            // 组件外的上下文数据搬运到组件内
            componentState[key] = U.getData(opt.data, tagAttr[key]);
          }
        });
        // ---- 组件插槽 ----
        if (children.length) {
          // ---- 关键字children ----
          componentState.children = renderChildren();
        }

        return renderTag({
          tagName: newNodeInfo.tagName,
          tagAttr: newNodeInfo.tagAttr,
          data: componentState,
          renderChildren: () => renderChildren(newNodeInfo.children, { data: componentState }),
        });
      }
      return renderTag({
        tagName,
        tagAttr,
        data: opt.data,
        renderChildren,
      });
      // 渲染子节点，支持更改子节点上下文
      function renderChildren(n = children, contextOption = opt) {
        return parseNodeList(n, contextOption).join('');
      }
    }

    function checkAttr(attrData, context) {
      // 处理动态赋值class名
      if (attrData[E.DIRECT.xC]) {
        let data = U.getData(context, attrData[E.DIRECT.xValue]);
        // 没有上下文取值，则以该字符为class名
        if (!data) {
          data = attrData[E.DIRECT.xValue];
        }
        const key = attrData[E.DIRECT.xC];
        const revert = key[0] == '!';
        const result = U.getData(context, revert ? key.slice(1) : key);
        const enable = revert ? !result : !!result;
        if (enable) {
          attrData.class = `${attrData.class || ''} ${data}`;
        }
      }
    }

    // 执行节点渲染
    function renderTag({ tagName, tagAttr, data, renderChildren }) {
      let attrString = getAttrString(tagAttr, data);
      attrString = attrString ? ` ${attrString}` : '';
      // ---- 「标签」单闭合html ----
      if (E.VOID_ELEMENT[tagName]) {
        return [`<${tagName}${attrString}/>`];
      }
      const childrenString = renderChildren();
      // ---- 「标签」Fragment ----
      if (tagName === E.COMP.fragment) {
        return [childrenString];
      }
      return [`<${tagName}${attrString}>${childrenString}</${tagName}>`];
      // 序列化标签属性
      function getAttrString(attrs, context) {
        return Object.keys(attrs)
          .reduce((memo, key) => {
            const val = U.parseTemplate(attrs[key], context);
            if (!E.DIRECT[key] && !U.inValid(val)) {
              memo.push(`${key}="${val}" `);
            }
            return memo;
          }, [])
          .join('');
      }
    }
  }
  // ----- 常量模块 -----
  function initEnum() {
    // 支持的特殊标签
    const DIRECT = {
      xIf: 'x-if',
      xC: 'x-c',
      xValue: 'x-value',
      xElse: 'x-else',
      xFor: 'x-for',
      xItem: 'x-item',
      xName: 'x-name',
      'x-if': 1,
      'x-for': 1,
      'x-else': 1,
      'x-item': 1,
      'x-name': 1,
      'x-c': 1,
      'x-value': 1,
    };
    // token中对应节点属性的位置
    const TOKEN_VALUE = {
      type: 0,
      children: 1,
      tagName: 2,
      tagAttr: 3,
    };
    // 特殊标签，区分Html标签，都要是大写
    const COMP = {
      fragment: 'Fragment',
      template: 'Template',
      Fragment: 1,
      Template: 1,
    };
    // 原生html中单闭合标签
    const VOID_ELEMENT = {
      area: 1,
      base: 1,
      basefont: 1,
      br: 1,
      col: 1,
      command: 1,
      embed: 1,
      frame: 1,
      hr: 1,
      img: 1,
      input: 1,
      isindex: 1,
      keygen: 1,
      link: 1,
      meta: 1,
      param: 1,
      source: 1,
      track: 1,
      wbr: 1,
    };
    return {
      DIRECT,
      COMP,
      TOKEN_VALUE,
      VOID_ELEMENT,
    };
  }
  // ----- 工具模块 -----
  function initUtil() {
    return {
      getData,
      inValid,
      isArray,
      parseTemplate,
    };
    function getData(data, path, defaultValue?) {
      if (data && path) {
        // eslint-disable-next-line no-useless-escape
        const keys = path.match(/([^\.\[\]"']+)/g);

        for (let i = 0, len = keys.length; i < len; i++) {
          data = data[keys[i]];
          if (inValid(data)) {
            return defaultValue;
          }
        }
        return data;
      }
      return defaultValue;
    }
    function inValid(e) {
      return typeof e === 'undefined' || e === null;
    }
    function isArray(e) {
      return Object.prototype.toString.call(e) === '[object Array]';
    }
    function parseTemplate(str, data) {
      try {
        data = data || {};
        return (
          (str || '').trim().replace(/\{\{([^{}]+)\}\}/g, (matched, group) => {
            const d = getData(data, group.trim());
            return inValid(d) ? '' : d;
          }) || ''
        );
      } catch (err) {
        console.error(err);
      }
      return '';
    }
  }
}
