import { projectWhiteMap, projectKvMap, streamRenderConfig } from '../const';

/**
 * '/app/trip/rx-travel-detail/pages/index'
 * -->
 * groupname: rx-travel-detail
 * servicename: index
 *
 * '/app/trip/rx-travel-order-detail/pages/detail'
 * -->
 * groupName: rx-travel-order-detail
 * servicename: detail
 * @param pathname
 * @returns
 */
export function formatPath(pathname) {
  try {
    const test = pathname.match(/\/app\/trip\/(\S*)\/pages\/(\S*)/);
    if (test) {
      return {
        groupname: test[1],
        servicename: test[2],
      };
    }
  } catch (err) {}

  return {
    groupname: '',
    servicename: '',
  };
}

export function isVacationStreamPage(pathname, urlString?: string) {
  if (streamRenderConfig[pathname]) {
    return true;
  }
  if (urlString.indexOf('_use_vstream') > -1) {
    return true;
  }
  return !!parseConfigKey(pathname);
}

// 动态解析度假页面配置key
export function parseConfigKey(pathname) {
  if (projectKvMap[pathname]) {
    return projectKvMap[pathname];
  }
  const { groupname, servicename } = formatPath(pathname);
  if (!groupname || !servicename) {
    return;
  }

  if (projectWhiteMap[groupname]) {
    return parseCacheKey(groupname, servicename);
  }
  return;
}

/**
 * rx-trip/index --> rx-trip
 * rx-trip/detail -> rx-trip_detail
 */
export function parseCacheKey(groupname, servicename) {
  const paths = [groupname];
  if (servicename !== 'index') {
    paths.push(servicename);
  }
  return paths.join('_');
}
