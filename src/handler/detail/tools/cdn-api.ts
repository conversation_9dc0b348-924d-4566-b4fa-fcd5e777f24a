import { FAAS_HOST_PRE, FAAS_HOST_ONLINE } from '../const';
import { raceTasks } from './common';

export async function setJsonCache (cacheKey, jsonData, seconds = 60 * 60, isPre = false) {
  try {
    const jsonString = JSON.stringify(jsonData);
    const response = new Response(jsonString);
    response.headers.set('cache-control', `max-age=${seconds}`);
    const result: any = await cache.put(`http://${isPre ? FAAS_HOST_PRE : FAAS_HOST_ONLINE}${cacheKey}`, response);
    return {
      cacheKey,
      errorMessage: result || '',
    };
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
    };
  }
}

export async function getJsonCache (cacheKey, isPre = false) {
  try {
    const data = await cache.get(`http://${isPre ? FAAS_HOST_PRE : FAAS_HOST_ONLINE}${cacheKey}`);
    const jsonData = await data.json();
    return {
      cacheKey,
      errorMessage: '',
      data: jsonData,
    };
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
      data: null,
    };
  }
}


export async function getKvCache (key) {
  const namespace = 'fliggyrax_124215';
  try {
    const result = await raceTasks([loadKv()], 30);
    return {
      data: result
    };
  } catch (err) {
    return {
      errorMessage: err.message,
    };
  }

  async function loadKv () {
    const edgeKv = new EdgeKV({ namespace });
    return edgeKv.get(key, {
      type: 'text',
    })
  }
}

export async function getKvCacheJson (key) {
  try {
    const result: any = await getKvCache(key);
    if (!result || !result.data) {
      return {
        data: null,
        errorMessage: result && result.errorMessage
      }
    }
    return {
      data: JSON.parse(result.data)
    }
  } catch (err) {
    return {
      errorMessage: err.message
    }
  }
}
