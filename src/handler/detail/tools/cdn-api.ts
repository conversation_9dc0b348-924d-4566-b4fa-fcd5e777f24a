import { FAAS_HOST_PRE, FAAS_HOST_ONLINE, VACATION_KV_NS } from '../const';
import { raceTasks } from './common';

export async function setJsonCache(cacheKey, jsonData, seconds = 60 * 60, isPre = false) {
  try {
    const jsonString = JSON.stringify(jsonData);
    const response = new Response(jsonString);
    response.headers.set('cache-control', `max-age=${seconds}`);
    const result: any = await cache.put(`http://${isPre ? FAAS_HOST_PRE : FAAS_HOST_ONLINE}${cacheKey}`, response);
    return {
      cacheKey,
      errorMessage: result || '',
    };
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
    };
  }
}

export async function getJsonCache(cacheKey, isPre = false) {
  try {
    if (!cacheKey) {
      return {
        cacheKey,
        errorMessage: 'cacheKey is empty',
        data: null,
      };
    }
    const data = await cache.get(`http://${isPre ? FAAS_HOST_PRE : FAAS_HOST_ONLINE}${cacheKey}`);
    const jsonData = await data.json();
    return {
      cacheKey,
      errorMessage: '',
      data: jsonData,
    };
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
      data: null,
    };
  }
}

// 默认走度假独立kv空间
export async function getKvCache(key, timeout = 30, namespace = VACATION_KV_NS) {
  try {
    const result = await raceTasks([loadKv()], timeout);
    return {
      data: result,
    };
  } catch (err) {
    return {
      errorMessage: err.message,
    };
  }

  async function loadKv() {
    const edgeKv = new EdgeKV({ namespace });
    return edgeKv.get(key, {
      type: 'text',
    });
  }
}

export async function getKvCacheJson(key, timeout = 30, namespace = VACATION_KV_NS) {
  try {
    const start = Date.now();
    const result = await getKvCache(key, timeout, namespace);
    if (!result.data) {
      return {
        data: null,
        errorMessage: result.errorMessage,
        cost: Date.now() - start,
      };
    }
    return {
      data: JSON.parse(result.data),
      cost: Date.now() - start,
    };
  } catch (err) {
    return {
      errorMessage: err.message,
    };
  }
}
