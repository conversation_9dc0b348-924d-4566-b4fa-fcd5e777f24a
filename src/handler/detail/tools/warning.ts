import { BUILD_VERSION } from '../const';
export function dingdingwarning(event, err, exMessage = []) {
  try {
    const { request } = event || {};
    const url = request && request.url;
    const contents = [
      'CDN异常',
      `build：${BUILD_VERSION}`,
      `message：${err && err.message}`,
      `stack：${err && err.stack}`,
      `url：${url}`,
      ...exMessage
    ];
    // https://open.dingtalk.com/document/orgapp/custom-robots-send-group-messages
    fetch('https://oapi.dingtalk.com/robot/send?access_token=b11dcac9a2766a80e824ef05c42e345188f794d752da9820b8d5bc5cd547dd7e', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: `{"msgtype": "text","at":{ "atMobiles": ["15062262826"] },"text": {"content":"${contents.join('，\n')}"}}`,
      mode: 'no-cors',
    }).catch(() => {});
  } catch (err) {}
}

export function assert(shouldTrue, opt: any = {}) {
  if (!shouldTrue) {
    const error: any = new Error(opt.message || opt.code);
    error.code = opt.code;
    error.detailMessage = opt.detail;
    throw error;
  }
}
