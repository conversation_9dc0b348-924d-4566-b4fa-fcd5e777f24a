import { BUILD_VERSION } from '../const';
export function dingdingwarning(event, err, exMessage = []) {
  try {
    const { request } = event || {};
    const url = request && request.url;
    const contents = [
      'CDN异常',
      `build：${BUILD_VERSION}`,
      `message：${err && err.message}`,
      `stack：${err && err.stack}`,
      `url：${url}`,
      ...exMessage
    ];
    // https://open.dingtalk.com/document/orgapp/custom-robots-send-group-messages
    fetch('https://oapi.dingtalk.com/robot/send?access_token=364245ba6cc3527183ee1b09b77c4ca2fb8d4ec10860c93ea627e3796b8b85eb', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: `{"msgtype": "text","at":{ "atMobiles": ["15062262826"] },"text": {"content":"${contents.join('，\n')}"}}`,
      mode: 'no-cors',
    }).catch(() => {});
  } catch (err) {}
}

export function assert(shouldTrue, opt: any = {}) {
  if (!shouldTrue) {
    const error: any = new Error(opt.message || opt.code);
    error.code = opt.code;
    error.detailMessage = opt.detail;
    throw error;
  }
}
