interface IEventGeo {
  ip_city_en?: string; // 'Hangzhou';
  ip_region_id?: string; // '330000';
  ip_country_id?: string; // 'CN';
  ip_region_en?: string; // 'Zhejiang';
  ip_isp_en?: string; // 'AliBaBa';
  ip_isp_id?: string; // '100098';
  ip_country_en?: string; // 'China';
  remote_port?: string; // '52089';
  remote_addr?: string; // '***********';
  ip_city_id?: string; // '330100';
}

interface ICookieGeo {
  _fli_cityCode?: string; // 330100
  _fli_cityName?: string; // 杭州
  // 和经过geolocation缓存的所在地区分
  _fli_destCode?: string; // 目的地城市code
  _fli_destName?: string; // 目的地城市名称
  _fli_country?: string; // 中国
  _fli_latitude?: string; // 30.284815
  _fli_longitude?: string; // 120.02309"
}

interface IQueryGeo {
  cityCode?: string; // 330100
  cityName?: string;
  lbsCityCode?: string; // 手淘闪购场景金刚位透传
  lbsCityName?: string;
}

export function getLocation(event, query: IQueryGeo = {}, cookieObject: ICookieGeo = {}) {
  const { requestCityCode, cityCode } = getCityCode(event, query, cookieObject);
  const result = {
    // 结合query和cookie获取的当前期望访问的cityCode
    requestCityCode,
    // 用户所在的cityCode
    cityCode,
    // 是国际化访问
    oversea: !!checkOverSea(event),
  };

  return result;
}

function getCityCode(event: any = {}, query: IQueryGeo, cookieObject: ICookieGeo) {
  const info: IEventGeo = event.info;
  const cityCode = info?.ip_city_id;

  if (query.cityCode && query.cityName) {
    return {
      requestCityCode: query.cityCode,
      requestCityName: query.cityName,
      cityCode,
    };
  }

  if (query.lbsCityCode && query.lbsCityName) {
    return {
      requestCityCode: query.lbsCityCode,
      requestCityName: query.lbsCityName,
      cityCode,
    };
  }

  // 优先取目的地缓存
  if (cookieObject._fli_destCode && cookieObject._fli_destName) {
    return {
      requestCityCode: cookieObject._fli_destCode,
      requestCityName: cookieObject._fli_destName,
      cityCode,
    };
  }

  if (cookieObject._fli_cityCode && cookieObject._fli_cityName) {
    return {
      requestCityCode: cookieObject._fli_cityCode,
      requestCityName: cookieObject._fli_cityName,
      cityCode,
    };
  }

  return {
    requestCityCode: info.ip_city_id,
    cityCode,
  };
}

// 判断是否是国际化访问，faas没有海外机器不如csr快
function checkOverSea(event) {
  // https://aliyuque.antfin.com/progsvc/wd1vpu/sw43ec
  const info: IEventGeo = event.info || {};
  const { ip_country_id, ip_region_id, ip_city_id } = info;
  // 预发域名上没值
  if (ip_country_id && ip_country_id !== 'CN') {
    return true;
  }
  // https://aliyuque.antfin.com/trip_plat/pu6xpg/aprq6wn91soosu8d
  const special = {
    // 香港
    '810001': 1,
    '810000': 1,
    '810100': 1,
    // 澳门
    '820001': 1,
    '820000': 1,
    // 台湾
    '710001': 1,
    '710000': 1,
  };

  return !!special[ip_region_id] || !!special[ip_city_id];
}
