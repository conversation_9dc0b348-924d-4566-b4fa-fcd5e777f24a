import { streamRootStartReg, streamFaasStartReg, streamMetaName_Version } from '../const';

/**
 * 从 <meta name="xx" content="" /> 标签中解析出content值
 * @param htmlText
 * @param metaName
 * @returns
 */
export function getMetaContent(htmlText, metaName) {
  try {
    const reg = new RegExp(`<meta\\s+name="?'?${metaName}"?'?\\s+content="?'?([^"'>]*)'?"?\/?>`);
    const result = htmlText.match(reg);
    if (result) {
      return result[1] || '';
    }
  } catch (err) {}
  return '';
}

/**
 * 从 <meta name="xx" content="" /> 标签中解析出content值并转成json对象
 * @param htmlText
 * @param metaName
 * @returns
 */
export function getMetaJson(htmlText, metaName) {
  const value = getMetaContent(htmlText, metaName);
  return safeDecodeJson(value);

  function safeDecodeJson(jsonString) {
    if (!jsonString) {
      return;
    }
    try {
      return JSON.parse(decodeURIComponent(jsonString));
    } catch (err) {
      try {
        return JSON.parse(decodeURIComponent(decodeURIComponent(jsonString)));
      } catch (err2) {}
    }
  }
}

/**
 * 从html文本中解析当前项目版本
 * @param htmlText
 * @returns
 */
export function getVersionFromHtml(htmlText) {
  return getMetaContent(htmlText || '', streamMetaName_Version);
}

/**
 * 解析ssrhtml的结构，以html业务渲染/ssr数据为节点，将html分成三段便于处理：
 * <html>   |
 *          |
 *      beforeView
 *          |
 * <div id="root"
 *          |
 *      beforeData
 *          |
 * <script data-from="server"
 *          |
 *      beforeEnd
 *          |
 * </html>  |
 *
 * @param htmlText
 * @returns
 */
export function parseSsrHtml(htmlText) {
  if (!htmlText) {
    return null;
  }
  // fcp渲染成功，截取部分ssr数据拼接渲染
  const [rootTag] = htmlText.match(streamRootStartReg) || [];
  const [faasTag] = htmlText.match(streamFaasStartReg) || [];

  if (!rootTag || !faasTag) {
    return null;
  }

  // ssr文本开始位置
  const contentStartPosition = htmlText.indexOf(rootTag);
  // ssr服务端数据开始位置
  const scriptStartPosition = htmlText.indexOf(faasTag);

  if (contentStartPosition == -1 || scriptStartPosition == -1) {
    return null;
  }

  return {
    htmlText,
    version: getVersionFromHtml(htmlText),
    beforeView: htmlText.slice(0, contentStartPosition),
    beforeData: htmlText.slice(contentStartPosition, scriptStartPosition),
    beforeEnd: htmlText.slice(scriptStartPosition),
    dataScript: getDataScript(),
  };

  function getDataScript() {
    const scriptString = htmlText.slice(scriptStartPosition);
    const endString = '</script>';
    const end = scriptString.indexOf(endString);
    return end > -1 ? scriptString.slice(0, end + endString.length) : '';
  }
}

export function getCssTagList(e) {
  return (e.match(/\<link[^\>]+>/g) || [])
    .filter(l => /rel=['"]+stylesheet/.test(l))
    .reduce((memo, origin) => {
      const matched = origin.match(/\s*href=['"](.*?)['"]\s*/);
      if (matched) {
        const url = matched[1];
        if (url) {
          memo.push({
            tag: origin,
            url,
          });
        }
      }
      return memo;
    }, []);
}
