import { streamRootStartReg, streamFaasStartReg, streamMetaName_Version, getVersionFormatScripts, getLinkReplaceScripts } from '../const';

/**
 * 从 <meta name="xx" content="" /> 标签中解析出content值
 * @param htmlText
 * @param metaName
 * @returns
 */
export function getMetaContent(htmlText, metaName) {
  try {
    const reg = new RegExp(`<meta\\s+name="?'?${metaName}"?'?\\s+content="?'?([^"'>]*)'?"?\/?>`);
    const result = htmlText.match(reg);
    if (result) {
      return result[1] || '';
    }
  } catch (err) {}
  return '';
}

/**
 * 从 <meta name="xx" content="" /> 标签中解析出content值并转成json对象
 * @param htmlText
 * @param metaName
 * @returns
 */
export function getMetaJson(htmlText, metaName) {
  const value = getMetaContent(htmlText, metaName);
  return safeDecodeJson(value);

  function safeDecodeJson(jsonString) {
    if (!jsonString) {
      return;
    }
    try {
      return JSON.parse(decodeURIComponent(jsonString));
    } catch (err) {
      try {
        return JSON.parse(decodeURIComponent(decodeURIComponent(jsonString)));
      } catch (err2) {}
    }
  }
}

/**
 * 从html文本中解析当前项目版本
 * @param htmlText
 * @returns
 */
export function getVersionFromHtml(htmlText) {
  return getMetaContent(htmlText || '', streamMetaName_Version);
}

/**
 * 解析ssrhtml的结构，以html业务渲染/ssr数据为节点，将html分成三段便于处理：
 * <html>   |
 *          |
 *      beforeView
 *          |
 * <div id="root"
 *          |
 *      beforeData
 *          |
 * <script data-from="server"
 *          |
 *      beforeEnd
 *          |
 * </html>  |
 *
 * @param htmlText
 * @returns
 */
export function parseSsrHtml(htmlText) {
  if (!htmlText) {
    return null;
  }
  // fcp渲染成功，截取部分ssr数据拼接渲染
  const [rootTag] = htmlText.match(streamRootStartReg) || [];
  const [faasTag] = htmlText.match(streamFaasStartReg) || [];

  if (!rootTag || !faasTag) {
    return null;
  }

  // ssr文本开始位置
  const contentStartPosition = htmlText.indexOf(rootTag);
  // ssr服务端数据开始位置
  const scriptStartPosition = htmlText.indexOf(faasTag);

  if (contentStartPosition == -1 || scriptStartPosition == -1) {
    return null;
  }

  return {
    htmlText,
    version: getVersionFromHtml(htmlText),
    beforeView: htmlText.slice(0, contentStartPosition),
    beforeData: htmlText.slice(contentStartPosition, scriptStartPosition),
    beforeEnd: htmlText.slice(scriptStartPosition),
    dataScript: getDataScript(),
  };

  function getDataScript() {
    const scriptString = htmlText.slice(scriptStartPosition);
    const endString = '</script>';
    const end = scriptString.indexOf(endString);
    return end > -1 ? scriptString.slice(0, end + endString.length) : '';
  }
}

export function getCssTagList(e) {
  return (e.match(/\<link[^\>]+>/g) || [])
    .filter(l => /rel=['"]+stylesheet/.test(l))
    .reduce((memo, origin) => {
      const matched = origin.match(/\s*href=['"](.*?)['"]\s*/);
      if (matched) {
        const url = matched[1];
        if (url) {
          memo.push({
            tag: origin,
            url,
          });
        }
      }
      return memo;
    }, []);
}

/**
 * 处理两种情况：
 * -- 【1】项目head内结构变化
 * 有些项目的head结构会变化，重点关注style标签是否一致
 * 如版本1.0.0时，有common.css 和index.css
 * 而版本1.0.1时，只有了index.css
 * 样式冗余需要删
 *
 * 又或是
 * 如版本1.0.0时，只有index.css
 * 而版本1.0.1时，有了index.css和common.css
 * 样式丢了需要增
 *
 * 问题常见于多页面的仓库，在多页同时发布时会抽出公共的common.css，若下次迭代仅发单页则又不生成common.css
 * 可通过package.json中设置clam.minChunks = 99强制不抽common来稳定每个页面的构建结果
 *
 * -- 【2】项目head内版本不同
 * 因构造fcp展示需要html>head等标签内容，所以存在复用场景
 * 即2.0.0版本复用了1.0.0的html结构，但因为版本不同css样式会有差异，需要运行时修正
 */
export function checkStyleMiss(ctx, fcpHeadString, fspHeadString) {
  // 没有fcp无需修正，无htmlstring无法修正，未识别出分组不是正常访问ssr
  if (!fcpHeadString || !fspHeadString || !ctx.groupname) {
    return '';
  }

  const fcpV = getVersionFromHtml(fcpHeadString);
  const fspV = getVersionFromHtml(fspHeadString);

  // 无版本不处理，版本一致无需修正
  if (!fcpV || !fspV || fcpV == fspV) {
    return '';
  }

  const reg = new RegExp(`/${ctx.groupname}/([0-9.]+)/`);
  const fcpLinkList = getCssTagList(fcpHeadString).filter(filterTargetCss);
  const fspLinkList = getCssTagList(fspHeadString).filter(filterTargetCss);

  const scriptList = [];

  // 数量一致则认为结构相等，仅将fcp的css版本为fsp的即可
  if (fcpLinkList.length == fspLinkList.length) {
    const scriptString = getVersionFormatScripts(ctx.groupname, fspV);
    if (scriptString) {
      scriptList.push(scriptString)
    }
  } else {
    const scriptString = getLinkReplaceScripts(fcpLinkList.map(e => e.url), fspLinkList.map(e => e.url));
    if (scriptString) {
      scriptList.push(scriptString)
    }
  }

  // 无需修正
  if (!scriptList.length) {
    return '';
  }

  return `<script name="fix-fcp-css">${scriptList.join('')}</script>`;

  function filterTargetCss(e) {
    const { url } = e;
    return !!url.match(reg);
  }
}
