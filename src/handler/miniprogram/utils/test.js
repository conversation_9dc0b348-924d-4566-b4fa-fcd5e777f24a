function test() {
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();
  const encoder = new TextEncoder();
  const writeHtml = (e) => writer.write(encoder.encode(e));

  writeHtml('<html><head><title>Test</title></head><body>');

  setTimeout(() => {
    writeHtml('<html><head><title>Test2</title></head><body>');
  }, 0);

   setTimeout(() => {
    writeHtml('<html><head><title>Test3</title></head><body>');
  }, 50);

  return new Response(readable, {
    headers: {
      'content-type': 'text/html; charset=utf-8',
    },
    status: 200,
  })
}

async function handleTest() {
  const response = test();
  const html = await response.clone().text();
  console.log(6666, html)
  return html
}

handleTest();