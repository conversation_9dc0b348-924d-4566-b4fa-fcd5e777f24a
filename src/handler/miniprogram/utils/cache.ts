import { getCsrFetchUrl } from './url';
import { CSR_CACHE_QUERY } from '../constant/index';
import Logger from './logger';
import { removeErCacheHeaders } from '../../../utils';
import { getAppEnv, getCookies } from './headers';

/** 
 * 获取缓存key：异步信息跟平台、用户有关，因此要作为缓存key
 * */
function getCacheKey(request: Request, reqUrl: URL) {
  try {
    const extraParams = new URLSearchParams(reqUrl.search);
    const { headers } = request;
    const clientIP = headers.get("Ali-Cdn-Real-Ip");
    // 获取userid
    const cookies = getCookies(headers);
    const platform = getAppEnv(headers);
    const user = cookies.munb || cookies.unb || cookies.openId;

    // 平台信息
    if (platform) {
      extraParams.set('platform', platform);
    }

    // 用户信息
    if (user) {
      extraParams.set('user', user);
    } else if (!reqUrl.searchParams.get('_wx_user_info_')){
      // 优先取_wx_user_info_参数，兜底clientIP
      extraParams.set('clientIP',clientIP);
    }

    // 把特殊参数去掉
    extraParams.delete('_bx-m');
    extraParams.delete('_preMaxAge');
    extraParams.delete('debugCache');
    extraParams.delete(CSR_CACHE_QUERY.DELETE_CACHE);
    extraParams.delete(CSR_CACHE_QUERY.PRE_FETCH);

    return `http://${reqUrl.host}${reqUrl.pathname}?${extraParams.toString()}`;
  } catch (e) {
    return '';
  }
}

/** 清除缓存 */
export async function deleteCache(request: Request, reqUrl: URL, realHost: string) {
  const preCacheUrl = getCacheKey(request, reqUrl);
  const res: any = {};
  // 清除源站请求缓存
  res.deletePageCache = await cache.delete(getCsrFetchUrl(reqUrl, realHost)).catch(err => err && err.message)
  if (preCacheUrl) {
    // 清除预请求缓存
    res.deletePreCache = await cache.delete(preCacheUrl).catch(err => err && err.message);
  }
  return JSON.stringify(res);
}

/** 从预请求的缓存中读取 */
export async function getPageByPrefetchCache(event: FetchEvent, reqUrl: URL, sls: Logger) {
  return new Promise(async (resolve) => {
    const { request } = event;
    const preCacheUrl = getCacheKey(request, reqUrl);
    if (preCacheUrl) {
      const cacheRes = await cache.get(preCacheUrl).catch((err) => {
        event.waitUntil(sls.putSlsLog({
          logType: 'error',
          errorType: 'getCsrPageByCacheError',
          errInfo: err,
          errMsg: err?.message || '',
        }));
        return null;
      });
      // 测试日志
      if (reqUrl.searchParams.get('debugCache')) {
        event.waitUntil(sls.putSlsLog({
          logType: 'getPageByPrefetchCache',
          url: preCacheUrl,
          info: !!cacheRes,
        }));
      }
      if (cacheRes) {
        sls.performanceTimeEnd('getPage');
        event.waitUntil(sls.performanceEnd('normal', { hitCache: 2 }));
        cacheRes.headers.set('content-type', 'text/html; charset=utf-8');
        cacheRes.headers.set('x-proxy-hit-cache', '2');
        removeErCacheHeaders(cacheRes.headers);
        resolve(cacheRes);
      }
    }
  }) as Promise<Response>;
}

/** 获取csr页面资源：不含动态信息 */
export const getPageHtmlByCache = (csrPageUrl: string) => {
  return new Promise(async (resolve) => {
    const url = csrPageUrl.replace('https://', 'http://');
    const cacheRes = await cache.get(url).catch(() => null);
    if (cacheRes) {
      cacheRes.headers.set('content-type', 'text/html; charset=utf-8');
      cacheRes.headers.set('x-proxy-hit-cache', '1');
      removeErCacheHeaders(cacheRes.headers);
      resolve(cacheRes);
    }
  }) as Promise<Response>;
}

/**
 * 设置csr页面缓存
 * @param event
 * @param pageRes 页面响应
 * @param csrUrl 页面路径
 * @returns 
 */
export async function setPageHtmlCache(pageRes: Response, csrUrl: string) {
  // 未成功不缓存
  if (pageRes.status !== 200) return;
  const pageCache = pageRes.clone();
  const url = csrUrl.replace('https://', 'http://');
  // 默认1小时的缓存时间
  await setCache(url, pageCache, 60 * 5);
}

/**
 * 设置预请求缓存
 * @param pageRes
 * @param request
 * @param reqUrl 
 */
export async function setPagePrefetchCache(pageRes, request, reqUrl, sls) {
  if (pageRes.status !== 200) return;
  const cacheRes = pageRes.clone();
  // 默认30s的缓存时间
  const cacheSeconds = reqUrl.searchParams.get('_preMaxAge') || 30;
  const preCacheUrl = getCacheKey(request, reqUrl);
  const info = await setCache(preCacheUrl, cacheRes, cacheSeconds);

  if (info || reqUrl.searchParams.get('debugCache')) {
    sls.putSlsLog({
      logType: 'prefetchCsrPage',
      url: preCacheUrl,
      info,
    });
  }
}

/** 
 * 缓存标识 hitCache
 *  1 - csr源站缓存
 *  2 - csr 预请求缓存
 *  3 - ssr 预请求缓存
 * */
export function setCacheTag(resHeaders: Headers, sls: Logger) {
  const proxyCache = resHeaders.get('x-proxy-hit-cache');
  if (proxyCache) {
    sls.setExtra('hitCache', +proxyCache);
  } else if (resHeaders.get('x-er-hit-cache')) {
    sls.setExtra('hitCache', 3);
  }
}

async function setCache(url: string, cacheResp: Response, time: number) {
  cacheResp.headers.set('cache-control', `max-age=${time}`);
  cacheResp.headers.set('x-cache-time', `${Date.now()}`);
  return await cache.put(url, cacheResp).catch(err => err && err.message);
}

async function getJsonCache(url: string) {
  const cacheRes = await cache.get(url).catch(() => null);
  if (cacheRes) {
    return await cacheRes.json();
  }
  return null;
}

function getUserInfoCacheUrl(reqUrl: URL, wxUserInfo: string) {
  return `http://${reqUrl.host}/cache/wx-user-info?_wx_user_info_=${encodeURIComponent(wxUserInfo)}`;
}

/**
 * 设置用户缓存
 * @param wxUserInfo 请求参数中的微信用户信息
 * @param data 用户信息
 */
async function setUserCache(reqUrl, wxUserInfo?: string, data?: Record<string, string>) {
  const dataValid = data && data.openId && data.unionId && data.from === 'decrypt';
  if (!wxUserInfo || !dataValid) return;
  await setCache(getUserInfoCacheUrl(reqUrl, wxUserInfo), new Response(JSON.stringify(data)), 60 * 60 * 3);
}


/** 获取泰坦数据缓存key */
function getTitanCacheKey(reqUrl: URL) {
  return `http://${reqUrl.host}/cache/titan-data`;
}

/**
 * 设置泰坦数据缓存
 * @param reqUrl 
 * @param data 
 * @returns 
 */
export async function setTitanCache(reqUrl: URL, data?: Record<string, any>) {
  const cacheData = JSON.stringify(data || {});
  // 空数据不缓存
  if (cacheData === '{}') return;
  // 缓存3小时
  await setCache(getTitanCacheKey(reqUrl), new Response(JSON.stringify(data)), 60 * 60 * 3);
}

/** 设置异步数据缓存 */
export async function setAsyncDataCache(reqUrl: URL, data?: Record<string, any>, wxUserInfo?: string) {
  await Promise.all([
    setTitanCache(reqUrl, data?.titanConfig),
    setUserCache(reqUrl, wxUserInfo, data?.userInfo),
  ]);
}

/** 
 * 获取异步数据缓存
 * @param reqUrl 请求链接的url
 * @param wxUserInfo 请求参数中的微信用户信息
 * */
export async function getAsyncDataByCache(reqUrl: URL, wxUserInfo?: string) {
  const [titanConfig, userInfo] = await Promise.all([
    getJsonCache(getTitanCacheKey(reqUrl)),
    getJsonCache(getUserInfoCacheUrl(reqUrl, wxUserInfo)),
  ]);
  return {
    titanConfig,
    userInfo,
  };
}