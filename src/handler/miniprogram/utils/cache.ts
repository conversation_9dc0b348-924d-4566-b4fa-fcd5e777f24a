import { getCsrFetchUrl } from './url';
import { CSR_CACHE_QUERY } from '../constant/index';
import Logger from './logger';
import { removeErCacheHeaders } from '../../../utils';

export function getPlatform(ua: string) {
  // 微信
  if (ua.match(/MicroMessenger/i)) {
    return 'wechat';
  }
  // 抖音
  if (ua.match(/ToutiaoMicroApp/i)) {
    return 'douyin';
  }
  // 小红书
  if (ua.match(/xhsminiapp/i)) {
    return 'xiaohongshu';
  }
  return '';
}

/** 
 * 获取缓存key：异步信息跟平台、用户有关，因此要作为缓存key
 * */
function getCacheKey(request, reqUrl: URL) {
  try {
    const extraParams = new URLSearchParams(reqUrl.searchParams.toString());
    const clientIP = request.headers.get("Ali-Cdn-Real-Ip");
    // 获取APPName
    const ua = request.headers.get('user-agent') || '';
    // 获取userid
    const cookie = request.headers.get('cookie') || '';
    const matches = cookie.match(/munb=(\d+);/);
    const platform = getPlatform(ua);

    // 平台信息
    if (platform) {
      extraParams.set('platform', platform);
    }

    // 用户信息
    if (matches && matches.length > 1 && matches[1]) {
      extraParams.set('userId', matches[1]);
    } else if (!reqUrl.searchParams.get('_wx_user_info_')){
      // 优先取_wx_user_info_参数，兜底clientIP
      extraParams.set('clientIP',clientIP);
    }

    // 把特殊参数去掉
    extraParams.delete('_bx-m');
    extraParams.delete(CSR_CACHE_QUERY.DELETE_CACHE);
    extraParams.delete(CSR_CACHE_QUERY.PRE_FETCH);

    return `http://${reqUrl.host}${reqUrl.pathname}?${extraParams.toString()}`;
  } catch (e) {
    return '';
  }
}

/** 清除缓存 */
export async function deleteCache(request: Request, reqUrl: URL, realHost: string) {
  const preCacheUrl = getCacheKey(request, reqUrl);
  const res: any = {};
  // 清除源站请求缓存
  res.deletePageCache = await cache.delete(getCsrFetchUrl(reqUrl, realHost)).catch(err => err && err.message)
  if (preCacheUrl) {
    // 清除预请求缓存
    res.deletePreCache = await cache.delete(preCacheUrl).catch(err => err && err.message);
  }
  return JSON.stringify(res);
}

/** 从预请求的缓存中读取 */
export async function getPageByPrefetchCache(event: FetchEvent, reqUrl: URL, sls: Logger) {
  return new Promise(async (resolve) => {
    const { request } = event;
    const preCacheUrl = getCacheKey(request, reqUrl);
    if (preCacheUrl) {
      const cacheRes = await cache.get(preCacheUrl).catch((err) => {
        event.waitUntil(sls.putSlsLog({
          logType: 'error',
          errorType: 'getCsrPageByCacheError',
          errInfo: err,
          errMsg: err?.message || '',
        }));
        return null;
      });
      if (cacheRes) {
        sls.performanceTimeEnd('getPage');
        event.waitUntil(sls.performanceEnd('normal', { hitCache: 2 }));
        cacheRes.headers.set('content-type', 'text/html; charset=utf-8');
        cacheRes.headers.set('x-proxy-hit-cache', '1');
        removeErCacheHeaders(cacheRes.headers);
        resolve(cacheRes);
      }
    }
  }) as Promise<Response>;
}

/** 获取csr页面资源：不含动态信息 */
export const getPageHtmlByCache = (csrPageUrl: string) => {
  return new Promise(async (resolve) => {
    const url = csrPageUrl.replace('https://', 'http://');
    const cacheRes = await cache.get(url).catch(() => null);
    if (cacheRes) {
      cacheRes.headers.set('content-type', 'text/html; charset=utf-8');
      cacheRes.headers.set('x-proxy-hit-cache', '1');
      removeErCacheHeaders(cacheRes.headers);
      resolve(cacheRes);
    }
  }) as Promise<Response>;
}

/**
 * 设置csr页面缓存
 * @param event
 * @param pageRes 页面响应
 * @param csrUrl 页面路径
 * @returns 
 */
export async function setPageHtmlCache(pageRes: Response, csrUrl: string, sls) {
  // 未成功不缓存
  if (pageRes.status !== 200) return;
  const pageCache = pageRes.clone();
  const url = csrUrl.replace('https://', 'http://');
  // 默认1分钟的缓存时间
  const cacheSeconds = 60;
  pageCache.headers.set('cache-control', `max-age=${cacheSeconds}`);
  pageCache.headers.set('x-cache-time', `${Date.now()}`);
  await cache.put(url, pageCache).catch(err => err && err.message);
}

/**
 * 设置预请求缓存
 * @param pageRes
 * @param request
 * @param reqUrl 
 */
export async function setPagePrefetchCache(pageRes, request, reqUrl, sls) {
  if (pageRes.status !== 200) return;
  const cacheRes = pageRes.clone();
  // 默认30s的缓存时间
  const cacheSeconds = reqUrl.searchParams.get('_preMaxAge') || 30;
  cacheRes.headers.set('cache-control', `max-age=${cacheSeconds}`);
  cacheRes.headers.set('x-cache-time', `${Date.now()}`)
  const preCacheUrl = getCacheKey(request, reqUrl);
  const info = await cache.put(preCacheUrl, cacheRes).catch(err => err && err.message);
  return sls.putSlsLog({
    logType: 'prefetchCsrPage',
    url: preCacheUrl,
    info,
  })
}

/** 
 * 缓存标识 hitCache
 *  1 - csr源站缓存
 *  2 - csr 预请求缓存
 *  3 - ssr 预请求缓存
 * */
export function setCacheTag(resHeaders: Headers, sls: Logger) {
  if (resHeaders.get('x-proxy-hit-cache')) {
    sls.setExtra('hitCache', 1);
  } else if (resHeaders.get('x-er-hit-cache')) {
    sls.setExtra('hitCache', 3);
  }
}


/** 获取泰坦数据缓存key */
function getTitanCacheKey(reqUrl: URL) {
  return `http://${reqUrl.host}/cache/titan-data`;
}

/**
 * 设置泰坦数据缓存
 * @param reqUrl 
 * @param data 
 * @returns 
 */
export async function setTitanCache(reqUrl: URL, data?: Record<string, any>) {
  const cacheData = JSON.stringify(data || {});
  // 空数据不缓存
  if (cacheData === '{}') return;
  // 默认1小时的缓存时间
  const cacheSeconds = 60 * 60;
  const cacheResp = new Response(JSON.stringify(data));
  const cacheUrl = getTitanCacheKey(reqUrl);

  cacheResp.headers.set('cache-control', `max-age=${cacheSeconds}`);
  cacheResp.headers.set('x-cache-time', `${Date.now()}`);
  await cache.put(cacheUrl, cacheResp).catch(err => err && err.message);
}

/**
 * 获取泰坦数据缓存
 */
export async function getTitanCache(reqUrl: URL) {
  const cacheUrl = getTitanCacheKey(reqUrl);
  const cacheRes = await cache.get(cacheUrl).catch(() => null);
  if (cacheRes) {
    return await cacheRes.json();
  }
  return null;
}