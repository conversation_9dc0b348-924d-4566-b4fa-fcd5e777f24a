const HTML_CONFIG = {
  // 404
  NOT_FOUND: {
    funcCode: `function copy() {
        if (navigator.clipboard) {
          var url = window.location.href;
          navigator.clipboard.writeText(url).then(function() {
            console.log('Async: Copying to clipboard was successful!');
          }, function(err) {
            console.error('Async: Could not copy text: ', err);
          });
        }
      }`,
    imgUrl: 'https://gw.alicdn.com/imgextra/i3/O1CN01GcmLDo1ydJITFMqVN_!!6000000006601-2-tps-714-558.png',
    title: '对不起，找不到当前页面',
    desc: '',
    btnCode: '<div style="box-sizing: border-box; display: flex; flex-direction: row; flex-shrink: 0; place-content: flex-start center; border-width: 0.26667vw; border-style: solid; border-color: rgb(210, 212, 217); margin: 3.2vw 0vw 0vw; padding: 1.86667vw 4.8vw; min-width: 0vw; height: 8vw; align-items: center; border-radius: 4vw; background-color: transparent;" onclick="copy()"><span style="box-sizing: border-box; display: block; font-size: 3.73333vw; white-space: pre-wrap; color: rgb(92, 95, 102); font-weight: 500;">复制链接</span></div>'
  },
  // 服务错误
  SERVER_ERR: {
    funcCode: 'function reload() { window.location.reload(); }',
    imgUrl: 'https://gw.alicdn.com/imgextra/i2/O1CN01j4O4nW1aUYxFpQ2kV_!!6000000003333-2-tps-714-558.png',
    title: '对不起，系统繁忙',
    desc: '请刷新试试，飞猪会努力加载',
    btnCode: '<div style="box-sizing: border-box; display: flex; flex-direction: row; flex-shrink: 0; place-content: flex-start center; border-width: 0.26667vw; border-style: solid; border-color: rgb(210, 212, 217); margin: 3.2vw 0vw 0vw; padding: 1.86667vw 4.8vw; min-width: 0vw; height: 8vw; align-items: center; border-radius: 4vw; background-color: transparent;" onclick="reload()"><span style="box-sizing: border-box; display: block; font-size: 3.73333vw; white-space: pre-wrap; color: rgb(92, 95, 102); font-weight: 500;">刷新</span></div>',
  },
  // 不支持的页面
  INVALID: {
    funcCode: '',
    imgUrl: 'https://gw.alicdn.com/imgextra/i3/O1CN01GcmLDo1ydJITFMqVN_!!6000000006601-2-tps-714-558.png',
    title: '访问链接错误',
    desc: '',
    btnCode: '',
  }
}

function getErrType(status: number, invalid: boolean) {
  if (invalid) return 'INVALID';
  if (status === 404) return 'NOT_FOUND';
  if (/^5\d{2}$/.test(`${status}`)) return 'SERVER_ERR';
  return '';
}

/** 获取容灾页面html */
export function getDisasterHtml(event: FetchEvent, { status, isHtmlOrText, reqUrl, sls, invalid = false, resHeaders }) {
  const errType = getErrType(status, invalid);

  // 非页面资源或者不支持的错误类型
  if (!isHtmlOrText || !errType) return;
  const { funcCode = '', imgUrl, title, desc: _desc, btnCode } = HTML_CONFIG[errType];
  const desc = invalid ? reqUrl.toString() : _desc;

  const pageHtml = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="aplus-terminal" content="1">
    <meta name="weex-viewport" content="750">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="data-spm" content="181.SPM">
    <meta name="page-name" content="Result_demo">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no, email=no">
    <style>
      html, body {
        -ms-overflow-style: scrollbar;
        -webkit-tap-highlight-color: transparent;
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
      }

      body {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0;
        font-family: BlinkMacSystemFont, 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      }
    </style>
    <script>
      ${funcCode}
    </script>
  </head>
  <body>
    <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;box-sizing: border-box; border: none; margin: 0vw; padding: 0vw; min-width: 0vw;">
      <div style="box-sizing: border-box; display: flex; justify-content: center; flex-direction: column; align-items: center; flex-shrink: 0; align-content: flex-start; border-width: 0vw; border-style: solid; border-color: black; margin-top: -18vw; min-width: 0vw; inset: 0vw; background-color: transparent;">
        <img mode="aspectFit"
          aria-hidden="true"
          src="${imgUrl}"
          style="width: 63.4667vw; height: 49.6vw; object-fit: contain;"
        />
        <div style="box-sizing: border-box; border-width: 0vw; border-style: none; margin: 0vw; padding: 0vw; min-width: 0vw;">
          <div style="box-sizing: border-box; border-width: 0; border-style: none; margin: 0vw; padding: 0vw; min-width: 0vw; width: 63.4667vw;">
            <span style="box-sizing: border-box; display: block; font-size: 4vw; white-space: pre-wrap; margin-top: 4vw; color: rgb(15, 19, 26); font-weight: 500; line-height: 4.53333vw; text-align: center; letter-spacing: 0vw;">${title}</span>
          </div>
          <div style="box-sizing: border-box; border-width: 0; border-style: solid; margin: 1.6vw 0vw 0vw; padding: 0vw; min-width: 0vw;"><span style="box-sizing: border-box; display: block; font-size: 3.2vw; white-space: pre-wrap; color: rgb(92, 95, 102); line-height: 3.73333vw; text-align: center; letter-spacing: 0vw;">${desc}</span></div>
        </div>
       ${btnCode}
      </div>
    </div>
  </body>
</html>`;
  // 发送埋点
  event.waitUntil(sls.putSlsLog({
    logType: 'pageDisasterError',
    errorType: status,
    invalid,
  }));
  return new Response(pageHtml, {
    headers: resHeaders,
    status: 200
  });
}