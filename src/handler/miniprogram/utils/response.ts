import { getDisasterHtml } from './disaster';
import { getScriptStr } from './code';
import { mergeHeaderCookies, addAccessHeaders } from './headers';
import Logger from './logger';

interface IAsyncData {
  success: boolean;
  msg?: string;
  scriptList?: string[];
  headers?: Headers;
}

/** 
 * 获取扩展的脚本 
 * @param reqHeaders 请求头，用于获取ua、cookie等信息
 * @param params 异步数据、性能数据等
 * */
function getExtraScript(reqHeaders: Headers, params) {
  const { asyncData, startTimestamp, webviewLoadTime, pageUrl } = params;
  const asyncScriptList = asyncData?.scriptList || [];
  return getScriptStr(reqHeaders, asyncScriptList, { webviewLoadTime, beginTime: startTimestamp, pageUrl });
}

/** 
 * 处理response
 * @param event fetch事件
 * @param pageResp 页面响应
 * @param sls 日志对象
 *  */
function formatRespones(event: FetchEvent, pageResp: Response, sls: Logger) {
  const { request } = event;
  const pageUrl = request.url;
  const reqUrl = new URL(pageUrl);
  const { status } = pageResp;
  const contentType = pageResp.headers?.get('content-type') || '';
  const isHtmlOrText = contentType.includes('text/html') || contentType.includes('text/plain');
  const isDEFGray = `/${reqUrl.pathname.split('/')[1]}` === '/.well-known' // DEF强制灰度页面
  
  // 追加跨域头
  addAccessHeaders(pageResp.headers, reqUrl.host);
  
  // 请求不是正常返回或者非页面资源或者是DEF强制灰度时，直接返回
  if (status !== 200 || !isHtmlOrText || isDEFGray) {
    return {
      disaterResponse: getDisasterHtml(event, { status, isHtmlOrText, reqUrl, sls, resHeaders: pageResp.headers }) || pageResp,
    };
  }

  // 正常请求
  const webviewLoadTime = reqUrl.searchParams.get('webviewLoadTime');
  const { startTimestamp } = sls.getTime();
  return {
    disaterResponse: null,
    webviewLoadTime,
    startTimestamp,
    pageUrl,
    reqHost: reqUrl.host,
    headers: request.headers,
  }
}

/**
 * 处理流式响应
 * @param event 
 * @param pageResp 
 * @param asyncPromise 
 * @param sls 
 * @returns 
 */
export async function handleStreamResponse(event: FetchEvent, pageResp: Response, asyncPromise: Promise<IAsyncData>, sls: Logger) {
  const { disaterResponse, webviewLoadTime, startTimestamp, pageUrl, headers } = formatRespones(event, pageResp, sls);
  // 异常返回
  if (disaterResponse) {
    return disaterResponse;
  }
  try {
    const reader = pageResp.clone().body.getReader();
    const decoder = new TextDecoder();
    const encoder = new TextEncoder();
    const tag = `<script data-from="server"`; // 取body的标识，让response更快
    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();
    const handleHtml = async () => {
    try {
      while (true) {
        const { done, value } = await reader.read(); 
        if (done) break;

        if (value) {
          // 解码当前块
          const accumulatedHtml = decoder.decode(value, { stream: true });
          const index = accumulatedHtml.indexOf(tag);
          // 检查是否包含tag
          if (index > -1) {
            const asyncData = await asyncPromise;
            const extraScript = getExtraScript(headers, { asyncData, webviewLoadTime, startTimestamp, pageUrl });
            const finalHtml = accumulatedHtml.slice(0, index) + extraScript + accumulatedHtml.slice(index)
            await writer.write(encoder.encode(finalHtml));
            sls.performanceTimeEnd('spliceHtmlInStream');
            // 继续处理剩余流
            while (true) {
              const { done: nextDone, value: nextValue } = await reader.read();
              if (nextDone) break;
              if (nextValue) await writer.write(nextValue);
            }
            break;
          } else {
            await writer.write(value);
          }
        }
      }
      } catch (err) {
        event.waitUntil(sls.putSlsLog({ 
          logType: 'error', 
          errorType: 'handleHtmlError', 
          errInfo: err,
          errMsg: typeof err === 'object' && err !== null ? (err.message || String(err)) : String(err)
        }));
      } finally {
        event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime, isStream: true }))
        reader.releaseLock();
        await writer.close();
      }
    }
    event.waitUntil(handleHtml());
    sls.performanceTime('spliceHtmlInStream');
    sls.performanceTimeEnd('firstScreen');
    return new Response(readable, {
      headers: pageResp.headers,
      status: 200
    });
  } catch (err) {
    event.waitUntil(sls.putSlsLog({
      logType: 'error',
      isSuccess: false,
      errorType: 'mergeWebviewInfoError',
      errInfo: err,
      errMsg: err?.message || '',
    }));
  }
  return pageResp; 
}

/**
 * 处理页面返回结果
 * - 容灾处理：404、500、502场景
 * - 静态数据设置：各端jssdk、登录态、性能数据等
 * - 异步数据设置：微信config、泰坦配置、小程序用户信息（openId、unionId）等
 */
export async function handleMiniProgramResponse(event: FetchEvent, pageResp: Response, asyncData: IAsyncData, sls: Logger) {
  const { disaterResponse, webviewLoadTime, startTimestamp, headers, pageUrl, reqHost } = formatRespones(event, pageResp, sls);
  // 异常返回
  if (disaterResponse) {
    return disaterResponse;
  }

  try {
    // 普通响应
    sls.performanceTime('responseText');
    const pageHtml = await pageResp.clone().text();
    sls.performanceTimeEnd('responseText');
    const headIndex = pageHtml.indexOf('</head>');

    // 不是html结构，直接返回
    if (headIndex === -1) {
      // 上报日志
      event.waitUntil(sls.performanceEnd('notHtml', { webviewLoadTime }));
      return pageResp;
    }
    // 组装html
    const extraScript = getExtraScript(headers, { asyncData, webviewLoadTime, startTimestamp, pageUrl });
    const finalHtml = pageHtml.slice(0, headIndex) + extraScript + pageHtml.slice(headIndex);
    // 合并cookie
    mergeHeaderCookies(pageResp.headers, asyncData, reqHost);
    // 上报日志
    event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime }));
    // 返回
    return new Response(finalHtml, {
      headers: pageResp.headers,
      status: 200
    });
  } catch (err) {
    event.waitUntil(sls.putSlsLog({
      logType: 'error',
      isSuccess: false,
      errorType: 'mergeWebviewInfoError',
      errInfo: err,
      errMsg: err?.msg || err?.message || '',
    }));
  }
  return pageResp;
}
