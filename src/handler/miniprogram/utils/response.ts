import { getDisasterHtml } from './disaster';
import { getScriptStr, getStaticScript } from './code';
import { mergeHeaderCookies, addAccessHeaders } from './headers';
import { removeAtomCookie } from './atom';
import Logger from './logger';

interface IAsyncData {
  success: boolean;
  msg?: string;
  scriptList?: string[];
  headers?: Headers;
  userInfo?: {
    openId?: string;
    unionId?: string;
    userId?: string;
  }
}

/** 
 * 获取扩展的脚本 
 * @param reqHeaders 请求头，用于获取ua、cookie等信息
 * @param params 异步数据、性能数据等
 * */
function getExtraScript(reqHeaders: Headers, params) {
  const { asyncData, startTimestamp, ...other } = params;
  const asyncScriptList = asyncData?.scriptList || [];
  return getScriptStr(reqHeaders, asyncScriptList, { ...other, beginTime: startTimestamp, });
}

/** 
 * 处理response
 * @param event fetch事件
 * @param pageResp 页面响应
 * @param sls 日志对象
 *  */
function formatRespones(event: FetchEvent, pageResp: Response, sls: Logger) {
  const { request } = event;
  const pageUrl = request.url;
  const reqUrl = new URL(pageUrl);
  const { status } = pageResp;
  const contentType = pageResp.headers?.get('content-type') || '';
  const isHtmlOrText = contentType.includes('text/html') || contentType.includes('text/plain');
  const isDEFGray = `/${reqUrl.pathname.split('/')[1]}` === '/.well-known' // DEF强制灰度页面
  
  // 追加跨域头
  addAccessHeaders(pageResp.headers, reqUrl.host);
  // 清理atom cookie
  removeAtomCookie(reqUrl, request.headers, pageResp.headers);
  
  // 请求不是正常返回或者非页面资源或者是DEF强制灰度时，直接返回
  if (status !== 200 || !isHtmlOrText || isDEFGray) {
    return {
      disaterResponse: getDisasterHtml(event, { status, isHtmlOrText, reqUrl, sls, resHeaders: pageResp.headers }) || pageResp,
    };
  }

  // 正常请求
  const webviewLoadTime = reqUrl.searchParams.get('webviewLoadTime');
  const { startTimestamp } = sls.getTime();
  return {
    disaterResponse: null,
    webviewLoadTime,
    startTimestamp,
    pageUrl,
    reqHost: reqUrl.host,
    headers: request.headers,
  }
}

/** 通过HtmlStream插入脚本 */
async function getHtmlStream(
  event: FetchEvent,
  pageResp: Response,
  headers: Headers,
  asyncPromise: Promise<IAsyncData>,
  extParams: { webviewLoadTime: string, startTimestamp: string; pageUrl: string; reqHost: string },
  sls: Logger
) {
  const asyncData = await asyncPromise;
  const { reqHost, ...params } = extParams;
  // 创建 HTMLStream 实例，用于在指定位置插入脚本
  sls.setExtra('useHtmlStream', true);
  // 合并cookie
  mergeHeaderCookies(pageResp.headers, asyncData, reqHost);
  return new HTMLStream(pageResp.body, [
    [
      'script[data-from="server"]', // 使用 CSS 选择器匹配多个可能的插入点
      {
        element(element) {
          // 使用 HTMLStream 处理 HTML 内容插入
          try {
            const extraScript = getExtraScript(headers, { ...params, asyncData });
            // 在匹配的元素前插入额外的脚本
            element.before(extraScript, { html: true });
            sls.performanceTimeEnd('spliceHtmlInStream');
            event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime: params.webviewLoadTime, userInfo: asyncData?.userInfo }));
          } catch (e) {
            event.waitUntil(sls.putSlsLog({ logType: 'error', errorType: 'getHtmlStreamError', errMsg: e?.message || '' }));
          }
        }
      }
    ]
  ]);
}

/** 获取流式插入静态脚本后的html */
const getHtmlWithStaticData = (reqHeaders: Headers, _html: string, hasStaticData: boolean, sls: Logger) => {
  if (!hasStaticData && _html.indexOf('</head>') > -1) {
    hasStaticData = true;
    sls.performanceTimeEnd('setStaticScript');
    return _html.replace('</head>', getStaticScript(reqHeaders) + '</head>');
  }
  return _html;
}

/** 获取流式插入脚本后的html */
const getStreamFinalHtml = async (asyncPromise:Promise<IAsyncData>, headers: Headers, params: { webviewLoadTime: string, startTimestamp: string; pageUrl: string, onlyAsync?: boolean }, _html: string) => {
    // 最后一屏标识
  const lastPartTag = `<script data-id="last-part-success"`;
  const tag = `<script data-from="server"`; // 取body的标识，让response更快
  const lastPartIndex = _html.indexOf(lastPartTag);
  const serverTagIndex = _html.indexOf(tag);
  // 优先取最后一屏标识
  const index = lastPartIndex > -1 ? lastPartIndex : serverTagIndex;
  if (index > -1) {
    // 找到标识，插入脚本
    const asyncData = await asyncPromise;
    const extraScript = getExtraScript(headers, { asyncData, ...params });
    return {
      userInfo: asyncData?.userInfo,
      finalHtml: _html.slice(0, index) + extraScript + _html.slice(index),
      inserted: true,
    };
  }
  return {
    finalHtml: _html,
    inserted: false,
  };
};

function test() {
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();
  writer.write(new TextEncoder().encode('<html><head></head><body>'));
  setTimeout(() => {
    writer.write(new TextEncoder().encode('<div>1234</div></body></html>'));
  }, 50)

  return new Response(readable, {
    headers: {
      'content-type': 'text/html; charset=utf-8',
    },
    status: 200,
  });
}

async function handleTest() {
  const reader = test().clone().body.getReader();
  const decoder = new TextDecoder();
  const encoder = new TextEncoder();
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();

  // 方法1：读取完整内容
  let fullContent = '';
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      if (value) {
        const chunk = decoder.decode(value, { stream: true });
        fullContent += chunk;
        console.log('收到数据块:', chunk);
      }
    }

    console.log('完整内容:', fullContent);

    // 处理完整内容后写入新流
    await writer.write(encoder.encode(fullContent));
    await writer.close();

  } catch (err) {
    console.error('读取流出错:', err);
  } finally {
    reader.releaseLock();
  }

  return new Response(readable, {
    headers: { 'content-type': 'text/html; charset=utf-8' },
    status: 200,
  });
}

// 方法2：流式转发（边读边写，不等待完整内容）
async function handleTestStream() {
  const reader = test().clone().body.getReader();
  const decoder = new TextDecoder();
  const encoder = new TextEncoder();
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();

  // 异步处理流转发
  const processStream = async () => {
    let buffer = '';
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          // 流结束，处理剩余缓冲区内容
          if (buffer) {
            console.log('最终缓冲区内容:', buffer);
            await writer.write(encoder.encode(buffer));
          }
          break;
        }

        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          console.log('收到数据块:', chunk);
          console.log('当前缓冲区:', buffer);

          // 可以在这里添加处理逻辑，比如查找特定标识
          // 然后决定何时写入数据
          await writer.write(value); // 直接转发原始数据
        }
      }
    } catch (err) {
      console.error('流处理出错:', err);
    } finally {
      reader.releaseLock();
      await writer.close();
    }
  };

  // 启动异步处理
  processStream();

  return new Response(readable, {
    headers: { 'content-type': 'text/html; charset=utf-8' },
    status: 200,
  });
}

// 方法3：使用 Response.text() 简化读取（但会等待完整内容）
async function handleTestSimple() {
  const response = test();

  // 直接读取完整文本内容
  const fullContent = await response.text();
  console.log('完整内容:', fullContent);

  // 处理内容（比如插入脚本）
  const processedContent = fullContent.replace('</head>', '<script>console.log("inserted")</script></head>');

  return new Response(processedContent, {
    headers: { 'content-type': 'text/html; charset=utf-8' },
    status: 200,
  });
}

// 方法4：等待所有数据后再处理（解决异步问题）
async function handleTestWaitAll() {
  const reader = test().clone().body.getReader();
  const decoder = new TextDecoder();
  const chunks = [];

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      if (value) {
        chunks.push(value);
        console.log('收到数据块，大小:', value.length);
      }
    }

    // 合并所有数据块
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const combined = new Uint8Array(totalLength);
    let offset = 0;

    for (const chunk of chunks) {
      combined.set(chunk, offset);
      offset += chunk.length;
    }

    // 解码完整内容
    const fullContent = decoder.decode(combined);
    console.log('完整内容:', fullContent);

    return new Response(fullContent, {
      headers: { 'content-type': 'text/html; charset=utf-8' },
      status: 200,
    });

  } finally {
    reader.releaseLock();
  }
}

/** 重写流，插入脚本 */
function getRewriteStream(
  event: FetchEvent,
  pageResp: Response,
  asyncPromise: Promise<IAsyncData>,
  headers: Headers,
  params: { webviewLoadTime: string, startTimestamp: string; pageUrl: string },
  sls: Logger
) {
  const reader = pageResp.clone().body.getReader();
  const decoder = new TextDecoder();
  const encoder = new TextEncoder();
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();

  const handleHtml = async () => {
    let hasStaticData = false;
    let buffer = ''; // 缓冲区，用于处理跨块的标识
    const maxBufferSize = 160; // 缓冲区最大长度

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          // 处理最后的缓冲区内容
          if (buffer) {
            const html = getHtmlWithStaticData(headers, buffer, hasStaticData, sls)
            const asyncParams = { ...params, onlyAsync: hasStaticData };
            const { finalHtml, inserted, userInfo } = await getStreamFinalHtml(asyncPromise, headers, asyncParams, html);
            await writer.write(encoder.encode(finalHtml));
            if (inserted) {
              hasStaticData = true;
              sls.setUserInfo(userInfo);
              sls.setExtra('lastInsert', true);
              sls.performanceTimeEnd('spliceHtmlInStream');
            }
          }
          break;
        }

        if (value) {
          // 解码当前块并添加到缓冲区
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          buffer = getHtmlWithStaticData(headers, buffer, hasStaticData, sls);
          // 如果已经插入了静态脚本，异步数据就不返回静态数据了
          const { finalHtml, inserted, userInfo } = await getStreamFinalHtml(asyncPromise, headers, { ...params, onlyAsync: !!hasStaticData }, buffer);
          // 找到标识，插入脚本
          if (inserted) {
            hasStaticData = true;
            await writer.write(encoder.encode(finalHtml));
            sls.setUserInfo(userInfo);
            sls.performanceTimeEnd('spliceHtmlInStream');
            buffer = ''; // 清空缓冲区
            // 继续处理剩余流，不再缓冲
            while (true) {
              const { done: nextDone, value: nextValue } = await reader.read();
              if (nextDone) break;
              if (nextValue) await writer.write(nextValue);
            }
            break;
          } else {
            // 未找到标识，写入部分缓冲区内容，保留可能被切割的部分
            if (buffer.length > maxBufferSize) {
              const writeLength = buffer.length - maxBufferSize;
              const toWrite = buffer.slice(0, writeLength);
              await writer.write(encoder.encode(toWrite));
              buffer = buffer.slice(writeLength);
            }
          }
        }
      }
    } catch (err) {
      event.waitUntil(sls.putSlsLog({ logType: 'error', errorType: 'handleHtmlError', errMsg: err?.message || '' }));
    } finally {
      event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime: params.webviewLoadTime }))
      reader.releaseLock();
      await writer.close();
    }
  }
  sls.performanceTime('spliceHtmlInStream');
  event.waitUntil(handleHtml());
  return readable;
}

/**
 * 处理流式响应
 * @param event 
 * @param pageResp 
 * @param asyncPromise 
 * @param sls 
 * @returns 
 */
export async function handleStreamResponse(event: FetchEvent, pageResp: Response, asyncPromise: Promise<IAsyncData>, sls: Logger, asycResped?: boolean) {
  const { disaterResponse, webviewLoadTime, startTimestamp, pageUrl, headers, reqHost } = formatRespones(event, pageResp, sls);
  // 异常返回
  if (disaterResponse) {
    return disaterResponse;
  }
  try {
    sls.performanceTime('spliceHtmlInStream');
    sls.setExtra('isStream', true);
    // 提前获取到了异步数据，就用更高性能的方式处理脚本插入
    const extraParams = { webviewLoadTime, startTimestamp, pageUrl };
    const responseBody = asycResped ?
      await getHtmlStream(event, pageResp, headers, asyncPromise, { ...extraParams, reqHost}, sls) :
      getRewriteStream(event, pageResp, asyncPromise, headers, extraParams, sls);

    sls.performanceTimeEnd('firstScreen');
    // 返回使用 HTMLStream 处理后的响应
    return new Response(responseBody as any, {
      headers: pageResp.headers,
      status: 200
    });
  } catch (err) {
    event.waitUntil(sls.putSlsLog({
      logType: 'error',
      isSuccess: false,
      errorType: 'mergeWebviewInfoError',
      errMsg: err?.message || '',
    }));
  }
  return pageResp; 
}

/**
 * 处理页面返回结果
 * - 容灾处理：404、500、502场景
 * - 静态数据设置：各端jssdk、登录态、性能数据等
 * - 异步数据设置：微信config、泰坦配置、小程序用户信息（openId、unionId）等
 */
export async function handleMiniProgramResponse(event: FetchEvent, pageResp: Response, asyncData: IAsyncData, sls: Logger) {
  const { disaterResponse, webviewLoadTime, startTimestamp, headers, pageUrl, reqHost } = formatRespones(event, pageResp, sls);
  // 异常返回
  if (disaterResponse) {
    return disaterResponse;
  }

  try {
    // 普通响应
    sls.performanceTime('responseText');
    const pageHtml = await pageResp.clone().text();
    sls.performanceTimeEnd('responseText');
    const headIndex = pageHtml.indexOf('</head>');

    // 不是html结构，直接返回
    if (headIndex === -1) {
      // 上报日志
      event.waitUntil(sls.performanceEnd('notHtml', { webviewLoadTime }));
      return pageResp;
    }
    // 组装html
    const extraScript = getExtraScript(headers, { asyncData, webviewLoadTime, startTimestamp, pageUrl });
    const finalHtml = pageHtml.slice(0, headIndex) + extraScript + pageHtml.slice(headIndex);
    // 合并cookie
    mergeHeaderCookies(pageResp.headers, asyncData, reqHost);
    // 上报日志
    event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime, userInfo: asyncData?.userInfo }));
    // 返回
    return new Response(finalHtml, {
      headers: pageResp.headers,
      status: 200
    });
  } catch (err) {
    event.waitUntil(sls.putSlsLog({
      logType: 'error',
      isSuccess: false,
      errorType: 'mergeWebviewInfoError',
      errInfo: err,
      errMsg: err?.msg || err?.message || '',
    }));
  }
  return pageResp;
}
