import { getDisasterHtml } from './disaster';
import { getScriptStr } from './code';
import { mergeHeaderCookies, addAccessHeaders } from './headers';
import { removeAtomCookie } from './atom';
import Logger from './logger';

interface IAsyncData {
  success: boolean;
  msg?: string;
  scriptList?: string[];
  headers?: Headers;
}

/** 
 * 获取扩展的脚本 
 * @param reqHeaders 请求头，用于获取ua、cookie等信息
 * @param params 异步数据、性能数据等
 * */
function getExtraScript(reqHeaders: Headers, params) {
  const { asyncData, startTimestamp, webviewLoadTime, pageUrl } = params;
  const asyncScriptList = asyncData?.scriptList || [];
  return getScriptStr(reqHeaders, asyncScriptList, { webviewLoadTime, beginTime: startTimestamp, pageUrl });
}

/** 
 * 处理response
 * @param event fetch事件
 * @param pageResp 页面响应
 * @param sls 日志对象
 *  */
function formatRespones(event: FetchEvent, pageResp: Response, sls: Logger) {
  const { request } = event;
  const pageUrl = request.url;
  const reqUrl = new URL(pageUrl);
  const { status } = pageResp;
  const contentType = pageResp.headers?.get('content-type') || '';
  const isHtmlOrText = contentType.includes('text/html') || contentType.includes('text/plain');
  const isDEFGray = `/${reqUrl.pathname.split('/')[1]}` === '/.well-known' // DEF强制灰度页面
  
  // 追加跨域头
  addAccessHeaders(pageResp.headers, reqUrl.host);
  // 清理atom cookie
  removeAtomCookie(reqUrl, request.headers, pageResp.headers);
  
  // 请求不是正常返回或者非页面资源或者是DEF强制灰度时，直接返回
  if (status !== 200 || !isHtmlOrText || isDEFGray) {
    return {
      disaterResponse: getDisasterHtml(event, { status, isHtmlOrText, reqUrl, sls, resHeaders: pageResp.headers }) || pageResp,
    };
  }

  // 正常请求
  const webviewLoadTime = reqUrl.searchParams.get('webviewLoadTime');
  const { startTimestamp } = sls.getTime();
  return {
    disaterResponse: null,
    webviewLoadTime,
    startTimestamp,
    pageUrl,
    reqHost: reqUrl.host,
    headers: request.headers,
  }
}

/** 通过HtmlStream插入脚本 */
async function getHtmlStream(
  event: FetchEvent,
  pageResp: Response,
  headers: Headers,
  asyncPromise: Promise<IAsyncData>,
  extParams: { webviewLoadTime: string, startTimestamp: string; pageUrl: string; reqHost: string },
  sls: Logger
) {
  const asyncData = await asyncPromise;
  const { reqHost, ...params } = extParams;
  // 创建 HTMLStream 实例，用于在指定位置插入脚本
  sls.setExtra('useHtmlStream', true);
  // 合并cookie
  mergeHeaderCookies(pageResp.headers, asyncData, reqHost);
  return new HTMLStream(pageResp.body, [
    [
      'script[data-from="server"]', // 使用 CSS 选择器匹配多个可能的插入点
      {
        element(element) {
          // 使用 HTMLStream 处理 HTML 内容插入
          try {
            const extraScript = getExtraScript(headers, { ...params, asyncData });
            // 在匹配的元素前插入额外的脚本
            element.before(extraScript, { html: true });
            sls.performanceTimeEnd('spliceHtmlInStream');
            event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime: params.webviewLoadTime }))
          } catch (e) {
            event.waitUntil(sls.putSlsLog({ logType: 'error', errorType: 'getHtmlStreamError', errMsg: e?.message || '' }));
          }
        }
      }
    ]
  ]);
}

/** 重写流，插入脚本 */
function getRewriteStream(
  event: FetchEvent,
  pageResp: Response,
  asyncPromise: Promise<IAsyncData>,
  headers: Headers,
  params: { webviewLoadTime: string, startTimestamp: string; pageUrl: string },
  sls: Logger
) {
  const reader = pageResp.clone().body.getReader();
  const decoder = new TextDecoder();
  const encoder = new TextEncoder();
  // 最后一屏标识
  const lastPartTag = `<script data-id="last-part-success"`;
  const tag = `<script data-from="server"`; // 取body的标识，让response更快
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();
  const handleHtml = async () => {
    try {
      while (true) {
        const { done, value } = await reader.read(); 
        if (done) break;

        if (value) {
          // 解码当前块
          const accumulatedHtml = decoder.decode(value, { stream: true });
          const lastPartIndex = accumulatedHtml.indexOf(lastPartTag);
          // 优先取最后一屏标识
          const index = lastPartIndex > -1 ? lastPartIndex : accumulatedHtml.indexOf(tag);
          // 检查是否包含tag
          if (index > -1) {
            const asyncData = await asyncPromise;
            const extraScript = getExtraScript(headers, { asyncData, ...params });
            const finalHtml = accumulatedHtml.slice(0, index) + extraScript + accumulatedHtml.slice(index)
            await writer.write(encoder.encode(finalHtml));
            sls.performanceTimeEnd('spliceHtmlInStream');
            // 继续处理剩余流
            while (true) {
              const { done: nextDone, value: nextValue } = await reader.read();
              if (nextDone) break;
              if (nextValue) await writer.write(nextValue);
            }
            break;
          } else {
            await writer.write(value);
          }
        }
      }
    } catch (err) {
      event.waitUntil(sls.putSlsLog({ logType: 'error', errorType: 'handleHtmlError', errMsg: err?.message || '' }));
    } finally {
      event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime: params.webviewLoadTime }))
      reader.releaseLock();
      await writer.close();
    }
  }
  sls.performanceTime('spliceHtmlInStream');
  event.waitUntil(handleHtml());
  return readable;
}

/**
 * 处理流式响应
 * @param event 
 * @param pageResp 
 * @param asyncPromise 
 * @param sls 
 * @returns 
 */
export async function handleStreamResponse(event: FetchEvent, pageResp: Response, asyncPromise: Promise<IAsyncData>, sls: Logger, asycResped?: boolean) {
  const { disaterResponse, webviewLoadTime, startTimestamp, pageUrl, headers, reqHost } = formatRespones(event, pageResp, sls);
  // 异常返回
  if (disaterResponse) {
    return disaterResponse;
  }
  try {
    sls.performanceTime('spliceHtmlInStream');
    sls.setExtra('isStream', true);
    // 提前获取到了异步数据，就用更高性能的方式处理脚本插入
    const extraParams = { webviewLoadTime, startTimestamp, pageUrl };
    const responseBody = asycResped ?
      await getHtmlStream(event, pageResp, headers, asyncPromise, { ...extraParams, reqHost}, sls) :
      getRewriteStream(event, pageResp, asyncPromise, headers, extraParams, sls);

    sls.performanceTimeEnd('firstScreen');
    // 返回使用 HTMLStream 处理后的响应
    return new Response(responseBody as any, {
      headers: pageResp.headers,
      status: 200
    });
  } catch (err) {
    event.waitUntil(sls.putSlsLog({
      logType: 'error',
      isSuccess: false,
      errorType: 'mergeWebviewInfoError',
      errMsg: err?.message || '',
    }));
  }
  return pageResp; 
}

/**
 * 处理页面返回结果
 * - 容灾处理：404、500、502场景
 * - 静态数据设置：各端jssdk、登录态、性能数据等
 * - 异步数据设置：微信config、泰坦配置、小程序用户信息（openId、unionId）等
 */
export async function handleMiniProgramResponse(event: FetchEvent, pageResp: Response, asyncData: IAsyncData, sls: Logger) {
  const { disaterResponse, webviewLoadTime, startTimestamp, headers, pageUrl, reqHost } = formatRespones(event, pageResp, sls);
  // 异常返回
  if (disaterResponse) {
    return disaterResponse;
  }

  try {
    // 普通响应
    sls.performanceTime('responseText');
    const pageHtml = await pageResp.clone().text();
    sls.performanceTimeEnd('responseText');
    const headIndex = pageHtml.indexOf('</head>');

    // 不是html结构，直接返回
    if (headIndex === -1) {
      // 上报日志
      event.waitUntil(sls.performanceEnd('notHtml', { webviewLoadTime }));
      return pageResp;
    }
    // 组装html
    const extraScript = getExtraScript(headers, { asyncData, webviewLoadTime, startTimestamp, pageUrl });
    const finalHtml = pageHtml.slice(0, headIndex) + extraScript + pageHtml.slice(headIndex);
    // 合并cookie
    mergeHeaderCookies(pageResp.headers, asyncData, reqHost);
    // 上报日志
    event.waitUntil(sls.performanceEnd('normal', { webviewLoadTime }));
    // 返回
    return new Response(finalHtml, {
      headers: pageResp.headers,
      status: 200
    });
  } catch (err) {
    event.waitUntil(sls.putSlsLog({
      logType: 'error',
      isSuccess: false,
      errorType: 'mergeWebviewInfoError',
      errInfo: err,
      errMsg: err?.msg || err?.message || '',
    }));
  }
  return pageResp;
}
