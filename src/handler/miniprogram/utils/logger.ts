import { sendLog } from './api';

interface IContext {
  headers: Headers;
  reqUrl: URL;
  isSSR: boolean;
  beginTime?: number;
}

export default class Logger {
  temp: any = {};
  kv: any = {};
  reqUrl: URL;
  headers: Headers;
  isSSR: boolean;
  startTimestamp;
  extra: any = {};

  constructor(context: IContext) {
    this.headers = context.headers;
    this.reqUrl = context.reqUrl;
    this.isSSR = context.isSSR;
    this.startTimestamp = context.beginTime || Date.now();
  }

  performanceTime(key: string) {
    this.temp[key] = Date.now();
  }

  /** 单个片段计时结束 */
  performanceTimeEnd(key: string) {
    const startTime = this.temp[key] || this.startTimestamp;
    this.kv[key] = Date.now() - startTime;
  }

  /** 用户信息 */
  setUserInfo(userInfo?: Record<string, any>) {
    const { openId, unionId } = userInfo || {};
    if (openId) {
      this.extra.openId = openId;
    }
    if (unionId) {
      this.extra.unionId = unionId;
    }
  }

  /** 扩展信息 */
  setExtra(key: string, value: any) {
    this.extra[key] = value;
  }

  putSlsLog(data) {
    const { host, pathname, search } = this.reqUrl;
    const params = {
      host,
      path: pathname,
      queryStr: search,
      isSSR: this.isSSR,
      ...this.extra,
      ...data,
    };
    return sendLog(host, this.headers, params);
  }

  /** 整体结束 */
  performanceEnd(endIn: string, extra: Record<string, any> = {}) {
    const endTimestamp = Date.now();
    this.kv.overallTime = endTimestamp - this.startTimestamp;
    this.kv.startTimestamp = this.startTimestamp;
    this.kv.endTimestamp = endTimestamp;

    // 如果携带了套壳容器加载时间，则计算容器加载+代理服务耗时
    const { webviewLoadTime, ...other } = extra || {};
    const loadTime = this.extra.webviewLoadTime || webviewLoadTime;
    const { search } = this.reqUrl;
    // 区分一下预请求场景
    const logType = search.includes('_precsr') ? 'performance_precsr' : 'performance';
    if (loadTime) {
      this.kv.webviewLoadDuration = endTimestamp - loadTime;
    }
    return this.putSlsLog({
      logType,
      endIn,
      ...(other || {}),
      ...this.kv,
    });
  }

  getTime() {
    return {
      ...this.kv,
      startTimestamp: this.startTimestamp,
    };
  }
}