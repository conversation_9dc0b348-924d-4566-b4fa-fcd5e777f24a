import { setPageHtmlCache, getPageHtmlByCache, getAsyncDataByCache, setAsyncDataCache } from './cache';
import { removeResHeaders, getAppEnv } from './headers';
import { APP_ENV, CSR_CACHE_QUERY } from '../constant/index';
import Logger from './logger';
import { getRealPageUrl } from './url';
import { NEW_LOCATION_PAGES } from '../constant/page';
import { getScriptByAsyncData, getUserInfoFromCookie } from './code';

interface IOption {
  method?: 'POST' | 'GET',
  headers: Record<string, any>,
  body?: string,
  mode?: 'cors',
}

interface IMiniprogramResp {
  success: boolean;
  scriptList?: string[];
  msg?: string;
  headers?: Headers;
  userInfo?: {
    openId?: string;
    unionId?: string;
    userId?: string;
  }
}

export function getRequestHost(host: string) {
  return host.replace('proxy-er', 'proxy')
}

/** 包含超时时间的请求 */
export function requestWithTimeout(url: string, options: IOption, timeout?: number): Promise<Response> {
  const isPre = url.indexOf('pre-') > -1 || url.indexOf('.wapa.') > -1;
  // 预发20s，线上10s
  const timeoutDuration = isPre ? 20000 : 5000;
  return Promise.race([
    fetch(url, options),
    new Promise((_, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Request timed out'));
        clearTimeout(timer);
      }, timeout || timeoutDuration);
    }) as Promise<Response>,
  ]);
}


/** 获取套壳异步数据数据 */
export async function getMiniProgramData(event: FetchEvent, reqUrl: URL, pageUrl: string, headers: Headers): Promise<IMiniprogramResp> {
  const appEnv = getAppEnv(headers);
  if (appEnv !== APP_ENV.WEIXIN && appEnv !== APP_ENV.XIAOHONGSHU) {
    return {
      success: true,
      scriptList: [],
    };
  }
  // 无需获取wx.config的场景
  if (NEW_LOCATION_PAGES.includes(reqUrl.pathname)) {
    const wxUserInfo = reqUrl.searchParams.get('_wx_user_info_');
    // 读cookie
    const cookieUserInfo = getUserInfoFromCookie(headers);
    // 读缓存
    const { titanConfig, userInfo: cacheUserInfo } = await getAsyncDataByCache(reqUrl, wxUserInfo);
    const userInfo = { ...cookieUserInfo, ...(cacheUserInfo || {}) };

    // 从缓存中未读取到数据，发起异步请求
    if (
      !titanConfig || // 没有泰坦数据
      (userInfo.userId && !userInfo.openId) || // 有userId，没openId
      (wxUserInfo && !userInfo.openId) || // 有加密信息，没openId
      reqUrl.searchParams.get(CSR_CACHE_QUERY.PRE_FETCH) // 预请求
    ) {
      const asyncRes = await fetchAsyncData(event, reqUrl, headers);
      return {
        success: true,
        scriptList: getScriptByAsyncData(asyncRes, reqUrl, headers),
        userInfo: asyncRes?.userInfo,
      };
    }
    // 返回缓存中读取到数据，同时更新下缓存
    event.waitUntil(fetchAsyncData(event, reqUrl, headers));
    return {
      success: true,
      userInfo: cacheUserInfo, // 从_wx_user_info_参数拿到的，可信
      scriptList: getScriptByAsyncData({ userInfo, titanConfig }, reqUrl, headers),
    };
  }
  // 老链路
  return fetchAsyncScriptData(reqUrl.host, pageUrl, headers);
}

/** 获取页面数据 */
export async function fetchPageData(event: FetchEvent, csrPageUrl: string, sls: Logger): Promise<Response> {
  const headers = event.request.headers;

  return Promise.race([
    getPageHtmlByCache(csrPageUrl),
    requestWithTimeout(csrPageUrl, { headers })
      .then(res => {
        removeResHeaders(res.headers);
        event.waitUntil(setPageHtmlCache(res, csrPageUrl));
        return res;
      }).catch(err => {
        event.waitUntil(sls.putSlsLog({ 
          logType: 'error', 
          errorType: 'fetchPageDataError', 
          errInfo: '获取csr页面资源错误',
          errMsg: err?.message || '',
        }));
        // 超时走容灾处理
        if (err && err.message === 'Request timed out') {
          return new Response('请求超时', {
            status: 504,
            headers: {
              'content-type': 'text/html; charset=utf-8',
            }
          });
        }
        // 失败场景降级到proxy
        return new Response('', {
          status: 302,
          headers: {
            'content-type': 'text/html; charset=utf-8',
            location: event.request.url.replace('proxy-er', 'proxy')
          },
        });
      }),
  ]);
}

/** 请求异步数据 */
export function fetchAsyncScriptData(reqHost: string, pageUrl: string, headers: Headers): Promise<IMiniprogramResp> {
  const queryStr = pageUrl.split('?')[1] || '';
  return new Promise((resolve) => {
    const proxyReqUrl = `https://${getRequestHost(reqHost)}/miniprogram/webview-info${queryStr ? '?' : ''}${queryStr}`;

    requestWithTimeout(proxyReqUrl, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
        'User-Agent': headers.get('user-agent') || '',
        'Cookie': headers.get('cookie'),
      },
      // 预发拿到的是http的
      body: JSON.stringify({ pageUrl: getRealPageUrl(pageUrl) }),
      mode: 'cors',
    })
      .then(async res => {
        const jsonRes = await res.json();
        return resolve({
          ...jsonRes,
          headers: res.headers,
        });
      })
      .catch((err) => resolve({
        success: false,
        msg: err
      }));
  });
}

/**
 * 获取异步数据
 * 微信：泰坦降级配置及用户信息（openId、unionId）
 * 小红书：用户信息（openId）
 */
export function fetchAsyncData(event: FetchEvent, reqUrl: URL, headers: Headers): Promise<{ userInfo?: Record<string, any>; titanConfig?: Record<string, any>; msg?: string }> {
  const wxUserInfo = reqUrl.searchParams.get('_wx_user_info_');
  const queryStr = wxUserInfo ? `?_wx_user_info_=${encodeURIComponent(wxUserInfo)}` : '';
  return new Promise((resolve) => {
    const proxyReqUrl = `https://${getRequestHost(reqUrl.host)}/miniprogram/async-info${queryStr}`;
    requestWithTimeout(proxyReqUrl, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
        'User-Agent': headers.get('user-agent') || '',
        'Cookie': headers.get('cookie')
      },
      mode: 'cors',
    }).then(async res => {
      const jsonRes = await res.json();
      event.waitUntil(setAsyncDataCache(reqUrl, jsonRes, wxUserInfo));
      return resolve({
        ...jsonRes,
        headers: res.headers,
      });
    })
    .catch((err) => resolve({
      msg: err
    }));
  });
}

/** 发送埋点 */
export function sendLog(reqHost: string, headers, data) {
  return requestWithTimeout(`https://${getRequestHost(reqHost)}/miniprogram/proxy-log`, {
    method: 'POST',
    headers: {
      ...headers,
      'Content-Type': 'application/json',
      'User-Agent': headers.get('user-agent') || '',
      'Cookie': headers.get('cookie')
    },
    body: JSON.stringify({ data }),
    mode: 'cors',
  }).catch(res => { console.error('send proxy-log error', res) });
}
