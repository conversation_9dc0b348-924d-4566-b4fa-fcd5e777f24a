import { PRO_HOST_MAP, PRE_HOST_MAP, ORIGIN_HOST_NAME, PRO_DOMAIN_WHITELIST, CSR_CACHE_QUERY } from '../constant/index';

export function getRealHost(reqUrl: URL) {
  const isWapa = reqUrl.host.indexOf('pre-') > -1 || reqUrl.host.indexOf('.wapa.') > -1;
  const fmRealHost = reqUrl.searchParams.get(ORIGIN_HOST_NAME);
  const firstPath = `/${reqUrl.pathname.split('/')[1]}`;

  // 预发
  if (isWapa || reqUrl.searchParams.get('_fm_pro_proxyany_')) {
    // edith域名切分支
    const edithProjectVersion = reqUrl.searchParams.get('_fm_project_version_');
    if (reqUrl.searchParams.get('_fm_use_edith_') && edithProjectVersion) {
      return isWapa ? 'pre-edith.feizhu.com' : 'edith.feizhu.com';
    }
    // 优先使用预发的域名
    const preHost = PRE_HOST_MAP[firstPath] || fmRealHost;
    return preHost;
  }

  // 优先取链接上有带源host，线上只允许白名单的源host
  if (fmRealHost && PRO_DOMAIN_WHITELIST.indexOf(fmRealHost) > -1) {
    return fmRealHost;
  }

  // 未带源host，则取可代理的path
  const realHost = PRO_HOST_MAP[firstPath];
  if (realHost) {
    return realHost;
  }
  return '';
}

/** 获取csr请求路径 */
export function getCsrFetchUrl(reqUrl: URL, realHost: string) {
  const csrUrl = `https://${realHost}${reqUrl.pathname}`;
  const edithProjectVersion = reqUrl.searchParams.get('_fm_project_version_');

  // edith域名切分支
  if (reqUrl.searchParams.get('_fm_use_edith_') && edithProjectVersion) {
    return `${csrUrl}?projectVersion=${edithProjectVersion}`;
  }
  return csrUrl;
}

/** 获取页面真实链接 */
export function getRealPageUrl(url: string) {
  const urlObj = new URL(url);
  // 预发是http，需要转一层
  let processedUrl = url.replace('http://', 'https://');

  // 非预请求，直接返回
  if (!urlObj.searchParams.get(CSR_CACHE_QUERY.PRE_FETCH)) {
    return processedUrl;
  }

  // 预请求需要删除的参数列表
  const paramsToDelete = [
    CSR_CACHE_QUERY.DELETE_CACHE, // '_deletePre'
    CSR_CACHE_QUERY.PRE_FETCH,    // '_precsr'
    '_bx-m'
  ];

  // 使用字符串方式删除相应参数，保持其他参数顺序不变
  paramsToDelete.forEach(param => {
    const sign = urlObj.search.indexOf(param) === 1 ? '?' : '&';
    processedUrl = processedUrl.replace(`${sign}${param}=${urlObj.searchParams.get(param)}`, '');
  });

  return processedUrl;
}

/** 获取二级域名 */
export function getSecondLevelDomain(host: string) {
  try {
    return host.split('.').slice(-2).join('.');
  } catch (e) {
    console.warn('【Host】二级域名解析出错', e);
  }
  return host;
}