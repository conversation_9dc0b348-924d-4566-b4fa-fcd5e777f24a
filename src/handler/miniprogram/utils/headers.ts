import { getSecondLevelDomain } from './url';

/** 清理重复响应求头信息 */
export function removeResHeaders(headers: Headers) {
  const keys = [
    'content-encoding',
    'eagleeye-traceid',
    's-brt',
    's-rt',
    'strict-transport-security',
    'ups-target-key',
    'vary',
    'x-protocol',
  ];
  for (const _k of keys) {
    headers.delete(_k);
  }
  return headers;
}

/** 合并异步请求的cookie */
export function mergeHeaderCookies(respHeaders, asyncData: { headers?: Headers; userInfo?: Record<string, any>; }, reqHost: string) {
  try {
    const asyncSetCookie = asyncData?.headers?.get('Set-Cookie') || '';
    if (asyncSetCookie) {
      // 处理多个 Set-Cookie 头
      const cookies = asyncSetCookie.split(/,\s*(?=[^;]+=[^;]+)/);
      for (const cookie of cookies) {
        if (cookie) {
          respHeaders.append('Set-Cookie', cookie);
        }
      }
    }
    // 设置cookie
    const domain = getSecondLevelDomain(reqHost);
    const { openId, unionId } = asyncData?.userInfo || {};
    const options = { domain, httpOnly: true, maxAge: 3 * 60 * 60 * 1000 };
    openId && setCookie(respHeaders, 'openId', openId, options);
    unionId && setCookie(respHeaders, 'unionId', unionId, options);
  } catch (e) {
    // 记录错误但继续执行
    console.error('Set cookie error:', e);
  }
}

/** 添加跨域头 */
export function addAccessHeaders(headers: Headers, host: string) {
  headers.set('Access-Control-Allow-Origin', `https://${host}`);
  headers.set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type');
  headers.set('Access-Control-Allow-Credentials', 'true');
}

/** 从headers读取cookie */
export function getCookies(headers: Headers) {
  const headerCookie = headers.get('cookie') || '';
  const cookieMap = headerCookie.split(';').reduce((cookies, cookie) => {
    const [key, value] = cookie.trim().split('=');
    cookies[key] = value;
    return cookies;
  }, {} as Record<string, string>);
  return cookieMap;
}

/** 
 * 设置cookie默认有效期3小时 
 * @param headers 响应头
 * @param name 字段名
 * @param value 值
 * @param options.domain 域名
 * @param options.httpOnly 是否只允许http访问
 * @param options.maxAge 有效期，单位毫秒
 * */
export function setCookie(headers: Headers, name: string, value: string, options: { domain: string; httpOnly?: boolean; maxAge: number }) {
  try {
    const { domain, httpOnly, maxAge } = options;
    // 设置3小时后过期
    const expiresDate = new Date(Date.now() + maxAge);
    const httpOnlyStr = httpOnly ? ' HttpOnly' : '';
    const cookieValue = `${name}=${value}; Domain=${domain}; Path=/; Expires=${expiresDate.toUTCString()};${httpOnlyStr}`;
    headers.append('Set-Cookie', cookieValue);
  } catch (e) {
    console.error('set cookie error:', e);
  }
}

/** 删除指定的cookie */
export function removeCookie(headers: Headers, name: string, domain: string) {
  setCookie(headers, name, '', { domain, maxAge: 0 });
}