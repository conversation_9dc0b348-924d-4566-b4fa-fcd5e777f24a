import { getSecondLevelDomain } from './url';
import { APP_ENV } from '../constant/index';

export function getAppEnv(headers: Headers) {
  const ua = headers.get('user-agent') || '';
  // 微信
  if (ua.match(/MicroMessenger/i)) {
    return APP_ENV.WEIXIN;
  }
  // 抖音
  if (ua.match(/ToutiaoMicroApp/i)) {
    return APP_ENV.JDOUYIN;
  }
  // 小红书
  if (ua.match(/xhsminiapp/i)) {
    return APP_ENV.XIAOHONGSHU;
  }
  return '';
}

/** 
 * 从headers读取cookie
 */
export function getCookies(headers: Headers) {
  const headerCookie = headers.get('cookie') || '';
  const cookieMap = headerCookie.split(';').reduce((cookies, cookie) => {
    const [key, value] = cookie.trim().split('=');
    cookies[key] = value;
    return cookies;
  }, {} as Record<string, string>);
  return cookieMap;
}

/** 清理重复响应求头信息 */
export function removeResHeaders(headers: Headers) {
  const keys = [
    'content-encoding',
    'eagleeye-traceid',
    's-brt',
    's-rt',
    'strict-transport-security',
    'ups-target-key',
    'vary',
    'x-protocol',
  ];
  for (const _k of keys) {
    headers.delete(_k);
  }
  return headers;
}

/** 合并异步请求的cookie */
export function mergeHeaderCookies(respHeaders, asyncData: { headers?: Headers; userInfo?: Record<string, any>; }, reqHost: string) {
  try {
    const asyncSetCookie = asyncData?.headers?.get('Set-Cookie') || '';
    if (asyncSetCookie) {
      // 处理多个 Set-Cookie 头
      const cookies = asyncSetCookie.split(/,\s*(?=[^;]+=[^;]+)/);
      for (const cookie of cookies) {
        if (cookie) {
          respHeaders.append('Set-Cookie', cookie);
        }
      }
    }
    // 设置用户信息cookie
    const domain = getSecondLevelDomain(reqHost);
    const { openId, unionId, from } = asyncData?.userInfo || {};
    if (from !== 'cookie') {
      const options = { domain, httpOnly: true, maxAge: 3 * 60 * 60 * 1000 };
      openId && setCookie(respHeaders, '_fm_wx_open_id_', openId, options);
      unionId && setCookie(respHeaders, '_fm_wx_union_id_', unionId, options);
    }
  } catch (e) {
    // 记录错误但继续执行
    console.error('Set cookie error:', e);
  }
}

/** 添加跨域头 */
export function addAccessHeaders(headers: Headers, host: string) {
  headers.set('Access-Control-Allow-Origin', `https://${host}`);
  headers.set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type');
  headers.set('Access-Control-Allow-Credentials', 'true');
}

/** 
 * 设置cookie默认有效期3小时 
 * @param headers 响应头
 * @param name 字段名
 * @param value 值
 * @param options.domain 域名
 * @param options.httpOnly 是否只允许http访问
 * @param options.maxAge 有效期，单位毫秒
 * */
export function setCookie(headers: Headers, name: string, value: string, options: { domain: string; httpOnly?: boolean; maxAge: number }) {
  try {
    const { domain, httpOnly, maxAge } = options;
    // 将毫秒转换为秒，Max-Age 使用秒为单位
    const maxAgeInSeconds = Math.floor(maxAge / 1000);
    // 同时设置 Max-Age 和 Expires 以确保兼容性
    const expiresDate = new Date(Date.now() + maxAge);
    const httpOnlyStr = httpOnly ? '; HttpOnly' : '';
    const cookieValue = `${name}=${value}; Domain=${domain}; Path=/; Max-Age=${maxAgeInSeconds}; Expires=${expiresDate.toUTCString()}; Secure${httpOnlyStr}`;
    headers.append('Set-Cookie', cookieValue);
  } catch (e) {
    console.error('set cookie error:', e);
  }
}

/** 删除指定的cookie */
export function removeCookie(headers: Headers, name: string, domain: string) {
  setCookie(headers, name, '', { domain, maxAge: 0 });
}