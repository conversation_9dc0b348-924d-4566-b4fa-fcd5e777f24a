/** 清理重复响应求头信息 */
export function removeResHeaders(headers: Headers) {
  const keys = [
    'content-encoding',
    'eagleeye-traceid',
    's-brt',
    's-rt',
    'strict-transport-security',
    'ups-target-key',
    'vary',
    'x-protocol',
  ];
  for (const _k of keys) {
    headers.delete(_k);
  }
  return headers;
}

/** 合并异步请求的cookie */
export function mergeHeaderCookies(headers, asyncHeaders) {
  try {
    const asyncSetCookie = asyncHeaders?.get('Set-Cookie') || '';
    if (asyncSetCookie) {
      // 处理多个 Set-Cookie 头
      const cookies = asyncSetCookie.split(/,\s*(?=[^;]+=[^;]+)/);
      for (const cookie of cookies) {
        if (cookie) {
          headers.append('Set-Cookie', cookie);
        }
      }
    }
  } catch (e) {
    // 记录错误但继续执行
    console.error('Set cookie error:', e);
  }
}

/** 添加跨域头 */
export function addAccessHeaders(headers: Headers, host: string) {
  headers.set('Access-Control-Allow-Origin', `https://${host}`);
  headers.set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type');
  headers.set('Access-Control-Allow-Credentials', 'true');
}

/** 从headers读取cookie */
export function getCookies(headers: Headers) {
  const headerCookie = headers.get('cookie') || '';
  const cookieMap = headerCookie.split(';').reduce((cookies, cookie) => {
    const [key, value] = cookie.trim().split('=');
    cookies[key] = value;
    return cookies;
  }, {} as Record<string, string>);
  return cookieMap;
}

/** 删除指定的cookie */
export function removeCookie(headers: Headers, name: string, domain: string) {
  // 通过设置过期时间为过去的时间来删除cookie
  const expiredDate = new Date(0).toUTCString(); // 1970-01-01
  const cookieValue = `${name}=; Domain=${domain}; Path=/; Expires=${expiredDate}; HttpOnly`;
  headers.append('Set-Cookie', cookieValue);
}