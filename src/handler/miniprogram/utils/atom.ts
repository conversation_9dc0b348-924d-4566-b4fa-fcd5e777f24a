// import { getSecondLevelDomain } from './url';
import { getCookies, removeCookie } from './headers';
import { getSecondLevelDomain } from './url';

const ATOM_REJECT_SCRIPT_SRC: string = 'https://g.alicdn.com/code/npm/@ali/rxpi-miniwork-tool/2.0.1/pages/trail/index.web.js';
const ATOM_REJECT_CSS_HREF: string = 'https://g.alicdn.com/code/npm/@ali/rxpi-miniwork-tool/2.0.1/pages/trail/index.css';
// const ATOM_REJECT_SCRIPT_SRC: string = 'https://dev.g.alicdn.com/code/npm/@ali/rxpi-miniwork-tool/2.0.1/pages/trail/index.web.js';
// const ATOM_REJECT_CSS_HREF: string = 'https://dev.g.alicdn.com/code/npm/@ali/rxpi-miniwork-tool/2.0.1/pages/trail/index.css';

export function isShowAtom(reqUrl: URL, cookies: Record<string, string>) {
  const PAGE_ENV = reqUrl.searchParams.get('PAGE_ENV');
  const ATOM_SHOW = reqUrl.searchParams.get('ATOM_SHOW');
  // 小程序版本
  const envVersion = PAGE_ENV || cookies.__atom_miniapp_env__ || 'release';
  // 仅开发版或者扫码开启了ATOM_SHOW才展示
  return envVersion === 'develop' || ATOM_SHOW;
}

/** 不展示atom的情况下清理atom cookie */
export function removeAtomCookie(reqUrl: URL, reqHeaders: Headers, respHeaders: Headers) {
  const cookies = getCookies(reqHeaders);
  const showAtom = isShowAtom(reqUrl, cookies);
  if (!showAtom) {
    const domain = getSecondLevelDomain(reqUrl.host);
    if (cookies.__atom_acess_params__) {
      removeCookie(respHeaders, '__atom_acess_params__', domain);
    }
    if (cookies.__atom_miniapp_env__) {
      removeCookie(respHeaders, '__atom_miniapp_env__', domain);
    }
  }
}

/** 注入Atom调试工具 */
export function insertAtom(reqUrl: URL, reqHeaders: Headers) {
  try {
    const cookies = getCookies(reqHeaders);
    const showAtom = isShowAtom(reqUrl, cookies);

    // 不展示移除cookie内容
    if (!showAtom) {
      // const domain = getSecondLevelDomain(reqUrl.host);
      // removeCookie(respHeaders, '__atom_acess_params__', domain);
      // removeCookie(respHeaders, '__atom_miniapp_env__', domain)
      return;
    };

    let capturing = false;
    try{
      const preAtomParams = JSON.parse(cookies.__atom_acess_params__ || '{}');
      capturing = preAtomParams.scanParams && preAtomParams.scanParams.capturing || false;
    } catch (err) {}

    const linkTag = `<link rel="stylesheet" href="${ATOM_REJECT_CSS_HREF}">`;
    const scriptTag = `<script src="${ATOM_REJECT_SCRIPT_SRC}" defer="${!capturing}" type="text/javascript"></script>`;
    return `${linkTag}${scriptTag}`;
  } catch (err) {
    return '';
  }
}
