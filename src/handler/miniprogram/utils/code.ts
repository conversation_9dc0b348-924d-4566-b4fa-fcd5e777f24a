import { JSSDK_CONFIG, APP_ENV } from '../constant/index';
import { getRealPageUrl } from './url';

const PERFORMANCE_DATA_KEY = '_proxy_perf_obj_';

function getAppEnv(ua: string) {
  // 微信
  if (ua.match(/MicroMessenger/i)) {
    return APP_ENV.WEIXIN;
  }
  // 抖音
  if (ua.match(/ToutiaoMicroApp/i)) {
    return APP_ENV.JDOUYIN;
  }
  // 小红书
  if (ua.match(/xhsminiapp/i)) {
    return APP_ENV.XIAOHONGSHU;
  }
  return '';
}

/** 插入套壳性能埋点数据 */
function getProxyPerfData(params: Record<string, any>) {
  const { webviewLoadTime, beginTime } = params;

  if (webviewLoadTime) {
    const endTime = Date.now();
    // 仅当第一页或者前置页是登录页有效
    return `
      <script>
        try {
          if (history && history.length === ${1} || document.referrer === '' || document.referrer.indexOf('passport.') > -1) {
            window.${PERFORMANCE_DATA_KEY} = {
              t4: ((window.performance && window.performance.timing && window.performance.timing.navigationStart) || 0) - ${webviewLoadTime},
              t7: ${beginTime - webviewLoadTime},
              t8: ${endTime - beginTime},
              src: ${webviewLoadTime}
            };
          }
        } catch(e) {
          console.error('proxy performance info error', e);
        }
      </script>
    `;
  }
  return '';
}

/**
 *  解决ios在客户端执行wx.config 签名不合法的问题：ios校验页面url取的是第一次加载的链接，而安卓是最终的，base会通过replaceState修改url，因此ios需要记录一下最开始的页面链接
 * @param headers 
 * @param pageUrl 
 * @param appEnv 
 * @returns 
 */
function getPageUrlScript(headers: Headers, pageUrl: string, appEnv: string) {
  const ua = headers.get('user-agent') || headers.get('User-Agent') || '';
  // 预发是http，需要转一层
  const url = getRealPageUrl(pageUrl);
  if (appEnv === APP_ENV.WEIXIN && url && /(iPhone\sOS)\s([\d_]+)/.test(ua)) {
    return `<script>window._fmInitPageUrl = "${url}";</script>`;
  }
  return '';
}

/** 从cookie中获取用户信息 */
function getUserInfoByCookie(headers: Headers, scriptList: string[], appEnv: string) {
  const hasUserInfo = (scriptList || []).some(v => v.indexOf('window._fm_wx_user_info_') > -1);
  // 从异步信息中拿到了用户信息，或者非微信环境，不需要从cookie中获取
  if (hasUserInfo || appEnv !== APP_ENV.WEIXIN) return '';

  const headerCookie = headers.get('cookie') || '';
  const cookieMap = headerCookie.split(';').reduce((cookies, cookie) => {
    const [key, value] = cookie.trim().split('=');
    cookies[key] = value;
    return cookies;
  }, {} as Record<string, string>);

  // 用户信息
  const userId = cookieMap.munb;
  const unionId = cookieMap._fm_wx_union_id_;
  const openId = cookieMap._fm_wx_open_id_;

  return `<script>window._fm_wx_user_info_ = ${JSON.stringify({userId: userId || undefined,unionId: unionId || undefined,openId: openId || undefined})};</script>`
}

/** 获取静态配置 */
export function getScriptStr(headers: Headers, scriptList: string[], extra: Record<string, any> = {}) {
  const ua = headers.get('user-agent') || '';
  const appEnv = getAppEnv(ua);
  const jssdk = JSSDK_CONFIG[appEnv] || '';

  return [
    // 各端的jssdk
    jssdk,
    // 用户信息
    getUserInfoByCookie(headers, scriptList, appEnv),
    // 性能数据
    getProxyPerfData(extra),
    // 初始化页面链接
    getPageUrlScript(headers, extra.pageUrl, appEnv),
    // 异步获取的信息
    ...scriptList,
  ].join('');
}
