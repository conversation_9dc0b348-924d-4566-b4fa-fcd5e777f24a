import { JSSDK_CONFIG, APP_ENV } from '../constant/index';
import { getRealPageUrl } from './url';
import { insertAtom } from './atom';

const PERFORMANCE_DATA_KEY = '_proxy_perf_obj_';

export function getAppEnv(headers: Headers) {
  const ua = headers.get('user-agent') || '';
  // 微信
  if (ua.match(/MicroMessenger/i)) {
    return APP_ENV.WEIXIN;
  }
  // 抖音
  if (ua.match(/ToutiaoMicroApp/i)) {
    return APP_ENV.JDOUYIN;
  }
  // 小红书
  if (ua.match(/xhsminiapp/i)) {
    return APP_ENV.XIAOHONGSHU;
  }
  return '';
}

/** 插入套壳性能埋点数据 */
function getProxyPerfData(params: Record<string, any>) {
  const { webviewLoadTime, beginTime } = params;

  if (webviewLoadTime) {
    const endTime = Date.now();
    // 仅当第一页或者前置页是登录页有效
    return `
      <script>
        try {
          if (history && history.length === ${1} || document.referrer === '' || document.referrer.indexOf('passport.') > -1) {
            window.${PERFORMANCE_DATA_KEY} = {
              t4: ((window.performance && window.performance.timing && window.performance.timing.navigationStart) || 0) - ${webviewLoadTime},
              t7: ${beginTime - webviewLoadTime},
              t8: ${endTime - beginTime},
              src: ${webviewLoadTime}
            };
          }
        } catch(e) {
          console.error('proxy performance info error', e);
        }
      </script>
    `;
  }
  return '';
}

/**
 *  解决ios在客户端执行wx.config 签名不合法的问题：ios校验页面url取的是第一次加载的链接，而安卓是最终的，base会通过replaceState修改url，因此ios需要记录一下最开始的页面链接
 * @param headers 
 * @param pageUrl 
 * @param appEnv 
 * @returns 
 */
function getPageUrlScript(headers: Headers, pageUrl: string, appEnv: string) {
  const ua = headers.get('user-agent') || headers.get('User-Agent') || '';
  // 预发是http，需要转一层
  const url = getRealPageUrl(pageUrl);
  if (appEnv === APP_ENV.WEIXIN && url && /(iPhone\sOS)\s([\d_]+)/.test(ua)) {
    return `<script>window._fmInitPageUrl = "${url}";</script>`;
  }
  return '';
}

function getUserInfoCode(userInfo) {
  if (!userInfo) return '';
  return `<script>window._fm_wx_user_info_ = ${JSON.stringify(userInfo)};</script>`;
}

// 直连ER配置
const WEIXIN_NEW_PROXY_CLIENT_KEY = '_fm_proxy_gray_conf_';
// 降级配置
const DOWNGRADE_CONFIG_NODE_KEY = '_fm_minipage_downgrade_conf_';

function getTitanConfigCode(titanConfig) {
  if (!titanConfig) return '';
  return `<script>
    window.${DOWNGRADE_CONFIG_NODE_KEY} = ${JSON.stringify(titanConfig.downgrade)};
    window.${WEIXIN_NEW_PROXY_CLIENT_KEY} = ${JSON.stringify(titanConfig.webviewGray)};
  </script>`;
}

/** 从cookie中获取用户信息 */
export function getUserInfoFromCookie(headers: Headers) {
  const headerCookie = headers.get('cookie') || '';
  const cookieMap = headerCookie.split(';').reduce((cookies, cookie) => {
    const [key, value] = cookie.trim().split('=');
    cookies[key] = value;
    return cookies;
  }, {} as Record<string, string>);

  // 用户信息
  const userId = cookieMap.munb;
  const unionId = cookieMap._fm_wx_union_id_;
  const openId = cookieMap._fm_wx_open_id_;
  const invalid = userId && !openId;

  return { userId, unionId, openId, invalid };
}

/** 处理异步数据 */
export function getScriptByAsyncData(asyncData: { userInfo?: Record<string, any>; titanConfig?: Record<string, any>;}, reqUrl: URL, reqHeaders: Headers) {
  const { userInfo, titanConfig } = asyncData;
  return [
    // 用户信息
    getUserInfoCode(userInfo),
    // 泰坦配置
    getTitanConfigCode(titanConfig),
    // atom
    insertAtom(reqUrl, reqHeaders),
  ];
}

/** 从cookie中获取用户信息 */
export function getUserInfoByCookie(headers: Headers, scriptList: string[], appEnv: string) {
  const hasUserInfo = (scriptList || []).some(v => v.indexOf('window._fm_wx_user_info_') > -1);
  // 从异步信息中拿到了用户信息，或者非微信环境，不需要从cookie中获取
  if (hasUserInfo || appEnv !== APP_ENV.WEIXIN) return '';

  const { invalid, ...userInfo } = getUserInfoFromCookie(headers);
  return getUserInfoCode(userInfo);
}

/** 获取静态配置 */
export function getScriptStr(headers: Headers, scriptList: string[], extra: Record<string, any> = {}) {
  const appEnv = getAppEnv(headers);
  const jssdk = JSSDK_CONFIG[appEnv] || '';

  return [
    // 各端的jssdk
    jssdk,
    // 用户信息
    getUserInfoByCookie(headers, scriptList, appEnv),
    // 性能数据
    getProxyPerfData(extra),
    // 初始化页面链接
    getPageUrlScript(headers, extra.pageUrl, appEnv),
    // 异步获取的信息
    ...scriptList,
  ].join('');
}
