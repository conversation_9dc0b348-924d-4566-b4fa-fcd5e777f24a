import { getDisasterHtml } from './utils/disaster';
import { getMiniProgramData } from './utils/api';
import { getScriptStr } from './utils/code';
import { getRealHost } from './utils/url';
import { SSR_HOST, ORIGIN_HOST_NAME } from './constant/index';
import { WX_HOST } from '../../constant';
import Logger from './utils/logger';
import { handleMiniProgramCsrRequest } from './csr'
import { handleMiniProgramSsrRequest } from './ssr';

// 外部定制流程，获取在小程序套壳场景的html注入内容（登录态/脚本）
// 提供给hanlder/detail自定义渲染流程
export async function loadMiniprogramDependencies(event: FetchEvent, serverBeginTime?: number) {
  try {
    const { request } = event;
    const headers = request.headers;
    const pageUrl = request.url;
    const reqUrl = new URL(pageUrl);
    const pageHost = reqUrl.host;

    // 非ER域名不追加代理服务内容
    if (!WX_HOST.includes(pageHost)) {
      return {
        success: true,
        htmlString: '',
      };
    }

    const beginTime = serverBeginTime || new Date().getTime();
    const webviewLoadTime = reqUrl.searchParams.get('webviewLoadTime');
    const scriptData = await getMiniProgramData(event, reqUrl, pageUrl, headers);
    const asyncScriptList = scriptData?.scriptList || [];

    return {
      success: true,
      htmlString: getScriptStr(headers, asyncScriptList, { webviewLoadTime, beginTime, pageUrl }),
    }
  } catch (err) {
    return {
      success: false,
      errorMessage: err.message
    }
  }
}

/** 小程序来源请求逻辑 */
export async function handleMiniProgramRequest(event: FetchEvent) {
  const { request } = event;
  const headers = request.headers;
  const reqUrl = new URL(request.url);
  const isSSR = SSR_HOST.includes(reqUrl.searchParams.get(ORIGIN_HOST_NAME));
  const sls = new Logger({
    headers,
    reqUrl,
    isSSR,
  });

  // ssr场景
  if (isSSR) {
    return await handleMiniProgramSsrRequest(event, sls);
  }

  // 不合法的链接
  const realHost = getRealHost(reqUrl);
  if (!realHost) {
    return getDisasterHtml(event, {
      sls,
      invalid: true,
      reqUrl,
      status: 200,
      isHtmlOrText: true,
      resHeaders: {
        'content-type': 'text/html; charset=utf-8',
      }
    });
  }

  // csr场景
  return await handleMiniProgramCsrRequest(event, realHost, sls);
}
