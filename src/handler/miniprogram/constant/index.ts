
// SSR域名
export const SSR_HOST = [
  // 线上域名
  'h5-er.fliggy.cn',
  'outfliggys.m.taobao.com',
  'front-traffic-fliggy-er.amap.com',
  // 预发域名
  'pre-h5-er.fliggy.cn',
  'outfliggys.wapa.taobao.com',
  'pre-fliggyrax.wapa.taobao.com',
  'pre-fliggyssr.wapa.taobao.com',
  'pre-front-traffic-fliggy-er.amap.com',
  'trip-air-fliggy-common-er.pre-air-er.taobao.com',
];

// 线上域名映射
export const PRO_HOST_MAP = {
  '/app': 'market.m.taobao.com',
  '/trip': 'h5.m.taobao.com',
  '/wow': 'f.m.taobao.com',
  '/item': 'detail.m.tmall.com',
  '/tpsservice': 'files.alicdn.com',
  '/apps': 'survey.nhcilab.com',
  '/html': 'haibao.m.taobao.com',
  '/clam': 'edith.m.taobao.com',
  '/markets': 'market.m.taobao.com',
}
// 线上域名映射
export const PRE_HOST_MAP = {
  '/app': 'market.wapa.taobao.com',
  '/trip': 'h5.wapa.taobao.com',
  '/wow': 'pre-wormhole.wapa.taobao.com',
  '/item': 'detail.m.tmall.com',
  '/tpsservice': 'files.alicdn.com',
  '/proxy': 'edith.wapa.taobao.com',
  '/apps': 'survey.nhcilab.com',
  '/html': 'haibao.m.taobao.com',
  '/clam': 'edith.wapa.taobao.com',
  '/.well-known': 'market.wapa.taobao.com',
}

export const ORIGIN_HOST_NAME = '_fm_real_host_';

// 生产允许代理的域名 -- 外网域名不允许访问内网域名
export const PRO_DOMAIN_WHITELIST = [
  ...Object.values(PRO_HOST_MAP),
  'f.m.fliggy.com', // CSR域名
  'ai.alimebot.taobao.com',
  'scrm.feizhu.com', // scrm域名
  'passport.feizhu.com',  // 登录域名
  'passport.fzwxxcx.com',  // 登录域名
  'passport.fzwxxcx.cn',  // 登录域名
  'passport.tmwxxcx.com',  // 登录域名
  'survey.taobao.com' //问卷域名
];

export const APP_ENV = {
  WEIXIN: 'weixin', // 微信
  JDOUYIN: 'jduoyin', // 抖音
  XIAOHONGSHU: 'xiaohongshu', // 小红书
};

export const JSSDK_CONFIG = {
  // 微信
  [APP_ENV.WEIXIN]: '<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script><script fixed="true">(function(){ function _noop(){return {};} history.replaceState = window.history.replaceState || _noop;  wx.getSystemInfoSync=_noop; wx.request=wx.request||_noop; window.getApp = _noop; window.getCurrentPages = _noop;})()</script>',
  // 抖音
  [APP_ENV.JDOUYIN]: '<script src="https://lf1-cdn-tos.bytegoofy.com/goofy/developer/jssdk/jssdk-1.0.3.js"></script>',
  // 小红书
  [APP_ENV.XIAOHONGSHU]: '<script src="https://fe-static-1251524319.cos.ap-shanghai.myqcloud.com/xhs-mp/open/js/xiaohongshu-3.8.0.js"></script>',
};

// 缓存参数
export const CSR_CACHE_QUERY = {
  PRE_FETCH: '_precsr',
  DELETE_CACHE: '_deletePre',
}
