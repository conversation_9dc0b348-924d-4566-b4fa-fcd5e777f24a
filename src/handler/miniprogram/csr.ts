import { getMiniProgramData, fetchPageData } from './utils/api';
import { getPageByPrefetchCache, deleteCache, setPagePrefetchCache, setCacheTag } from './utils/cache';
import { removeHtmlCacheHeaders } from '../../utils';
import { handleMiniProgramResponse } from './utils/response';
import { getCsrFetchUrl } from './utils/url';
import { CSR_CACHE_QUERY } from './constant/index';
import Logger from './utils/logger';

/** 从源站拉取页面 */
async function getPageByFetch(event: FetchEvent, reqUrl: URL, realHost: string, sls: Logger) {
  const { request } = event;
  const headers = request.headers;
  const reqHost = reqUrl.host;
  const csrPageUrl = getCsrFetchUrl(reqUrl, realHost);

  // 发送请求
  const [pageRes, scriptData] = await Promise.all([
    fetchPageData(event, csrPageUrl, sls).then(res => {
      sls.performanceTimeEnd('getPage');
      return res;
    }), // 获取页面html
    getMiniProgramData(event, reqUrl, request.url, headers).then(res => {
      sls.performanceTimeEnd('fetchMiniProgramData');
      return res;
    }),
  ]);
  // 缓存标
  setCacheTag(pageRes.headers, sls);
  return await handleMiniProgramResponse(event, pageRes, scriptData, sls);
}

/** 获取csr页面 */
export async function handleMiniProgramCsrRequest(event: FetchEvent, realHost: string, sls: Logger) {
  const { request } = event;
  const reqUrl = new URL(request.url);

  // 清理cache缓存
  if (reqUrl.searchParams.get(CSR_CACHE_QUERY.DELETE_CACHE)) {
    return await deleteCache(request, reqUrl, realHost);
  }

  // 预请求缓存数据
  if (reqUrl.searchParams.get(CSR_CACHE_QUERY.PRE_FETCH)) {
    const handlePrefetch = async () => {
      const pageResp = await getPageByFetch(event, reqUrl, realHost, sls)
      await setPagePrefetchCache(pageResp, request, reqUrl, sls);
    };
    event.waitUntil(handlePrefetch());
    return new Response('预请求成功', {
      headers: {
        'content-type': 'text/html; charset=utf-8',
      }
    });
  }

  const pageResponse = await Promise.race([
    // 取缓存
    getPageByPrefetchCache(event, reqUrl, sls),
    // 实时拿
    getPageByFetch(event, reqUrl, realHost, sls),
  ]);
  // 清理缓存头
  removeHtmlCacheHeaders(pageResponse.headers);
  return pageResponse;
}