
import { getMiniProgramData } from './utils/api';
import { handleMiniProgramResponse, handleStreamResponse } from './utils/response';
import handleRaxRequest from '../rax';
import { judgeIsStream } from '../../utils/rax';
import Logger from './utils/logger';
import { setCacheTag } from './utils/cache';

/** ssr请求 */
export async function handleMiniProgramSsrRequest(event: FetchEvent, sls: Logger) {
  const { request } = event;
  const headers = request.headers;
  const reqUrl = new URL(request.url);
  const isStream = judgeIsStream(reqUrl, headers);

  /**
   * 流式ssr
   */
  if (isStream) {
    const asyncPromise = getMiniProgramData(event, reqUrl, request.url, headers).then(resp => {
      sls.performanceTimeEnd('fetchMiniProgramData');
      return resp;
    });
    const raxRes = await handleRaxRequest(event).then(res => {
      sls.performanceTimeEnd('getPage');
      return res;
    });
    if (!(raxRes instanceof Response)) {
      return raxRes;
    }
    // 非流式返回
    if (raxRes.headers.get('Transfer-Encoding') !== 'chunked') {
      const asyncData = await asyncPromise;
      setCacheTag(raxRes.headers, sls);
      return await handleMiniProgramResponse(event, raxRes, asyncData, sls);
    }
    // 流式返回
    return await handleStreamResponse(event, raxRes, asyncPromise, sls);
  }

  /**
   * 普通ssr
   */
  const [raxRes, asyncData] = await Promise.all([
    // 获取ssr页面信息
    handleRaxRequest(event).then(res => {
      sls.performanceTimeEnd('getPage');
      return res;
    }),
    // 小程序异步数据
    getMiniProgramData(event, reqUrl, request.url, headers).then(resp => {
      sls.performanceTimeEnd('fetchMiniProgramData');
      return resp;
    }),
  ]);

  // 处理非 Response 类型的逻辑
  const isResponse = raxRes instanceof Response;
  if (!isResponse) {
    return raxRes;
  }
  // 缓存标
  setCacheTag(raxRes.headers, sls);
  // 小程序脚本拼接
  return await handleMiniProgramResponse(event, raxRes, asyncData, sls);
}