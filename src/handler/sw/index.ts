import { checkIsPre } from "../../utils/rax";

export default async function handleSwRequest(event: FetchEvent) {
  const { request } = event;
  const reqUrl = new URL(request.url);
  const searchParams = reqUrl.searchParams;
  const version = searchParams.get('version');
  const matchStr = request.url.match(/app\/(.*?)\/pages/);
  if (matchStr && matchStr.length > 1 && version) {
    const isPre = checkIsPre(reqUrl);
    const swUrl = `https://${isPre ? 'dev.' : ''}g.alicdn.com/${matchStr[1]}/${version}/web/sw.js`;
    const swRes = await fetch(swUrl, { cdnProxy: true, decompress: 'manual' });
    return swRes;
  }
  if (reqUrl.pathname === '/app/trip/sw.js') {
    const isPre = checkIsPre(reqUrl);
    const swUrl = `https://${isPre ? 'dev.' : ''}o.alicdn.com/mpi/ssr-service-worker/index.js`;
    const swRes = await fetch(swUrl, { cdnProxy: true, decompress: 'manual' });
    return swRes;
  }
  return '';
}