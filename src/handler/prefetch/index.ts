import { addEdgeCache } from "../../processer/rax/edgeCache";
import { checkIsPre } from "../../utils/rax";
import {
  NEW_PRE_RAX_SSR_HOST,
  NEW_RAX_SSR_HOST
} from '../../constant/rax'

export default async function handlePrefetchRequest(event: FetchEvent) {
  const { request } = event;
  const reqUrl = new URL(request.url);
  const searchParams = reqUrl.searchParams;
  const rid = searchParams.get('rid');
  const isPre = checkIsPre(reqUrl);
  if (rid) {
    const hostName = isPre ? NEW_PRE_RAX_SSR_HOST: NEW_RAX_SSR_HOST;
    const oriUrl = `https://${hostName}/prefetch/index?${searchParams.toString()}`;
    const prefetchRes = await fetch(oriUrl, {
      headers: request.headers
    });
    const res = await prefetchRes.text();
    try {
      const resObj = JSON.parse(res) || {};
      Object.keys(resObj).map(item => {
        if (resObj[item] && resObj[item].indexOf('<html>') > 0 && resObj[item].indexOf('root') > 0) {
          const ssrResponse = new Response(resObj[item], {
            headers: { 'content-type': 'text/html; charset=utf-8' },
            status: 200
          });
          addEdgeCache(ssrResponse, event, {
            url: item.replace(/raxssr\.m\.fliggy\.com([\/]*[^\/]*)\/app/, 'outfliggys.m.taobao.com/app'),
            headers: request.headers
          })
        }
      })
    } catch (e) {}
  }
}