export default async function handleSidRequest(event: FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const searchParams = reqUrl.searchParams;
    const sid = searchParams.get('sid');
    const sidRes = await fetch(`https://fliggyrax.taobao.com/updateSid/index?sid=${sid}`, {
      headers: request.headers
    });
    return sidRes;
  } catch(e) {
    return ''
  }
}
