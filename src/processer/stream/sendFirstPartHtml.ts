import { PRE_RAX_SSR_HOST, RAX_SSR_HOST, NEW_PRE_RAX_SSR_HOST, NEW_RAX_SSR_HOST } from "../../constant/rax";
import { getHost } from "../../utils/rax";

// 返回一屏的骨架图
export default async function (firstPartHtml, raxUrl, _startTime, titleBarOpt:any = {}, writeHtml, timeLog:any = {}, isDebug = false, isPre) {
  const newUrl = new URL(raxUrl);
  newUrl.searchParams.set('_er_failback', 'timeout');

  let newFirstPartHtml = firstPartHtml;
  // 如果有沉浸式参数 且 当前端支持沉浸式，则替换「标识」为真正的高度
  if (titleBarOpt.hasImmersiveQuery && titleBarOpt.canUseImmersive) {
    newFirstPartHtml = firstPartHtml
      .replace('_cookie_status_bar_height_er_', `${titleBarOpt['statusBarHeight'] || 0}px`)
      .replace('_cookie_total_bar_height_er_', `${titleBarOpt['totalBarHeight'] || 44}px`);
  }

  const wxNewUrl = new URL(raxUrl);
  wxNewUrl.searchParams.set('_er_failback', 'timeout');
  wxNewUrl.searchParams.set('_fm_real_host_', getHost(wxNewUrl));

  const list = [
    newFirstPartHtml,
    `<script data-id="first-part-success">
      window._first_part_show_time = Date.now();
      if(window.performance && typeof window.performance.mark == 'function'){
        window.performance.mark('stage-first-chunk')
      }
      if (window.performance && window.performance.timing && window.performance.timing.navigationStart) {
        window._first_part_time = window._first_part_show_time - window.performance.timing.navigationStart;
      }
      setTimeout(function(){
        if (!window._er_next_part_render && ${isDebug ? 'false' : 'true'}) {
          const wxNewUrl = "${wxNewUrl.toString()}";
          if (window.location.hostname.includes('proxy')) {
            location.replace(wxNewUrl.replace("${PRE_RAX_SSR_HOST}", window.location.hostname).replace("${RAX_SSR_HOST}", window.location.hostname)
            .replace("${NEW_PRE_RAX_SSR_HOST}", window.location.hostname).replace("${NEW_RAX_SSR_HOST}", window.location.hostname))
          } else {
            location.replace("${newUrl.toString()}")
          }
        }
      }, ${isPre ? 10000 : 5000});
    </script>`,
    '\n',
  ];
  timeLog.timeStamp[`first_part_end`] = Date.now();
  await writeHtml(list.join(''));
}
