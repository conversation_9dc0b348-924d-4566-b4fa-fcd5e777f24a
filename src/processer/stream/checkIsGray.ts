/**
 * 判断用户是否在流式渲染的灰度中
 * */
export default function (pageErConfig, request) {
  const grayRatio = pageErConfig && pageErConfig.grayRatio || 0;
  const grayTargetUserList = (pageErConfig && pageErConfig.grayUserList) ? pageErConfig.grayUserList.split(',') : [];

  const cookies = request.headers && request.headers.get('cookie');
  const unbMatches = cookies.match(/unb=(\d+);/);
  const munbMatches = cookies.match(/munb=(\d+);/);
  const unsafeUserId = (unbMatches && unbMatches[1]) || (munbMatches && munbMatches[1]) || '';
  const unsafeUserIdSuf = unsafeUserId.slice(-2);

  return grayTargetUserList.includes(unsafeUserId) || (parseInt(unsafeUserIdSuf) < parseInt(grayRatio));
}
