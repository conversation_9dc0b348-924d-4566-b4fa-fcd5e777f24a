export default async function sendFirstChunkCache(writeHtml, raxUrl, isDebug, isPre) {
  const newUrl = new URL(raxUrl);
  newUrl.searchParams.set('_er_failback', 'timeout');

  await writeHtml(`
    <div id="fcc_ios_placeholder" style="height:0;width:0;">\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b\u200b</div>
    <style type="text/css" id="html-common-style">
      @font-face {
        font-family: 'fliggy-home-iconfont';
        src: url("data:application/x-font-ttf;charset=utf-8;base64,AAEAAAALAIAAAwAwR1NVQiCLJXoAAAE4AAAAVE9TLzI8GkkMAAABjAAAAGBjbWFw6N8LcAAAAogAAAOEZ2x5Znvv4r8AAAZcAAAT4GhlYWQtUzq1AAAA4AAAADZoaGVhCbcFpwAAALwAAAAkaG10eJ8z//MAAAHsAAAAnGxvY2FeOGMwAAAGDAAAAFBtYXhwATkAdgAAARgAAAAgbmFtZenDj28AABo8AAAC93Bvc3Qe40M1AAAdNAAAAn8AAQAAA4D/gABcBgD/8///BeUAAQAAAAAAAAAAAAAAAAAAACcAAQAAAAEAAF6t07FfDzz1AAsEAAAAAADkLftbAAAAAOQt+1v/8/98BeUDhgAAAAgAAgAAAAAAAAABAAAAJwBqAAgAAAAAAAIAAAAKAAoAAAD/AAAAAAAAAAEAAAAKADAAPgACREZMVAAObGF0bgAaAAQAAAAAAAAAAQAAAAQAAAAAAAAAAQAAAAFsaWdhAAgAAAABAAAAAQAEAAQAAAABAAgAAQAGAAAAAQAAAAQEFQGQAAUAAAKJAswAAACPAokCzAAAAesAMgEIAAACAAUDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFBmRWQAwOYB5icDgP+AAAAD3ACEAAAAAQAAAAAAAAAAAAAAAAACBAAAAAQAAAAEAAAABAAAAAQAAAAEAP/zBTMAAAQAAAAEAAAABAAAAAQAAAAGAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAAAAAABQAAAAMAAAAsAAAABAAAAaAAAQAAAAAAmgADAAEAAAAsAAMACgAAAaAABABuAAAABAAEAAEAAOYn//8AAOYB//8AAAABAAQAAAAmACUACQAYABkAGgAbABwAHQAeAB8AIAAhACIAIwAkABcAFgAVABIAEwAUABEADwAQAA4ADQAMAAsACgAJAAgABAAFAAYABwADAAIAAQAAAQYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAAAAB5AAAAAAAAAAnAADmAQAA5gEAAAAmAADmAgAA5gIAAAAlAADmAwAA5gMAAAAJAADmBAAA5gQAAAAYAADmBQAA5gUAAAAZAADmBgAA5gYAAAAaAADmBwAA5gcAAAAbAADmCAAA5ggAAAAcAADmCQAA5gkAAAAdAADmCgAA5goAAAAeAADmCwAA5gsAAAAfAADmDAAA5gwAAAAgAADmDQAA5g0AAAAhAADmDgAA5g4AAAAiAADmDwAA5g8AAAAjAADmEAAA5hAAAAAkAADmEQAA5hEAAAAXAADmEgAA5hIAAAAWAADmEwAA5hMAAAAVAADmFAAA5hQAAAASAADmFQAA5hUAAAATAADmFgAA5hYAAAAUAADmFwAA5hcAAAARAADmGAAA5hgAAAAPAADmGQAA5hkAAAAQAADmGgAA5hoAAAAOAADmGwAA5hsAAAANAADmHAAA5hwAAAAMAADmHQAA5h0AAAALAADmHgAA5h4AAAAKAADmHwAA5h8AAAAJAADmIAAA5iAAAAAIAADmIQAA5iEAAAAEAADmIgAA5iIAAAAFAADmIwAA5iMAAAAGAADmJAAA5iQAAAAHAADmJQAA5iUAAAADAADmJgAA5iYAAAACAADmJwAA5icAAAABAAAAAAAiAEQAhgDaATQBpAHUAiACXgKiAtAC8AMeAz4DdAOWA8wEKASUBPgFOAXSBmgGvAceB2YHkgfEB+YIBAgkCEQI5AkyCWYJpgnECfAAAQAAAAACsALWABAAAAEmNDYyFwEWFAcBBiImNDcBAUwMGCIMAR4MDP7iDCIYDAEBAo0MIxkM/tQMIgz+1AwZIwwBDQABAAAAAANWAkAAEAAAATYyFhQHAQYiJwEmNDYyFwEDDQwjGQz+1AwiDP7UDBkjDAENAjQMGCIM/uIMDAEeDCIYDP7/AAIAAAAAAxsC0gAbACgAAAEeARQGBw4CDwEGIicuAicuATQ2NzY3NhcWByIOARQeATI+ATQuAQLIKCsrKBNUJSIFCBoIJSZVEygrKyg2SkhISpIbLhsbLjYuGxsuAncnZm9mJxNJJCYHCQkrJkkTJ2ZvZic1ExISE5oaLjUuGxsuNS4aAAIAAP+AA7YDgQAaADMAAAEuASIHBgcOARQWFx4CFxYyNz4CNz4BNCYBMjc+ATc+ATc2LgIHBgcOARcWFxYXFhcDNT6hrU9SPz1DQz0dhDs6DScMOzuDHT1DQ/6NFhUQIA4aIwYJHkZeLhwYKx0SESUbIRUWAwE9QiAhPj2frZ89HXM6Qw8PQztyHT2frZ/+RQYEDwoTNyAvWD4TEQoTI2s0Lh8WCgYBAAL/8/98BAEDhgAgADcAAAEUBwYHBgcGBwYnLgMnJicmNzY3Njc+ARcWFxYXFhclERQXFRQfAR4BPgE3NiYvARE0LgEOAQQADxYxKjtPYVVZP3lsWiAuERcdHEdJaUWWS3djYDo6Bv29Aw+HCRkZFAQGBgt7Fh8fEgGAPz1XSUAwQB4aBQMkQVo2TFdycG5YWTIgFwwRRUNnaHnF/u8KCQEVD4YJCAUQDA8eC3sBDBIbCQsbAAQAAP+NBTADdAALAC4APgBOAAAlISIGFBYzIT4BNCYBNDY7ATI2Ji8BLgEjISIGFREUFjMhMjc+AScmJyYjISIuAQUUBisBIiY9ATQ2OwEyFhUFFAYrASImPQE0NjsBMhYVBMf7nyY1NSYEYSU0NP3yEAv1CAkBB5o+kEv+GyQyMiQEEjsuKyoGByQSHf6pKkcp/gQaE1sTGhoTWxMaASIaE1sTGhoTWxMaRDVMNgE1SzUCOwsQDA4EZyorMyP91yQyHx5hMzYpFipGQxMaGhNbExoaE1sTGhoTWxMaGhMAAQAA/4wDIQN8ABgAAAkBFhUWBgcBBicmJyY1NDcJAS4BNz4CFgFJAbscAQ4O/kUUGxkPDhIBpf5cDwcKBxYbGwNq/mcaJhMiDf5RFAMCFBAVGRIBmgGEDigSDBAFCAAAAQAA/5sD+ANlADEAAAEjIgYXEyMnLgErASIOAR8BBwYeAjsBMjY/ATMDBhY7ATI2NxMzMj4BNC4BKwEDLgEBzD8TGAQ9xD8LIhMwDhYGB11dBQELEgowEyILP8Q9BBgTPxUlC7yxITghITghsbwLJQNlHRL+w1gQEREbDbm5CRURChEQWP7DEh0VEgFFIDhCOCABRRIVAAADAAD/ygO2AzYAAAARACIAAAEhFB4BMj4BNCcmJyYiBwYHBiU+AR4BDwEOAS8BJjQ+AR8BAgD+SnbL6st2PDpjZu5mYzo8AmQMIRkCC/wMIA2PDBYhDXIBgHXLdnbL7GZjOjw8OmNmIwwBFyEN/AwBCo8MIRkCCnIAAAAAAgAA/8kDtwM3ABQAKQAABSInJicmNDc2NzYyFxYXFhQHBgcGAyIHBgcGFBcWFxYyNzY3NjQnJicmAgB2ZmQ6PDw6ZGbsZmQ6PDs6Y2Z4ZFVTMTIyMVNVyFVTMTIyMVNVNjs6Y2fuZ2M6Ozs6Y2fvZmM6OwMlMjFTVchVUzEyMjFTVchVUzEyAAEAAP+YBeUDZgAWAAAFHgE2NwE+ASYHBgcJAS4BBgcOARQWFwHqGkRFGgMfHAM1Iycn/QT+1BpQUBkTFRUTPRkSEhkDFCI+LgIBH/2ZAToZEREZEjA0MBIAAAAAAQAAAAACgAJ2ABAAACUGFBYyPwE2NC8BJiIGFB8BAXEGDBMG6gYG6gYSDQbarwYSDAbgBhIG4AYMEgbRAAAAAAEAAP+AAy0DggAWAAABHgEPAQEGIicuAT8BCQEuAT8BPgEfAQMUFQMSBv5HF0EXFQMSBgGC/n4VAxIGFTsYBwG2FDsXBv5NFxcUOxcGAX0BfRQ7FwYVBBMGAAAAAQAA/9ID6QMrAA4AAAE3ASYiBhQXCQEGFBYyNwPKH/5gDSMZDQFj/p0NGSMNAWAeAaANGSQM/pz+nQwkGQ0AAAEAAAAAA2sC6wAbAAAJASY0NjIXCQE2MhYUBwkBFhQGIicJAQYiJjQ3AcX+3AwZIgwBJAEkDCIZDP7cASQMGSIM/tz+3AwiGQwBgAEkDCIZDP7cASQMGSIM/tz+3AwiGQwBJP7cDBkiDAAAAAEAAAAAA1YCRQAQAAA3BiImNDcBNjIXARYUBiInAfMMIxkMASwMIgwBLAwZIwz+8+EMGCIMAR4MDP7iDCIYDAEBAAAAAQAAAAADawLrABoAAAkBJjQ2MhcJATYyFhQHCQEWFAYiJwkBBi4BNwHi/roGDBEHAUYBRgcRDAb+ugFGBgwRB/66/roJGAYJAYABRgcRDAb+ugFGBgwRB/66/roHEQwGAUb+ugkGGAkAAAAAAwAAAAAD1gKLAAgAEQA/AAATMhYUBiImNDYhMhYUBiImNDYDMhcWFxYdARQGByM1NC4BIg4BFRchNzQuASIOAR0BIyImPQE0Nz4BMzY3PgEz6SU0NEo1NQJTJTU1SjQ0KTxBPSgqJBsEIThDOCIB/sYBIjhDOCEXEhogHV8uBSUiZC4BKzVLNjZLNTVLNjZLNQFgKyk+Qj1xGygBCyI5IiI5IgsLIjkiIjkiCxoTUC0pJS8rJiMrAAMAAP/AA8ADQAAzAEAATQAAATIWHQEeAhczMhYdARQGKwEOAgcVFAYrASImPQEuAicjIiY9ATQ2OwE+Ajc1NDYzFyIOARQeATI+ATQuAQcyHgEUDgEiLgE0PgECEA0TTYJTC0MNExMNQwtTgk0TDSANE02CUwtDDRMTDUMLU4JNEw0QRnVFRXWMdUVFdUYrSisrSlZKKytKA0ATDUMLU4JNEw0gDRNNglMLQw0TEw1DC1OCTRMNIA0TTYJTC0MNE8BFdYx1RUV1jHVFYCtKVkorK0pWSisAAAIAAAAAA6EDAAAnAEIAAAEyHgEdARQHDgEUFhcWHQEUDgEjISIuAT0BNDc+ATQmJyY9ATQ+ATMFJiIPAg4BHwEHBh4BPwEXFj4BLwE3NiYvAQMgIzsiDBkbHBgMIjsj/cAjOyIMGRsbGQwiOyMBOAokCi5YEwsNOwQDEiMUNjkTIRICBTsNCxNYAwAiOyOIDwoUO0E6FAoPaCM7IiI7I2gPChQ7QDsUCg+IIzsi0RERTRUEIQ9EPhMeCgkZGQgJHBU+RA8hBBUAAgAA/7cDyQNJABQAJwAAATIXFhcWFAcGBwYiJyYnJjQ3Njc2FyIGFREUHwIWMj4BLwE1NCYnAgB8amc9Pj49Z2r4amc9Pj49Z2p8DRQEA40KGhMBCoARDANIPj1navhqZz0+Pj1navhqZz0+wxMO/vsIBwSOCRMbCYH3DBMBAAAEAAAAAAOhAwAAJwBDAF4AaAAAATIeAR0BFAcOARQWFxYdARQOASMhIi4BPQE0Nz4BNCYnJj0BND4BMwUhIgYdAR4BFAYHFRQWMyEyNj0BLgE0Njc1NCYFHwEeAQ8BFxYOAS8BBwYuAT8BJyY2PwI2Mg8CFwc3Fyc3JwMgIzsiDBkbHBgMIjsj/cAjOyIMGRsbGQwiOyMCQP3AGyUfISEfJRsCQBslHyEhHyX+3S5YEwsNPAYCEiETOTYUIxIDBDsNCxNYLgokEiE/KgU6PAYrPwMAIjsjiA8KFDtBOhQKD2gjOyIiOyNoDwoUO0A7FAoPiCM7IkAlG3odTlZOHVobJSUbWh1OVk4dehslkU0VBCEPRD4UHQkIGRkJCh4TPkQPIQQVTRFfOA4wQhsaQDEOAAAAAAUAAAAAA70CYAAlAE8AWABhAGgAAAEUBgcOASImJyEOASImJy4BPQE0PwE+ATMhMhYfARYXMRYfAR4BBz4BNTQmLwIjIiY9ASMiBg8BMzIWFAYrARUUFhc+ATMyFhchPgEzMhYHMjY0JiIGFBYhMjY0JiIGFBYBFTMnLgEjA70yJws8TTwL/usLPE09CicyCi8OOSIBRhovEWYGCAcEXCo2XAoNFhFcCesdKNkOGAUhQg8UFA9UDQoNOyMmPAsBFQs8JiM7XhMbGyYbG/4kExsbJRsbAQyUSgYRCgEwKT8JJDAtJCQtMCQKPilUGBdoICUWE3cGBAMBEAhAUwYWDREaAxACKRxmDw1JFBwUQg0VByIoLSMjLSh5GyYbGyYbGyYbGyUbAWllVgcIAAIAAP/gA4ADAAAXADUAACU+ATU0JyYnJiIHBgcGFBcWFxYzMjY/ARcWDgIvAQ4BIyInJicmNDc2NzYyFxYXFhUUBwYHAtQlJywqSEuuS0gqLCwqSEtXOWosFLQJARIaCXUzgERnWlYzNDQzVlrPWVczNBgXLLEsajlXS0gqLCwqSEuuS0gqLCclEYcJGhIBCXUsLzQzV1nPWlYzNDQzVlpnRkA+MwAAAAQAAP/AA8ADQQAUACkANQA+AAABFAcGBwYiJyYnJjQ3Njc2MhcWFxYFFBcWFxYyNzY3NjQnJicmIgcGBwYFFRQGIiY9ATQ2MhYnMjY0JiIGFBYDwD08ZWj0aGU8PT08ZWj0aGU8PfzANDNXWtBaVzM0NDNXWtBaVzM0AaATGhMTGhMgFBwcKBwcAYB6aGU8PT08ZWj0aGU8PT08ZWh6aFpXMzQ0M1da0FpXMzQ0M1daSOANExMN4A0TE1McKBwcKBwAAAACAAD/xQOAA00AHgArAAABHgEUBgcGDwEGBwYPAQYiJy4CJy4BNDY3Njc2FxYHIg4BFB4BMj4BNC4BAxA1Ozs1FB1QFRAZLQgLIgszNHMaNTs7NUplYWFlxiU+JSU+Sj4lJT4C0jWLlos1ExlGExAYNAkMDDsyZBk1i5aLNUgaGRka0CU9ST4kJD5JPSUAAAACAAAAAAMBAmEADAAZAAABMhYVERQGIiY1ETQ2BRQGIyEiJjQ2MyEyFgIQFBwcKBwcAQQcFP6AFBwcFAGAFBwCYBwU/oAUHBwUAYAUHPAUHBwoHBwAAAAAAQAAAAADHQKeABwAAAEeAQ8BFxYUDgEvAQcGIi4BPwEnJjQ+AR8BNzYyAxALAgvW1AwXIg3W0wwjGQIL1tQMFyIN1tMNIwKQDCEN1tMMIxkCC9bUDBciDdbTDCMZAgvW1AwAAAEAAAAAAo8ClgAUAAABMDE1FQcGFBYyPwE2NC8BJiIGFBcCZPMGDBIG8w0N8QYRDQYBfgEB8QcRDAbzDSIL8wYMEgYAAQAAAAADVgJWAA0AABMmIgYUFwkBNjQmIgcB8wwjGQwBSQFJDBkjDP7zAkkMFyML/sUBOwsjFwz/AAAAAQAAAAACwAHOAA8AAAEzJyYOAR8BHgE/ATYuAQcB7iCoCBgICKgGEwepCQgYCAEHvQkFFwm9BwEIvQkYBQoAAAEAAAAAAwYCIQAPAAABNh4BBg8BBiIvAS4BPgEzAuIKEggEB+IJGwnjBwMHEAoCIAELEhQH4gkJ4gcTEgsAAAAIAAD/3gOjAyEAOABEAEYASABLAFQAXQBpAAABMhcWFxYXFhcWFREUBgcGBw4BIxceAQ4CJi8BIQcOAS4BPwEjIiYnJicuATURNDc2NzY3Njc2MwEyFhQGIyEiJjQ2MycjKQIzMSUiBhQWMjY0JiEiBhQWMjY0JgMjIg4BBxUhNTQuAQJTOy0rJUgmFAoKBAUJEAgVE0kGBAQNEBEGeP4ddwkbEwIISwQREwgQCQQECgkUJkglKy46ARMNExMN/oAOEhIORjsCO/4MAgA6/fAUHBwnHR0BjRQcHCcdHXDnP2tAAgLAQG0DIAoJFCZJJSstO/7hFBUJEAgFBEkGEREMBQUGd3cJAREaCkwEBQgQCRUUAR87LSslSSYUCQr9ABMaExMaE4CAHCgcHCgcHCgcHCgcAcA9aT97c0FsQAAAAAEAAP/gA6EDIQAyAAABMhYXEzMyHgEUDgErAQMOAQcjIiY3EyMHDgEHIyIuATQ/AScmND4BOwEyFh8BMwMmNjMBwxMfCaKYHDAcHDAcmKIIHBE8EBQDNKg2CBsPLgkPCgRPTwQKDwkpEB4JNqg0AxQQAyASEP7qHDA4MBz+6g8RAhkQAQ9LDQ8BCQ8RCJ+fCBEPCQ8OSwEPEBkAAAEAAAAAAyUCLAAdAAATNj8BMh8BFj8CJzc2HwEWPwE2FhcWBgcFBicmJ9UEBiUCAzMDA1kRhTUFBNMEBqgUIQMCDw/+SSAWCRMBNAECDgIgAgEgB80UAgSnBAI9CBQVDhgGoAwdDBcABQAA/+oDqwMWAAMABwALAA8AJwAAATUhFQU1IRUFNSEVBTUhFQURNCYrASIGFREjETQmIyEiBhURIxUhNQEVAQD/AAEA/wABAP8AAQABawwJgAkNKhkS/isSGSsDVgJAKyuAKyuAKyuAKyurAesJDAwJ/hUC1hEZGRH9KioqAAABAAD/gAMhA4AADQAAEyY0NjIXCQEGIiY0NwHyEiM0EQHY/igRNCMSAYEDExM0JhP+E/4TEyY0EwGTAAABAAD/4AOhAyEAGwAAASEyFhQGIyERFAYiJjURISImNDYzIRE0NjIWFQIgAWANExMN/qATGhP+oA0TEw0BYBMaEwGgExoT/qANExMNAWATGhMBYA0TEw0AAAASAN4AAQAAAAAAAAATAAAAAQAAAAAAAQAUABMAAQAAAAAAAgAHACcAAQAAAAAAAwAUAC4AAQAAAAAABAAUAEIAAQAAAAAABQALAFYAAQAAAAAABgAUAGEAAQAAAAAACgArAHUAAQAAAAAACwATAKAAAwABBAkAAAAmALMAAwABBAkAAQAoANkAAwABBAkAAgAOAQEAAwABBAkAAwAoAQ8AAwABBAkABAAoATcAAwABBAkABQAWAV8AAwABBAkABgAoAXUAAwABBAkACgBWAZ0AAwABBAkACwAmAfNDcmVhdGVkIGJ5IGljb25mb250ZmxpZ2d5LWhvbWUtaWNvbmZvbnRSZWd1bGFyZmxpZ2d5LWhvbWUtaWNvbmZvbnRmbGlnZ3ktaG9tZS1pY29uZm9udFZlcnNpb24gMS4wZmxpZ2d5LWhvbWUtaWNvbmZvbnRHZW5lcmF0ZWQgYnkgc3ZnMnR0ZiBmcm9tIEZvbnRlbGxvIHByb2plY3QuaHR0cDovL2ZvbnRlbGxvLmNvbQBDAHIAZQBhAHQAZQBkACAAYgB5ACAAaQBjAG8AbgBmAG8AbgB0AGYAbABpAGcAZwB5AC0AaABvAG0AZQAtAGkAYwBvAG4AZgBvAG4AdABSAGUAZwB1AGwAYQByAGYAbABpAGcAZwB5AC0AaABvAG0AZQAtAGkAYwBvAG4AZgBvAG4AdABmAGwAaQBnAGcAeQAtAGgAbwBtAGUALQBpAGMAbwBuAGYAbwBuAHQAVgBlAHIAcwBpAG8AbgAgADEALgAwAGYAbABpAGcAZwB5AC0AaABvAG0AZQAtAGkAYwBvAG4AZgBvAG4AdABHAGUAbgBlAHIAYQB0AGUAZAAgAGIAeQAgAHMAdgBnADIAdAB0AGYAIABmAHIAbwBtACAARgBvAG4AdABlAGwAbABvACAAcAByAG8AagBlAGMAdAAuAGgAdAB0AHAAOgAvAC8AZgBvAG4AdABlAGwAbABvAC4AYwBvAG0AAAIAAAAAAAAACgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJwECAQMBBAEFAQYBBwEIAQkBCgELAQwBDQEOAQ8BEAERARIBEwEUARUBFgEXARgBGQEaARsBHAEdAR4BHwEgASEBIgEjASQBJQEmAScBKAAXeGlhb2h1YW5ndGlhb2ppYW50b3UtY3UNc2hvdXFpamlhbnRvdRFhLUZyYW1lMTMxMjMyMzczMgVHcm91cAhhLUdyb3VwMQhhLUdyb3VwMghhLUdyb3VwMwdWZWN0b3IxCnh1YW56aG9uZzEMd2VpeHVhbnpob25nBlZlY3Rvcg9qaWFudG91LWJpYW9kYW4RamlhbnRvdS13ZW56aWxpYW4RamlucnVqaWFudG91MnhpYW8LeXV5aW5ndWFuYmkRc2hvdXFpamlhbnRvdXhpYW8OX2d1YW5iaWppYW50b3UKenVjaGUtbWlhbhBkaXR1ZGluZ3dlaS1taWFuDG1lbnBpYW8tbWlhbjJhLU8xQ04wMXQ3S3F3RjFIWEtTZ291NTZDXzYwMDAwMDAwMDA3NjctNTUtdHBzLTI4LQdtZW5waWFvBXp1Y2hlBnNvdXN1bwV4aW54aQt3ZWl6aGktbWlhbgZqaWFoYW8GZ3VhbmJpDWppbnJ1amlhbnRvdTIRX3hpYWxhamlhbnRvdXhpYW8OemhhbmthaWppYW50b3UIeGlhLW1pYW4PaHVvY2hlcGlhby1taWFuC2ppcGlhby1taWFuCmZlaWppemhvbmcJX2ppdWRpYW4yEWppbnJ1amlhbnRvdTN4aWFvHmJ1ZGFpcXVhbmppYW9yemVuZ2ppYW9yeGluemVuZwAAAA==");
      }
      html,
      body {
        -ms-overflow-style: scrollbar;
        -webkit-tap-highlight-color: transparent;
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
      }
      body {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0;
        font-family: BlinkMacSystemFont, 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      }
      input[type="search"]::-webkit-search-decoration,
      input[type="search"]::-webkit-search-cancel-button {
        -webkit-appearance: none !important;
      }
      body [web-sticky] {
        position: -webkit-sticky !important;
        position: sticky !important;
        user-select: none;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
      }
      img {
        user-select: none;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
      }
      .rax-view-v2 {
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        display: -webkit-flex;
        display: -moz-box;
        display: flex;
        -webkit-flex-direction: column;
        -moz-box-orient: vertical;
        -moz-box-direction: normal;
        flex-direction: column;
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        -webkit-align-content: flex-start;
        align-content: flex-start;
        border: 0 solid #000;
        margin: 0;
        padding: 0;
        min-width: 0
      }
      .rax-text-v2 {
        box-sizing: border-box;
        display: block;
        font-size: 4.26667vw;
        white-space: pre-wrap;
      }
      .rax-text-v2--overflow-hidden {
        overflow: hidden;
      }
      .rax-text-v2--singleline {
        white-space: nowrap;
      }
      .rax-text-v2--multiline {
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      .rax-scrollview-vertical {
        -webkit-flex-direction: column;
        flex-direction: column;
      }
      .rax-scrollview-horizontal {
        -webkit-flex-direction: row;
        flex-direction: row;
      }
      .rax-scrollview-content-container-horizontal {
        -webkit-flex-direction: row;
        flex-direction: row;
      }
      .rax-scrollview-webcontainer {
        display: block
      }
    </style>
    <script>
    try {
      var kvData = window && window.__megability_bridge__ && window.__megability_bridge__.syncCall('userKVStorage', 'getItem', {
        bizID: 'FZ_MINIAPP_HOME_FCC',
        key: 'first_chunk_cache',
      });
      window.fccData = {};

      try {
        window.fccData = JSON.parse(kvData && kvData.data && kvData.data.result);
      } catch {}

      var head = window.fccData.head || '';
      var body = window.fccData.body || '';

      if (head && body) {
        document.head.innerHTML = head;
        document.body.innerHTML = body;
      }

      setTimeout(function(){
        if (!window._er_next_part_render && ${isDebug ? 'false' : 'true'}) {
          location.replace("${newUrl.toString()}")
        }
      }, ${isPre ? 10000 : 5000});
    } catch {}
  </script>`)
}