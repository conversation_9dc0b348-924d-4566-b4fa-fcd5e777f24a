// PHA首页定制测试，获取最后一屏HTML文档
export default async function (data) {
  const {
    ssrHtmlRes = '',
    timeLog = {},
    writer,
    writeHtml = () => {},
  } = data;

  try {
    if (ssrHtmlRes && ssrHtmlRes.status !== 200) {
      throw new Error('302');
    }

    const reader = ssrHtmlRes.body.getReader();

    timeLog.timeStamp[`html_stringify_start`] = Date.now();
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        timeLog.timeStamp[`html_stringify_end`] = Date.now();
        await writeHtml(`<script>
          window._er_next_part_render = true;
          if(window.performance && typeof window.performance.mark == 'function'){
            window.performance.mark('stage-second-chunk')
          }
          if (window._config_ && (window._config_.renderTo || window._config_.url && window._config_.url.indexOf('_____tmd_____') > -1)) {
            console.log('命中霸下拦截');
            const fccRootCache = document.querySelector('#fcc_root_cache');
            const ssrRrHolder = document.querySelector('#ssr-er-holder');
            
            if (fccRootCache) {
              fccRootCache.remove();
            }

            if (ssrRrHolder) {
              ssrRrHolder.remove();
            }
          }
          console.log('流式返回生效');
          console.log('SSR 函数端到端耗时：${timeLog && timeLog.faas_duration || 'null'}');
          console.log('SSR 函数执行耗时：${timeLog && timeLog.ssr_duration || 'null'}');
        </script>`);
        break;
      };
      
      await writer.write(value);
    }
  } catch (err) {
    await Promise.resolve(err && err.message || '');
    throw new Error(`getLastPartHtmlError:${err.message}`);
  }
}
