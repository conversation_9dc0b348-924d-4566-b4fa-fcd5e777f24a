import {
  ROOT_BEFORE_START_REG,
  ROOT_START_REG,
  SECOND_STREAM_END
} from '../../constant/rax';

// 获取第二屏HTML文档
export default async function (data) {
  const {
    ssrHtml = '',
  } = data;
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || []; // '<div id="root"';
  const [secondPartEndTag] = ssrHtml.match(SECOND_STREAM_END) || []; // '<div id="__SECOND_STREAM_END__"></div>'

  if (rootTag && secondPartEndTag) {
    const startPosition = ssrHtml.indexOf(rootTag);
    const endPosition = ssrHtml.indexOf(secondPartEndTag);
    let html = ssrHtml.slice(startPosition, endPosition);
    html += html ?
      `<script data-id="second-part-success">
        window._er_second_part = Date.now();
        var firstErHolder = document.getElementById('ssr-er-holder');
        var erAddHolder = document.getElementById('ssr-er-additional-holder');
        if (firstErHolder) {
          firstErHolder.style.display = "none";
        }
        if (erAddHolder) {
          erAddHolder.style.display = "none";
        }
      </script>` : '';

    return html;
  }
  return '';
}
