import {
  SECOND_STREAM_END,
  ROOT_BEFORE_START_REG,
  ROOT_START_REG,
  SERVER_START_REG
} from '../../constant/rax';

// [debug]获取最后一屏HTML文档
export default async function (data) {
  const {
    ssrHtml = '',
    firstPartCacheHolder = '',
    erConfigTemplate = '',
    isDebug,
    useThreePartStream,
    isTest,
    timeLog,
  } = data;

  const [secondPartEndTag] = ssrHtml.match(SECOND_STREAM_END) || []; // '<div id="__SECOND_STREAM_END__"></div>'
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || []; // '<div id="root"';
  const [serverTag] = ssrHtml.match(SERVER_START_REG) || []; // <script data-from="server"

  // 如果是三段流式，则从【<div id="__SECOND_STREAM_END__"></div>】开始分割
  // 如果是二段流式，则从【<div id="root"】开始分割
  const startTag = useThreePartStream ? secondPartEndTag : rootTag;

  if (startTag && serverTag) {
    const startPosition = ssrHtml.indexOf(startTag);
    const serverPosition = ssrHtml.indexOf(serverTag);
    const list = [
      ssrHtml.slice(startPosition, serverPosition),
      `<script data-id="last-part-success">
        window._er_next_part_render = true;
        window._er_last_part = Date.now();
        var lastErHolder = document.getElementById('${useThreePartStream ? '__SECOND_STREAM_HOLDER__' : 'ssr-er-holder'}');
        var erAddHolder = document.getElementById('ssr-er-additional-holder');
        if (lastErHolder) {
          lastErHolder.remove();
        }
        if (erAddHolder) {
          erAddHolder.remove();
        }
      </script>`,
      `<script>
        if(window.performance && typeof window.performance.mark == 'function'){
          window.performance.mark('stage-second-chunk')
        }
        console.log('SSR 函数端到端耗时：${timeLog && timeLog.faas_duration || 'null'}');
        console.log('SSR 函数执行耗时：${timeLog && timeLog.ssr_duration || 'null'}');
      </script>`,
    ];
    if (isDebug) {
      const debugInfo = await getDebugInfo({
        ssrHtml,
        firstPartCacheHolder,
        erConfigTemplate,
      });
      list.push(debugInfo);
    }
    list.push(ssrHtml.slice(serverPosition));

    return list.join('');
  }

  throw new Error('getLastPartHtml error');
}

// [debug]获取debug数据
async function getDebugInfo(data) {
  const {
    ssrHtml,
    firstPartCacheHolder,
    erConfigTemplate,
  } = data;

  return `
  <script id="debug">
    window._er_debug_message = {
      ssrHtml: decodeURIComponent("${encodeURIComponent(ssrHtml || '')}"),
      firstPartCacheHolder: decodeURIComponent("${encodeURIComponent(firstPartCacheHolder || '')}"),
      erConfigTemplate: decodeURIComponent("${erConfigTemplate || ''}"),
    }
  </script>
  `;
}