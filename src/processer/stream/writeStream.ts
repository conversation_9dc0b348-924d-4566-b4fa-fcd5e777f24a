import {
  getfirstPart<PERSON>ache<PERSON>ey,
  getHost,
  getJsonCache,
  SlsLog,
  getPageName
} from "../../utils/rax";
import getSsrHtml from "./getSsrHtml";
import sendFirstPartHtml from "./sendFirstPartHtml";
import getSecondPartHtml from "./getSecondPartHtml";
import getLastPartHtmlForHome from "./getLastPartHtmlForHome";
import getSsrHtmlStream from "./getSsrHtmlSteam";
import getLastPartHtml from "./getLastPartHtml";
import setFristPartCache from "./setFristPartCache";
import sendFirstChunkCache from './sendFirstChunkCache';
import { STREAM_FIRST_SCREEN_STATIC_CONFIG } from "../../constant/firstPartMap";
import { PRE_RAX_SSR_HOST, RAX_SSR_HOST, NEW_PRE_RAX_SSR_HOST, NEW_RAX_SSR_HOST } from "../../constant/rax";

// 通用流式入口
export default async function (opt) {
  const {
    writer = {},
    pageErConfig = {},
    titleBarOpt = {},
    reqUrl,
    ssrUrl = '',
    secondPartSsrUrl = '',
    raxUrl = '',
    request,
    _startTime,
    isPre,
    isGray,
    isDebug,
    isTest,
    useFcc,
    isHomeTest,
    disableCache,
    onlyShowFirstPart,
    onlyShowSecondPart,
    useThreePartStream,
    timeLog,
    event,
    isStreamConfig
  } = opt;
  timeLog.timeStamp[`er_cache_start`] = Date.now();

  const encoder = new TextEncoder();
  const writeHtml = (e) => writer.write(encoder.encode(e));

  // 避免预发切换分支时，首屏版本和当前版本不一致，导致上下文件版本差异
  const projectVersionWrong = isPre && reqUrl.searchParams.get('projectVersion') && pageErConfig?.version !== reqUrl.searchParams.get('projectVersion');

  // er配置的缓存更新时间
  const erConfigUpdateTime = pageErConfig.updateTime || 0;

  // 获取第一屏数据缓存
  const firstPartCacheKey = getfirstPartCacheKey(reqUrl, pageErConfig.cacheKey, isGray);
  // 缓存版本、缓存更新时间、缓存内容
  let firstPartCacheVersion = '';
  let firstPartCacheUpdateTime = 0;
  let firstPartCacheHolder = '';

  let requestCombo: any = {}

  // 是否使用流式加速模式
  const isUseStreamFirstStaticMode = reqUrl.searchParams.get('useStaticStream') && STREAM_FIRST_SCREEN_STATIC_CONFIG[reqUrl.pathname]?.[pageErConfig.version]?.[isPre?'pre':'online'];

  try {
    const timeoutDuration = isStreamConfig ? 70 : 50;
    const startTimer = Date.now();
    // 流式所有请求，统一发出(流式首屏由于ER的稳定性问题，额外做了oss的备份)
    requestCombo = {
      first: (useFcc || isUseStreamFirstStaticMode) ? Promise.resolve({}) : Promise.race([
        getJsonCache(firstPartCacheKey, isPre),
        new Promise(async (resolve, reject) => {
          // 通过oss获取兜底首屏，提前回的话，就会替代er-cache内容
          try{
            const envPreTag = firstPartCacheKey.indexOf("?") === -1 ? `?envTag=pre` : `&envTag=pre`
            const cacheKey = `http://${isPre ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}${firstPartCacheKey}${isPre ? envPreTag : ''}`
            const { groupName, serviceName } = getPageName(ssrUrl);
            const newFirstPartCacheKey = cacheKey.slice(cacheKey.indexOf('http://') + 7).replace(/\//g, '-');
            const result = await fetch(`https://cdnoss.fliggy.com/stream/pages/${groupName}-${serviceName}/${newFirstPartCacheKey}.html`,{cdnProxy: true,decompress: "manual"}).then(r=> r&& r.json()).catch(e=> {return {}})
            if(result.url){
              timeLog.ossTime = Date.now() - startTimer; 
              timeLog.ossUrl = `https://cdnoss.fliggy.com/stream/pages/${groupName}-${serviceName}/${newFirstPartCacheKey}.html`;
              resolve({data: result, source: 'oss'})
            }
          }catch(e){}
        }),
        new Promise((resolve, reject) => {setTimeout(() => {resolve({})}, timeoutDuration)})
      ]),
      next: isHomeTest ? getSsrHtmlStream(ssrUrl, request, timeLog, '', isPre, useFcc) : getSsrHtml(useThreePartStream ? secondPartSsrUrl : ssrUrl, request, timeLog, '', isPre),
      last: useThreePartStream ? getSsrHtml(ssrUrl, request, timeLog, 'three_', isPre) : null
    }

    // 获取第一屏缓存，50ms超时

    let firstPartCacheData: any = isUseStreamFirstStaticMode? {
      data: {
        ...pageErConfig,
        holder: STREAM_FIRST_SCREEN_STATIC_CONFIG[reqUrl.pathname]?.[pageErConfig.version]?.[isPre?'pre':'online']
      }
    } : await requestCombo.first;
    timeLog.totalTime = Date.now() - startTimer

    timeLog.firstScreenSource = isUseStreamFirstStaticMode ? 'static' : (firstPartCacheData.source || 'cache');
    
    firstPartCacheData = firstPartCacheData.data;
    firstPartCacheVersion = firstPartCacheData.version;
    firstPartCacheUpdateTime = firstPartCacheData.updateTime || 0;
    firstPartCacheHolder = decodeURIComponent(firstPartCacheData.holder);

    timeLog.totalHolder = JSON.stringify(firstPartCacheData)

  } catch (e) { 
    timeLog.failed_get_cache = true;
    timeLog.failed_msg = e?.message || '其他错误';
  }

  
  try {
    // 如果 使用缓存 且 成功获取第一屏数据缓存 且 版本号一致 且 缓存更新时间大于er配置的时间，则返回首屏数据
    if ((useFcc || isUseStreamFirstStaticMode) || 
      (
        !disableCache &&
        firstPartCacheHolder &&
        (firstPartCacheVersion === pageErConfig.version) &&
        firstPartCacheUpdateTime > erConfigUpdateTime &&
        !projectVersionWrong
      )
    ) {
      // 如果是三段流式，则先异步请求完整的html文档
      let getCompleteSsrHtml: any = () => { };
      if (useThreePartStream) {
        getCompleteSsrHtml = requestCombo.last;
      }

      // 1. 写一屏骨架 + 获取ssr文档
      timeLog.timeStamp[`first_part_start`] = Date.now();
      const [writeReason, ssrHtml] = await Promise.all([
        useFcc ? sendFirstChunkCache(writeHtml, raxUrl, isDebug, isPre) : sendFirstPartHtml(firstPartCacheHolder, raxUrl, _startTime, titleBarOpt, writeHtml, timeLog, isDebug, isPre),
        requestCombo.next
      ]);
      
      timeLog.timeStamp[`other_part_start`] = Date.now();

      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = (function() {
        if (!ssrHtml) return false;

        if (typeof ssrHtml === 'string') {
          return ssrHtml.indexOf('<body') > -1;
        } else if (typeof ssrHtml === 'object') {
          return true;
        } else {
          return false;
        }
      })();
      if (!ssrHtmlValid) {
        if ((ssrHtml || '').includes('ssrRedirectUrl')) {
          throw new Error(ssrHtml);
        }
        throw new Error(`ssrHtml invalid(use cache)|${ssrHtml}`);
      }

      // 只返回第一屏数据，debug使用
      if (onlyShowFirstPart) {
        await writeHtml('</body></html>');
        await writer.close();
        return;
      }

      // 2. 如果是三段流式，则返回二屏数据
      if (useThreePartStream) {
        timeLog.timeStamp[`second_part_start`] = Date.now();
        const secondPartHtml = await getSecondPartHtml({
          ssrHtml,
        });

        await writeHtml(secondPartHtml);
        timeLog.timeStamp[`second_part_duration`] = Date.now() - timeLog.timeStamp[`second_part_start`];

        // 只返回第一屏 + 第二屏数据，debug使用
        if (onlyShowSecondPart) {
          await writer.close();
          return;
        }
      }

      // 如果是三段流式，则最多延迟5s，等待完整的ssr文档返回
      let completeSsrHtml: any = '';
      if (useThreePartStream) {
        completeSsrHtml = await Promise.race([
          getCompleteSsrHtml,
          new Promise((resolve, reject) => {
            setTimeout(() => {
              resolve('getCompleteSsrHtml timeout 5000');
            }, isPre ? 10000 : 5000);
          })
        ]);

        // 如果完整的ssr文档为空，则抛出错误，强制回源
        const completeSsrHtmlValid = completeSsrHtml && completeSsrHtml.indexOf('<body') > -1;
        if (!completeSsrHtmlValid) {
          throw new Error(`completeSsrHtml invalid(use cache)|${completeSsrHtml}`);
        }
      }

      // 首页定制：如果首页url增加_home_er_test，则直接返回response流
      if (isHomeTest || useFcc) {
        await getLastPartHtmlForHome({
          ssrHtmlRes: ssrHtml,
          timeLog,
          writer,
          writeHtml,
        });
      } else {
        timeLog.timeStamp[`last_part_start`] = Date.now();
        // 3. 返回最后一屏数据
        const lastPartHtml = await getLastPartHtml({
          ssrHtml: useThreePartStream ? completeSsrHtml : ssrHtml,
          firstPartCacheHolder,
          erConfigTemplate: pageErConfig.template,
          isDebug,
          useThreePartStream,
          isTest,
          timeLog,
        });

        await writeHtml(lastPartHtml);
        timeLog.timeStamp[`last_part_duration`] = Date.now() - timeLog.timeStamp[`last_part_start`];
      }

      timeLog.timeStamp[`other_part_end`] = Date.now();
    } else if (disableCache && isDebug) {

      // 日常开发 或者 查问题使用。
      const ssrHtml = await getSsrHtml(ssrUrl, request, timeLog, '', isPre);
      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = ssrHtml && ssrHtml.indexOf('<body') > -1;
      if (!ssrHtmlValid) {
        throw new Error(`ssrHtml invalid(disable cache)|${ssrHtml}`);
      }

      // 获取一屏骨架图
      const firstPartHolder = await setFristPartCache({
        ssrHtml,
        pageErConfig,
        firstPartCacheKey,
        isPre,
        ssrUrl,
        writeHtml
      });

      // 如果骨架图解析失败，则抛出异常
      if (!firstPartHolder) {
        throw new Error('decode erConfig template error');
      }

      // 返回一屏骨架图
      await writeHtml(firstPartHolder);

      // 只返回第一屏数据
      if (onlyShowFirstPart) {
        await writeHtml('</body></html>');
        await writer.close();
        return;
      }

      // 如果是三段流式，则返回二屏数据
      if (useThreePartStream) {
        const secondSsrHtml = await getSsrHtml(secondPartSsrUrl, request, timeLog, 'three_', isPre);
        const secondPartHtml = await getSecondPartHtml({
          ssrHtml: secondSsrHtml,
        });

        await writeHtml(secondPartHtml);

        // 只返回第一、第二屏数据
        if (onlyShowSecondPart) {
          await writer.close();
          return;
        }
      }

      // 返回最后一屏数据
      const lastPartHtml = await getLastPartHtml({
        ssrHtml,
        firstPartCacheHolder: firstPartHolder,
        erConfigTemplate: pageErConfig.template,
        isDebug,
        timeLog,
      });

      await writeHtml(lastPartHtml);
    } else {
      // 当第一屏数据缓存获取失败 或 版本号不一致 或 缓存更新时间小于等于er配置时间，则请求ssr文档
      const ssrHtml = (!isHomeTest && !useThreePartStream) ?await requestCombo.next : await getSsrHtml(ssrUrl, request, timeLog, '', isPre);
      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = ssrHtml && ssrHtml.indexOf('<body') > -1;
      if (!ssrHtmlValid) {
        if ((ssrHtml || '').includes('ssrRedirectUrl')) {
          throw new Error(ssrHtml);
        }
        throw new Error(`ssrHtml invalid(no cache)|${ssrHtml}`);
      }

      // 返回完整ssr文档
      await writeHtml(ssrHtml);

      // 存入首屏缓存
      await setFristPartCache({
        ssrHtml,
        pageErConfig,
        firstPartCacheKey,
        isPre,
        ssrUrl,
        writeHtml
      });
    }
  } catch (e) {
    // 如果catch，则跳转market页面
    if (isDebug) {
      await writeHtml(`<div id="error">${e && e.message}</div></body></html>`)
    } else {
      SlsLog.sendLog({
        event,
        reqUrl,
        logConfig: {
          logName: 'er_stream_log',
          logType: 'error',
          logData: {
            slsNew: true,
            reqUrl: reqUrl.toString(),
            errorMsg: (e && e.message) || JSON.stringify(e),
            ...timeLog
          }
        }
      })
      const newUrl = e && e.message && e.message.includes('ssrRedirectUrl') ?
        new URL(e.message.replace('ssrRedirectUrl:', '')) : new URL(raxUrl);
      newUrl.searchParams.set('_er_failback', '1');
      newUrl.searchParams.set('ssr_fail_back', '1');

      const wxNewUrl = new URL(raxUrl);
      wxNewUrl.searchParams.set('_er_failback', '1');
      wxNewUrl.searchParams.set('ssr_fail_back', '1');

      await writeHtml(
        `<script>
        if (window.location.hostname.includes('proxy')) {
          location.replace("${wxNewUrl.toString()}")
        } else {
          location.replace("${newUrl.toString()}")
        }
        </script></body></html>`
      );
    }
  }

  // 打日志
  SlsLog.sendLog({
    event,
    reqUrl,
    logConfig: {
      logData: {
        ...timeLog,
        timeStamp: {
          ...timeLog.timeStamp,
          er_end: Date.now(),
          stream_end: Date.now(),
        },
        er_duration: Date.now() - timeLog.timeStamp.er_start,
        stream_duration: Date.now() - timeLog.timeStamp.stream_start,
        er_config_duration: timeLog.timeStamp.er_config_end - timeLog.timeStamp.er_config_start,
        er_cache_duration: timeLog.timeStamp.er_cache_end - timeLog.timeStamp.er_cache_start,
        html_stringify_duration: timeLog.timeStamp.html_stringify_end - timeLog.timeStamp.html_stringify_start,
        first_part_duration: timeLog.timeStamp.first_part_end - timeLog.timeStamp.first_part_start,
        other_part_duration: timeLog.timeStamp.other_part_end - timeLog.timeStamp.other_part_start,
      }
    }
  })

  await writer.close();
}
