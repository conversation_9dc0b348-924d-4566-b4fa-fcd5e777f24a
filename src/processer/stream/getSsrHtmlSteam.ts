// 获取SSR文档
export default async function (ssrUrl, request, timeLog, logPrefix = '', isPre, useFcc) {
  try {
    timeLog.timeStamp[`${logPrefix}faas_req`] = Date.now();
    const newssrUrl = useFcc 
      ? ssrUrl.indexOf('?') > -1 ? `${ssrUrl}&useFccFromEr=1` : `${ssrUrl}?useFccFromEr=1`
      : ssrUrl.indexOf('?') > -1 ? `${ssrUrl}&onlyBodyHtml=1` : `${ssrUrl}?onlyBodyHtml=1`;
    const res: any = await Promise.race([
      fetch(newssrUrl, {
        headers: request.headers,
      }),
      new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve('get faas request is timeout.Timeout values is 4000');
        }, isPre ? 10000 : 5000);
      })
    ]).catch(err => {
      return `getSecondHtmlError:${err.message || ''}`;
    });
    if (res.headers && res.headers.get('X-Ssr-Start') && res.headers.get('X-Ssr-End')) {
      timeLog.timeStamp[`${logPrefix}faas_res`] = Date.now();
      timeLog[`${logPrefix}faas_duration`] = timeLog.timeStamp[`${logPrefix}faas_res`] - timeLog.timeStamp[`${logPrefix}faas_req`];
      timeLog.timeStamp[`${logPrefix}ssr_start`]  = parseInt(res.headers.get('X-Ssr-Start'));
      timeLog.timeStamp[`${logPrefix}ssr_end`]  = parseInt(res.headers.get('X-Ssr-End'));
      timeLog[`${logPrefix}ssr_duration`] = timeLog.timeStamp[`${logPrefix}ssr_end`] - timeLog.timeStamp[`${logPrefix}ssr_start`];
      timeLog[`${logPrefix}faas_req_duration`]  = timeLog.timeStamp[`${logPrefix}ssr_start`] - timeLog.timeStamp[`${logPrefix}faas_req`];
      timeLog[`${logPrefix}faas_res_duration`]  = timeLog.timeStamp[`${logPrefix}faas_res`] - timeLog.timeStamp[`${logPrefix}ssr_end`];
    }

    if (res.headers && res.headers.get('X-Redirect-Url')) {
      return `ssrRedirectUrl:${res.headers.get('X-Redirect-Url')}`;
    }

    return res;
  } catch (err) {
    return (err && err.message) || JSON.stringify(err);
  }
}
