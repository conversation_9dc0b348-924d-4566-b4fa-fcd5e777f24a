import {
  getMetaData,
  setJsonCache,
  parseTemplate
} from "../../utils/rax";
import getFirstPartHtml from "./getFirstPartHtml";
import { PRE_RAX_SSR_HOST, RAX_SSR_HOST } from "../../constant/rax";

// [debug]设置首屏缓存
export default async function ({ ssrHtml, pageErConfig = {version: '', template: ''}, firstPartCacheKey, isPre = false, ssrUrl, writeHtml}) {
  // 获取ssr文档中，骨架图数据
  let holderData = getMetaData(ssrHtml, 'er-cache-data');
  const ssrVersion = getMetaData(ssrHtml, 'alitrip-project-version');

  // 如果ssr版本 和 er后台配置的版本 不一致，则不缓存
  if (ssrVersion !== pageErConfig.version) {
    return false;
  }

  try {
    holderData = decodeURIComponent(decodeURIComponent(holderData));
    holderData = JSON.parse(holderData);
  } catch (err) {
    holderData = {};
  }

  // 骨架图HTML
  let holderTemp = '';
  try {
    holderTemp = decodeURIComponent(pageErConfig.template);
  } catch (err) {
    holderTemp = '';
  }

  // er配置解析失败
  if (!holderTemp) {
    return false;
  }

  /**
   * iOS 上 HTTP 已经返回第一个 chunk，但是流式渲染不生效（不上屏）。
   * 这是ios浏览器特性，在需要上屏的 HTML 片段中增加真实的文本节点 + 图片节点，
   * 文本样式设为 font-size: 0，不能 display: none。图片节点设置opacity: 0，可以触发浏览器立即执行渲染策略
   * 所以需要在骨架图上拼接一个图片和文字
   * */
  const str = `<div id="ssr-er-additional-holder"><div style="font-size:0;">%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D</div><img src="https://gw.alicdn.com/imgextra/i1/O1CN01MwAFGF1wu8c1qURA5_!!6000000006367-2-tps-42-42.png" style="opacity:0;width:100vw;position:absolute;" /></div>`;
  holderTemp += str;

  // 拼接数据和骨架图
  const holderHtmlData = parseTemplate(holderTemp, holderData);

  const firstPartHtml = getFirstPartHtml(ssrHtml);

  // 拼接 <head> + 骨架图
  const holder = firstPartHtml + holderHtmlData.data;

  // 存入缓存（暂定3天）
  await setJsonCache(firstPartCacheKey, {
    version: pageErConfig.version,
    updateTime: Date.now(),
    holder: encodeURIComponent(holder),
  }, 3 * 24 * 60 * 60, isPre);


  const envPreTag = firstPartCacheKey.indexOf("?") === -1 ? `?envTag=pre` : `&envTag=pre`
  const cacheKey = `http://${isPre ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}${firstPartCacheKey}${isPre ? envPreTag : ''}`;

  const STREAM_OSS = `https://${isPre ? 'pre-rax-stream.wapa.fliggy.com' : 'rax-stream.m.fliggy.com'}/set_stream_oss`;
  try{
    // 存入oss
    const tt = await fetch(STREAM_OSS,{
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: ssrUrl,
        firstPartCacheKey: cacheKey,
        version: pageErConfig.version,
        updateTime: Date.now(),
        holder: encodeURIComponent(holder),
      }),
      mode: 'cors'
    })
  
  }catch(e){}

  return holder;
}
