
import {
  ROOT_BEFORE_START_REG,
  ROOT_START_REG,
  SERVER_START_REG
} from '../../constant/rax';

export default async function (opt) {
  const {
    ssrHtml,
  } = opt;
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || []; // '<div id="root"';
  const [secondPartEndTag] = ssrHtml.match(SERVER_START_REG) || [];

  if (rootTag && secondPartEndTag) {
    const startPosition = ssrHtml.indexOf(rootTag);
    const endPosition = ssrHtml.indexOf(secondPartEndTag);
    let secondPartDomHtml = ssrHtml.slice(startPosition, endPosition);
    const t2TagScript = `<script>window.__ssr__t2 = Date.now();</script>`;
    
    // 拼接JS
    secondPartDomHtml += t2TagScript;

    secondPartDomHtml += secondPartDomHtml ?
      `<script data-id="second-part-success">
        window._er_second_part = Date.now();
        var firstErHolder = document.getElementById('ssr-er-holder');
        var erAddHolder = document.getElementById('ssr-er-additional-holder');
        if (firstErHolder) {
          firstErHolder.style.display = "none";
        }
        if (erAddHolder) {
          erAddHolder.style.display = "none";
        }
      </script>` : '';

    return secondPartDomHtml;
  }
  return '';
}