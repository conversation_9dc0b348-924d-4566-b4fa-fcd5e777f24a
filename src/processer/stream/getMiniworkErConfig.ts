// 获取ssr后台的流式配置
export default async function (projectName, pageName) {
  if (!projectName || !pageName) return '';

  const miniworkUrl = `https://fl-miniwork.fc.alibaba-inc.com/api/ssr-detail/ssr-list?current=1&pageSize=20&projectName=${projectName}&tabType=all`;
  const miniworkData = await fetch(miniworkUrl).then(res => res.json()).catch(() => { return {}; });
  const ssrPageList = miniworkData && miniworkData.data && miniworkData.data.data || [];

  let streamConfig = '';
  ssrPageList.forEach(item => {
    const childrenList = item.children || [];
    childrenList.forEach(cItem => {
      const path_project_name = cItem.path_project_name || cItem.project_name || '';
      const path_page_name = cItem.path_page_name || cItem.page_name || '';
      if (path_project_name === projectName && path_page_name === pageName) {
        streamConfig = cItem.pre_stream_config || '';
      }
    });
  });

  return streamConfig;
}
