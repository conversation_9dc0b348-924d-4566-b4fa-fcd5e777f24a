import {
  HOLDER_START_REG,
  ROOT_BEFORE_START_REG,
  ROOT_START_REG
} from '../../constant/rax';

// [debug]拼接一屏骨架图所需的基础html，添加对css和js的preload
export default function (ssrHtml) {
  const [holderTag] = ssrHtml.match(HOLDER_START_REG) || [];
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || [];
  if (!rootTag) {
    return '';
  }
  const holderStartPosition = holderTag ? ssrHtml.indexOf(holderTag) : -1;
  const contentStartPosition = rootTag ? ssrHtml.indexOf(rootTag) : -1;
  if (contentStartPosition === -1) {
    return '';
  }

  // 如果存在骨架图，先不拼接骨架图的html，后续会单独拼接。
  const position = holderStartPosition > -1 ? holderStartPosition : contentStartPosition;
  const topContent = ssrHtml.slice(0, position);
  const bottomContent = ssrHtml.slice(position);

  const tags = [...bottomContent.matchAll(/<(script(?![^>]*\bdata-from="server")|link).+?(src|href)='((?:https:)?\/\/(dev\.|)g\.alicdn\.com[^']+)'/g)];
  const preloadTags = tags.reduce((acc, cur) => {
    if (cur && cur[1] && cur[3]) {
      if (cur[1] === 'script') {
        acc += `<link tag="combojs" rel="preload" href="${cur[3]}" as="script" crossorigin="anonymous" />`;
      } else if (cur[1] === 'link') {
        acc += `<link tag="combocss" rel="preload" href="${cur[3]}" as="style" crossorigin="anonymous" />`;
      }
    }
    return acc;
  }, '');

  if (preloadTags) {
    const firstStylePosition = topContent.indexOf('<style');
    return [
      topContent.slice(0, firstStylePosition),
      preloadTags,
      topContent.slice(firstStylePosition)
    ].join('');
  }
  return `${topContent}${''}`;
}