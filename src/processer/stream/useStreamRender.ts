import getMiniworkErConfig from "./getMiniworkErConfig";
import checkIsGray from "./checkIsGray";
import writeStream from "./writeStream";
import {
  getKvCache,
  getTitleBarDefaultHeight,
  getFccTag,
} from '../../utils';
import { SlsLog } from '../../utils/rax';
import { TAOBAO_PHA_WHITE_LIST, STREAM_CONFIG, STREAM_FORCE_MAP, STREAM_PAGE_LIST } from "../../constant/rax";

// 通用流式入口
export default async function (event, opt) {
  const {
    reqUrl,
    ssrUrl,
    secondPartSsrUrl,
    raxUrl,
    request,
    _startTime,
    isPre,
    timeLog
  } = opt;
  timeLog.timeStamp[`stream_start`] = Date.now();

  const useStream = reqUrl.searchParams.get('_use_stream') || STREAM_FORCE_MAP[reqUrl.pathname];
  const useThreePartStream = reqUrl.searchParams.get('_use_three_part_stream') || (STREAM_FORCE_MAP[reqUrl.pathname] === 3);
  const isDebug = reqUrl.searchParams.get('_debug_er');
  const disableCache = reqUrl.searchParams.get('_disable_cache');
  const onlyShowFirstPart = reqUrl.searchParams.get('_debug_first_screen');
  const onlyShowSecondPart = reqUrl.searchParams.get('_debug_second_screen');
  const getErConfig = reqUrl.searchParams.get('_get_er_config');
  const isTest = reqUrl.searchParams.get('_tusi_test');
  const httpCache = reqUrl.searchParams.get('_http_cache');
  const etagCache = reqUrl.searchParams.get('_http_etag');
  const isHomeTest = reqUrl.searchParams.get('_home_er_test') || STREAM_PAGE_LIST.includes(reqUrl.pathname);
  const useFcc = false;

  // 获取页面配置
  timeLog.timeStamp[`er_config_start`] = Date.now();
  let pageErConfig = null;
  let isGray = false;
  let isStreamConfig = false;

  try {
    // "/app/trip/rx-fliggy-sqk/pages/home" --> "rx-fliggy-sqk_home"
    const reqPathArr = reqUrl.pathname.split('/');
    let erDataStr;
    // 预发拿不到 ER 后台配置，只能请求ssr后台获取配置
    if (isPre) {
      erDataStr = await getMiniworkErConfig(reqPathArr[3], reqPathArr[5]);
      pageErConfig = JSON.parse(erDataStr);
    } else {
      const pageErConfigKey = `${reqPathArr[3]}_${reqPathArr[5]}`;
      const tDuration = STREAM_CONFIG[pageErConfigKey] ? 70 : 50;
      // 获取流式配置，50ms超时
      const pagePromise = [new Promise(async (resolve, reject) => {
        const kvRes = await getKvCache('fliggyrax_124215', pageErConfigKey);
        resolve(kvRes);
      }), new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve({});
        }, tDuration);
      })];
      const pageErConfigData:any = useFcc ? await Promise.resolve({data: '{}'}) : await Promise.race(pagePromise);
      erDataStr = pageErConfigData.data;
      pageErConfig = JSON.parse(erDataStr);
      isStreamConfig = STREAM_CONFIG[pageErConfigKey] ? true : false;

      // 如果命中灰度，则使用灰度中的骨架配置
      isGray = checkIsGray(pageErConfig, request);
      if (isGray && pageErConfig.grayTemplate && pageErConfig.grayVersion) {
        pageErConfig = {
          cacheKey: pageErConfig.grayCacheKey,
          template: pageErConfig.grayTemplate,
          version: pageErConfig.grayVersion,
          updateTime: pageErConfig.grayUpdateTime,
        };
      }
    }
  } catch (err) {
    pageErConfig = false;
  }

  if (getErConfig) {
    return JSON.stringify(pageErConfig);
  }

  // 如果未开启端缓存 且 （流式配置不存在 或 不开启流式渲染），则返回false
  if (!useFcc && (!pageErConfig || !pageErConfig.template || !useStream)) {
    timeLog.timeStamp[`page_er_config_err_time`] = Date.now();
    return false;
  }

  // 获取titlebar数据
  const titleBarOpt = getTitleBarDefaultHeight(request, reqUrl.searchParams);

  timeLog.timeStamp[`er_config_end`] = Date.now();

  try {
    const { writable, readable } = new TransformStream();
    // 流式的拼接html文本
    event.waitUntil(writeStream({
      writer: writable.getWriter(),
      pageErConfig,
      titleBarOpt,
      reqUrl,
      ssrUrl,
      secondPartSsrUrl,
      raxUrl,
      request,
      _startTime,
      isPre,
      isGray,
      isDebug,
      isTest,
      useFcc,
      isHomeTest,
      disableCache,
      onlyShowFirstPart,
      onlyShowSecondPart,
      useThreePartStream,
      timeLog,
      event,
      isStreamConfig
    }).catch(e => {
      SlsLog.sendLog({
        event,
        reqUrl,
        logConfig: {
          logName: 'er_stream_log',
          logType: 'error',
          logData: {
            slsNew: true,
            reqUrl: reqUrl.toString(),
            errorMsg: (e && e.message) || JSON.stringify(e),
            ...timeLog
          }
        }
      })
    }));

    let cacheConfig = {};
    // 协商缓存
    if (etagCache) {
      const headerCookie = request.headers.get('cookie') || '';
      const etagReg = headerCookie.match(/_er_etag=(\S+);/) || headerCookie.match(/_er_etag=(\S+)$/);
      const etagValue = etagReg && etagReg.length > 1 ? etagReg[1] : 'initial';
      const etagTime = Date.now() + parseInt(reqUrl.searchParams.get('_http_etag')) * 1000;
      // etag规范：过期时间戳-etag标识，加单个双引号用于弱匹配校验，详见https://aone.alibaba-inc.com/v2/project/1109730/req/55582160
      // 手淘PHA文档预加载协商缓存无效，通过写cookie形式实现
      cacheConfig = {
        'etag': `"${etagTime}-${etagValue}`,
        'set-cookie': `_if_none_match=${etagTime}-${etagValue}; Max-Age=${parseInt(reqUrl.searchParams.get('_http_etag'))}`
      }
    }
    // 强缓存
    if (timeLog.userId && TAOBAO_PHA_WHITE_LIST.indexOf(timeLog.userId) >= 0) {
      // 手淘测试场景，全量开启
      cacheConfig = {
        'cache-control': `max-age=10800`
      }
    } else if (httpCache) {
      cacheConfig = {
        'cache-control': `max-age=${httpCache}`
      }
    }

    return new Response(readable, {
      headers: {
        'content-type': 'text/html; charset=utf-8',
        'use-stream': '1.0.8',
        "Transfer-Encoding": "chunked",
        "streaming-parser": "open",
        ...cacheConfig
      },
      status: 200,
    });
  } catch (err) {
    if (isDebug) {
      return err.message;
    } else {
      // 增加降级标识
      let redirectRaxUrl = '';
      try {
        const raxUrlObj = new URL(raxUrl);
        raxUrlObj.searchParams.set('ssr_fail_back', '1');
        redirectRaxUrl = raxUrlObj.href;
      } catch {
        redirectRaxUrl = raxUrl;
      }
      return Response.redirect(redirectRaxUrl);
    }
  }

}