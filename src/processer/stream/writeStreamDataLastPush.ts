import {
  getfirstPartCacheKey,
  getJsonCache,
  SlsLog,
  getPageName
} from "../../utils/rax";
import getSsrHtml from "./getSsrHtml";
import sendFirstPartHtml from "./sendFirstPartHtml";
import setFristPartCache from "./setFristPartCache";
import sendFirstChunkCache from './sendFirstChunkCache';
import getSecondPartHtmlForTraffic from './getSecondPartHtmlForTraffic';
import { STREAM_FIRST_SCREEN_STATIC_CONFIG } from "../../constant/firstPartMap";
import { PRE_RAX_SSR_HOST, RAX_SSR_HOST } from "../../constant/rax";
import getLastPartHtmlForTraffic from "./getLastPartHtmlForTraffic";

// 通用流式入口
export default async function (opt) {
  const {
    writer = {},
    pageErConfig = {},
    titleBarOpt = {},
    reqUrl,
    ssrUrl = '',
    secondPartSsrUrl = '',
    raxUrl = '',
    request,
    _startTime,
    isPre,
    isGray,
    isDebug,
    useFcc,
    disableCache,
    onlyShowFirstPart,
    onlyShowSecondPart,
    timeLog,
    event,
    isStreamConfig
  } = opt;
  timeLog.timeStamp[`er_cache_start`] = Date.now();

  const encoder = new TextEncoder();
  const writeHtml = (e) => writer.write(encoder.encode(e));

  // 避免预发切换分支时，首屏版本和当前版本不一致，导致上下文件版本差异
  const projectVersionWrong = isPre && reqUrl.searchParams.get('projectVersion') && pageErConfig?.version !== reqUrl.searchParams.get('projectVersion');

  // er配置的缓存更新时间
  const erConfigUpdateTime = pageErConfig.updateTime || 0;

  // 获取第一屏数据缓存
  const firstPartCacheKey = getfirstPartCacheKey(reqUrl, pageErConfig.cacheKey, isGray);
  // 缓存版本、缓存更新时间、缓存内容
  let firstPartCacheVersion = '';
  let firstPartCacheUpdateTime = 0;
  let firstPartCacheHolder = '';

  let requestCombo: any = {}

  // 是否使用流式加速模式
  const isUseStreamFirstStaticMode = reqUrl.searchParams.get('useStaticStream') && STREAM_FIRST_SCREEN_STATIC_CONFIG[reqUrl.pathname]?.[pageErConfig.version]?.[isPre?'pre':'online'];

  try {
    const timeoutDuration = isStreamConfig ? 70 : 50;
    const startTimer = Date.now();
    // 流式所有请求，统一发出(流式首屏由于ER的稳定性问题，额外做了oss的备份)
    requestCombo = {
      first: (useFcc || isUseStreamFirstStaticMode) ? Promise.resolve({}) : Promise.race([
        getJsonCache(firstPartCacheKey, isPre),
        new Promise(async (resolve, reject) => {
          // 通过oss获取兜底首屏，提前回的话，就会替代er-cache内容
          try{
            const envPreTag = firstPartCacheKey.indexOf("?") === -1 ? `?envTag=pre` : `&envTag=pre`
            const cacheKey = `http://${isPre ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}${firstPartCacheKey}${isPre ? envPreTag : ''}`
            const { groupName, serviceName } = getPageName(ssrUrl);
            const newFirstPartCacheKey = cacheKey.slice(cacheKey.indexOf('http://') + 7).replace(/\//g, '-');
            const result = await fetch(`https://cdnoss.fliggy.com/stream/pages/${groupName}-${serviceName}/${newFirstPartCacheKey}.html`,{cdnProxy: true,decompress: "manual"}).then(r=> r&& r.json()).catch(e=> {return {}})
            if(result.url){
              timeLog.ossTime = Date.now() - startTimer; 
              timeLog.ossUrl = `https://cdnoss.fliggy.com/stream/pages/${groupName}-${serviceName}/${newFirstPartCacheKey}.html`;
              resolve({data: result, source: 'oss'})
            }
          }catch(e){}
        }),
        new Promise((resolve, reject) => {setTimeout(() => {resolve({})}, timeoutDuration)})
      ]),
      next: getSsrHtml(ssrUrl, request, timeLog, '', isPre),
    }

    // 获取第一屏缓存，50ms超时
    let firstPartCacheData: any = isUseStreamFirstStaticMode? {
      data: {
        ...pageErConfig,
        holder: STREAM_FIRST_SCREEN_STATIC_CONFIG[reqUrl.pathname]?.[pageErConfig.version]?.[isPre?'pre':'online']
      }
    } : await requestCombo.first;
    timeLog.totalTime = Date.now() - startTimer

    timeLog.firstScreenSource = isUseStreamFirstStaticMode ? 'static' : (firstPartCacheData.source || 'cache');
    
    firstPartCacheData = firstPartCacheData.data;
    firstPartCacheVersion = firstPartCacheData.version;
    firstPartCacheUpdateTime = firstPartCacheData.updateTime || 0;
    firstPartCacheHolder = decodeURIComponent(firstPartCacheData.holder);

    timeLog.totalHolder = JSON.stringify(firstPartCacheData)

  } catch (e) { 
    timeLog.failed_get_cache = true;
    timeLog.failed_msg = e?.message || '其他错误';
  }

  
  try {
    // 如果 使用缓存 且 成功获取第一屏数据缓存 且 版本号一致 且 缓存更新时间大于er配置的时间，则返回首屏数据
    if ((useFcc || isUseStreamFirstStaticMode) || 
      (
        !disableCache &&
        firstPartCacheHolder &&
        (firstPartCacheVersion === pageErConfig.version) &&
        firstPartCacheUpdateTime > erConfigUpdateTime &&
        !projectVersionWrong
      )
    ) {

      // 1. 写一屏骨架 + 获取ssr文档
      timeLog.timeStamp[`first_part_start`] = Date.now();
      const [writeReason, ssrHtml] = await Promise.all([
        useFcc ? sendFirstChunkCache(writeHtml, raxUrl, isDebug, isPre) : sendFirstPartHtml(firstPartCacheHolder, raxUrl, _startTime, titleBarOpt, writeHtml, timeLog, isDebug, isPre),
        requestCombo.next
      ]);
      
      timeLog.timeStamp[`other_part_start`] = Date.now();

      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = (function() {
        if (!ssrHtml) return false;

        if (typeof ssrHtml === 'string') {
          return ssrHtml.indexOf('<body') > -1;
        } else if (typeof ssrHtml === 'object') {
          return true;
        } else {
          return false;
        }
      })();
      if (!ssrHtmlValid) {
        if ((ssrHtml || '').includes('ssrRedirectUrl')) {
          throw new Error(ssrHtml);
        }
        throw new Error(`ssrHtml invalid(use cache)|${ssrHtml}`);
      }

      // 只返回第一屏数据，debug使用
      if (onlyShowFirstPart) {
        await writeHtml('</body></html>');
        await writer.close();
        return;
      }

      // 2. 获取第二屏数据
      timeLog.timeStamp[`second_part_start`] = Date.now();
      const secondPartHtml = await getSecondPartHtmlForTraffic({
        ssrHtml,
      });
      await writeHtml(secondPartHtml);
      timeLog.timeStamp[`second_part_duration`] = Date.now() - timeLog.timeStamp[`second_part_start`];

      // 只返回第一屏 + 第二屏数据，debug使用
      if (onlyShowSecondPart) {
        await writer.close();
        return;
      }

      // 3. 第三屏数据
      timeLog.timeStamp[`last_part_start`] = Date.now();
      const lastPartHtml = await getLastPartHtmlForTraffic({
        ssrHtml: ssrHtml,
        timeLog,
        isDebug,
        firstPartCacheHolder,
        erConfigTemplate: pageErConfig.template
      });
      await writeHtml(lastPartHtml);
      timeLog.timeStamp[`last_part_duration`] = Date.now() - timeLog.timeStamp[`last_part_start`];

      timeLog.timeStamp[`other_part_end`] = Date.now();
    } else if (disableCache && isDebug) {

      // 日常开发 或者 查问题使用。
      const ssrHtml = await getSsrHtml(ssrUrl, request, timeLog, '', isPre);
      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = ssrHtml && ssrHtml.indexOf('<body') > -1;
      if (!ssrHtmlValid) {
        throw new Error(`ssrHtml invalid(disable cache)|${ssrHtml}`);
      }

      // 获取一屏骨架图
      const firstPartHolder = await setFristPartCache({
        ssrHtml,
        pageErConfig,
        firstPartCacheKey,
        isPre,
        ssrUrl,
        writeHtml
      });

      // 如果骨架图解析失败，则抛出异常
      if (!firstPartHolder) {
        throw new Error('decode erConfig template error');
      }

      // 返回一屏骨架图
      await writeHtml(firstPartHolder);

      // 只返回第一屏数据
      if (onlyShowFirstPart) {
        await writeHtml('</body></html>');
        await writer.close();
        return;
      }
      const secondSsrHtml = await getSsrHtml(secondPartSsrUrl, request, timeLog, '', isPre);
      const secondPartHtml = getSecondPartHtmlForTraffic({
        ssrHtml: secondSsrHtml,
      });
      await writeHtml(secondPartHtml);

      // 只返回第一、第二屏数据
      if (onlyShowSecondPart) {
        await writer.close();
        return;
      }

      // 返回最后一屏数据
      const lastPartHtml = getLastPartHtmlForTraffic({
        ssrHtml
      });
      await writeHtml(lastPartHtml);
    } else {
      // 当第一屏数据缓存获取失败 或 版本号不一致 或 缓存更新时间小于等于er配置时间，则请求ssr文档
      const ssrHtml = await requestCombo.next
      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = ssrHtml && ssrHtml.indexOf('<body') > -1;
      if (!ssrHtmlValid) {
        if ((ssrHtml || '').includes('ssrRedirectUrl')) {
          throw new Error(ssrHtml);
        }
        throw new Error(`ssrHtml invalid(no cache)|${ssrHtml}`);
      }

      // 返回完整ssr文档
      await writeHtml(ssrHtml);

      // 存入首屏缓存
      await setFristPartCache({
        ssrHtml,
        pageErConfig,
        firstPartCacheKey,
        isPre,
        ssrUrl,
        writeHtml
      });
    }
  } catch (e) {
    // 如果catch，则跳转market页面
    if (isDebug) {
      await writeHtml(`<div id="error">${e && e.message}</div></body></html>`)
    } else {
      SlsLog.sendLog({
        event,
        reqUrl,
        logConfig: {
          logName: 'er_stream_log',
          logType: 'error',
          logData: {
            slsNew: true,
            reqUrl: reqUrl.toString(),
            errorMsg: (e && e.message) || JSON.stringify(e),
            ...timeLog
          }
        }
      })
      const newUrl = e && e.message && e.message.includes('ssrRedirectUrl') ?
        new URL(e.message.replace('ssrRedirectUrl:', '')) : new URL(raxUrl);
      newUrl.searchParams.set('_er_failback', '1');
      newUrl.searchParams.set('ssr_fail_back', '1');

      const wxNewUrl = new URL(raxUrl);
      wxNewUrl.searchParams.set('_er_failback', '1');
      wxNewUrl.searchParams.set('ssr_fail_back', '1');

      await writeHtml(
        `<script>
        if (window.location.hostname.includes('proxy')) {
          location.replace("${wxNewUrl.toString()}")
        } else {
          location.replace("${newUrl.toString()}")
        }
        </script></body></html>`
      );
    }
  }

  // 打日志
  SlsLog.sendLog({
    event,
    reqUrl,
    logConfig: {
      logData: {
        ...timeLog,
        timeStamp: {
          ...timeLog.timeStamp,
          er_end: Date.now(),
          stream_end: Date.now(),
        },
        er_duration: Date.now() - timeLog.timeStamp.er_start,
        stream_duration: Date.now() - timeLog.timeStamp.stream_start,
        er_config_duration: timeLog.timeStamp.er_config_end - timeLog.timeStamp.er_config_start,
        er_cache_duration: timeLog.timeStamp.er_cache_end - timeLog.timeStamp.er_cache_start,
        html_stringify_duration: timeLog.timeStamp.html_stringify_end - timeLog.timeStamp.html_stringify_start,
        first_part_duration: timeLog.timeStamp.first_part_end - timeLog.timeStamp.first_part_start,
        other_part_duration: timeLog.timeStamp.other_part_end - timeLog.timeStamp.other_part_start,
      }
    }
  })

  await writer.close();
}
