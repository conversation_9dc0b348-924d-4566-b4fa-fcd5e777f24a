import {
  SERVER_START_REG
} from '../../constant/rax';

// [debug]获取最后一屏HTML文档
export default async function (data) {
  const {
    ssrHtml = '',
    timeLog,
    isDebug,
    firstPartCacheHolder,
    erConfigTemplate
  } = data;

  const [secondPartEndTag] = ssrHtml.match(SERVER_START_REG) || [];

  // 从【<<script data-from="server">】开始分割
  if (secondPartEndTag) {
    const serverPosition = ssrHtml.indexOf(secondPartEndTag);
    const list = [
      `<script data-id="last-part-success">
        window._er_next_part_render = true;
        window._er_last_part = Date.now();
        var lastErHolder = document.getElementById('ssr-er-holder');
        var erAddHolder = document.getElementById('ssr-er-additional-holder');
        if (lastErHolder) {
          lastErHolder.remove();
        }
        if (erAddHolder) {
          erAddHolder.remove();
        }
      </script>`,
      `<script>
        if(window.performance && typeof window.performance.mark == 'function'){
          window.performance.mark('stage-second-chunk')
        }
        console.log('SSR 函数端到端耗时：${timeLog && timeLog.faas_duration || 'null'}');
        console.log('SSR 函数执行耗时：${timeLog && timeLog.ssr_duration || 'null'}');
      </script>`,
    ];
    if (isDebug) {
      const debugInfo = await getDebugInfo({
        ssrHtml,
        firstPartCacheHolder,
        erConfigTemplate,
      });
      list.push(debugInfo);
    }

    list.push(ssrHtml.slice(serverPosition));

    return list.join('');
  }

  throw new Error('getLastPartHtml error');
}

// [debug]获取debug数据
async function getDebugInfo(data) {
  const {
    ssrHtml,
    firstPartCacheHolder,
    erConfigTemplate,
  } = data;

  return `
  <script id="debug">
    window._er_debug_message = {
      ssrHtml: decodeURIComponent("${encodeURIComponent(ssrHtml || '')}"),
      firstPartCacheHolder: decodeURIComponent("${encodeURIComponent(firstPartCacheHolder || '')}"),
      erConfigTemplate: decodeURIComponent("${erConfigTemplate || ''}"),
    }
  </script>
  `;
}