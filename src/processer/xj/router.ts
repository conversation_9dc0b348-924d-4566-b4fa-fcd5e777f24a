import { logger, getKvCache } from '../../utils/xj';
import { handleSSR } from '../../handler/xj/ssr';
import { ROUTER_KV_CONFIG_KEY, DEFAULT_REDIRECT_URL, PRE_CROWD_URL, CROWD_URL } from '../../constant/xj';

/**
 * 统一路由
 */
export const handleRouter = async ({ reqUrl, context, request, event }) => {
  //请求回源
  const fetchOrigin = async (url) => {
    const urlObj = new URL(url);
    const { hostname } = urlObj;
    const isPre = hostname.includes('.wapa.') || hostname.includes('pre-');
    return await handleSSR(event, { ...context, reqUrl: new URL(url), isPre });
  };

  //追加页面参数
  const appendSearchParams = ({ baseUrl, params }) => {
    const urlObj = new URL(baseUrl);
    params.forEach((key, value) => {
      if (!urlObj.searchParams.has(key)) {
        urlObj.searchParams.append(key, value);
      }
    });
    return urlObj.toString();
  };

  try {
    const { pathname, searchParams } = reqUrl;
    if (!/^\/xj(?:-csr)?\/router/.test(pathname)) return false;

    const cacheConfig = await getKvCache({ key: ROUTER_KV_CONFIG_KEY, context }) || {}; //KV配置
    const routerPath = pathname.split('/').pop(); //当前路由
    const currentConfig = cacheConfig[routerPath]; //当前配置

    //未获取到路由配置
    if (!currentConfig) {
      await logger.record({
        logKey: 'routerGetConfigError',
        logName: '未获取到当前页面路由配置',
        context,
        redirectUrl: DEFAULT_REDIRECT_URL
      });
      return Response.redirect(DEFAULT_REDIRECT_URL);
    }

    const { enabled = false, urls = [] } = currentConfig;
    let redirectUrl = urls.find(url => url.isDefault)?.realUrl || DEFAULT_REDIRECT_URL;

    if (!enabled) return await fetchOrigin(redirectUrl); //未开启

    const sortedUrls = [...urls].sort((a, b) => (a.priority || 0) - (b.priority || 0)); // 按优先级排序

    //匹配逻辑处理
    for (const urlConfig of sortedUrls) {
      const { distributionType, crowdId, urlParams = [], addUrlParams = false, realUrl } = urlConfig;
      if (distributionType === 'zhuge' && crowdId != null) {
        //诸葛圈人模式
        const crowdRes = await fetch(`${context.isPre ? PRE_CROWD_URL : CROWD_URL}?crowdIds=${crowdId}&userId=${context.userId}`, { headers: request.headers });
        const crowdJson = await crowdRes.json();
        const crowdRule = crowdJson?.data || {};
        //命中圈人
        if (crowdRule[crowdId] === true) {
          redirectUrl = realUrl;
          if (addUrlParams) redirectUrl = appendSearchParams({ baseUrl: redirectUrl, params: searchParams });
          break;
        }
      } else if (distributionType === 'urlParams') {
        //URL参数匹配模式
        if (urlParams.every(({ key, value }) => searchParams.get(key) === value)) {
          redirectUrl = realUrl;
          if (addUrlParams) redirectUrl = appendSearchParams({ baseUrl: redirectUrl, params: searchParams });
          break;
        }
      }
    }

    await logger.record({
      logKey: 'routerRedirect',
      logName: '统一路由重定向',
      context,
      redirectUrl
    });

    return await fetchOrigin(redirectUrl);
  } catch (e) {
    await logger.record({
      logKey: 'routerError',
      logName: '统一路由页异常',
      content: { tracker: e.message },
      context,
      redirectUrl: DEFAULT_REDIRECT_URL
    });
    return await Response.redirect(DEFAULT_REDIRECT_URL);
  }
};