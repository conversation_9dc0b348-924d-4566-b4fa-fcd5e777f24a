import { logger, getKvCache } from '../../utils/xj';
import { CDN_CACHE_HOST, PRE_CDN_CACHE_HOST, CDN_CACHE_PATH_REG, CDN_KV_CONFIG_KEY } from '../../constant/xj';

//读取CDN缓存
export const checkCdnCache = async ({ context, event }) => {
  const { reqUrl, isPre, userId } = context;
  const { searchParams, pathname } = reqUrl;
  const updateCache = searchParams.get('_up_snapshot') === 'true'; //是否更新缓存

  try {
    if (updateCache || isPre) return false; //更新缓存时不获取
    const cdnPath = pathname.match(CDN_CACHE_PATH_REG)[1]; //页面path
    const cacheConfig = await getKvCache({ key: CDN_KV_CONFIG_KEY, context }) || {}; //kv配置
    const currentConfig = cacheConfig[cdnPath]; //当前页面配置
    if (!currentConfig) return false; //当前页面未配置退出
    const { enabled = false, urlKeys = [], urlParams = [], cacheDuration = 1440 } = currentConfig;
    if (!enabled) return false; //未开启退出
    const cdnHost = isPre ? PRE_CDN_CACHE_HOST : CDN_CACHE_HOST; //CDN源站域名

    //获取CDN缓存
    const fetchCdnCache = async (cacheKey) => {
      const cdnUrl = `${cdnHost}${cdnPath}/${cacheKey}.html`; //oss地址
      const cdnRes = await fetch(cdnUrl, { cdnProxy: true, decompress: "manual" }); //请求CDN缓存
      //刷新缓存
      const refreshCacheUpdate = () => {
        addCdnCache({ event, context, cacheKey });
        return null;
      };
      if (cdnRes.status !== 200) return refreshCacheUpdate(); //未获取到缓存时刷新
      const lastModified = cdnRes?.headers?.get('Last-Modified'); //缓存写入时间
      const hasExpired = Date.now() > new Date(lastModified).getTime() + cacheDuration * 60 * 1000; //缓存是否过期
      if (hasExpired) return refreshCacheUpdate(); //超时刷新
      return cdnRes;
    };

    //遍历关键参数和动态参数获取缓存
    for (const urlKey of urlKeys) {
      if (!searchParams.has(urlKey)) continue; //关键参数未匹配退出
      if (urlKey === 'noLogin' && userId !== '') continue; //已登录退出
      let cacheKey = urlKey;
      for (const urlParam of urlParams) {
        const urlParamValue = searchParams.get(urlParam);
        if (urlParamValue) cacheKey += `-${urlParam}:${urlParamValue}`;
      }
      const cdnRes = await fetchCdnCache(cacheKey);
      if (cdnRes) return cdnRes;
    }

    return false;
  } catch (e) {
    await logger.record({
      logKey: 'cdnCacheReadError',
      logName: 'CDN预加载读取错误',
      content: { tracker: e.message },
      context
    });
    return false;
  }
}

//写入CDN缓存
export const addCdnCache = async ({ event, context, cacheKey }) => {
  const { reqUrl } = context;
  try {
    //写入缓存函数
    const cacheFunc = async ({ reqUrl, cacheKey }) => {
      const orginUrl = reqUrl;
      orginUrl.searchParams.set('_up_snapshot', true);
      orginUrl.searchParams.set('cdnCacheKey', cacheKey);
      await fetch(orginUrl);
    }
    event.waitUntil(cacheFunc({ reqUrl, cacheKey }));
  } catch (e) {
    await logger.record({
      logKey: 'cdnCacheWriteError',
      logName: 'CDN预加载写入错误',
      content: { tracker: e.message },
      context
    });
  }
}