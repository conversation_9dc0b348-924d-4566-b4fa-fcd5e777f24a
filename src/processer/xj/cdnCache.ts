import { logger, getKvCache } from '../../utils/xj';
import { removeErCacheHeaders, getPreloadCacheKey, removeHtmlCacheHeaders } from '../../utils';
import { SSR_PRE_ORIGIN_HOST, SSR_ORIGIN_HOST, CDN_CACHE_HOST, PRE_CDN_CACHE_HOST, CDN_CACHE_PATH_REG, CDN_KV_CONFIG_KEY, SSR_CACHE_AGE } from '../../constant/xj';

//读取CDN缓存
export const checkCdnCache = async ({ context, event }) => {
  const { reqUrl, isPre, userId } = context;
  const { request } = event;
  const { searchParams, pathname } = reqUrl;
  const updateCache = searchParams.get('_up_snapshot') === 'true'; //是否更新缓存
  const erCacheUrl = getPreloadCacheKey(request, reqUrl); //er缓存key

  try {
    if (updateCache || isPre) return false; //更新缓存时不获取
    const cdnPath = pathname.match(CDN_CACHE_PATH_REG)[1]; //页面path
    const cacheConfig = await getKvCache({ key: CDN_KV_CONFIG_KEY, context }) || {}; //kv配置
    const currentConfig = cacheConfig[cdnPath]; //当前页面配置
    if (!currentConfig) return false; //当前页面未配置退出
    const { enabled = false, urlKeys = [], urlParams = [], cacheDuration = 1440 } = currentConfig;
    if (!enabled) return false; //未开启退出
    const cdnHost = isPre ? PRE_CDN_CACHE_HOST : CDN_CACHE_HOST; //CDN源站域名

    //获取CDN缓存
    const fetchCdnCache = async (cacheKey) => {
      //刷新缓存
      const refreshCacheUpdate = () => {
        addCdnCache({ event, context, cacheKey, cacheDuration, erCacheUrl });
        return null;
      };

      //请求缓存
      const cdnPromise = new Promise<Response>(async (resolve) => {
        const cdnUrl = `${cdnHost}${cdnPath}/${cacheKey}.html`;
        const res = await fetch(cdnUrl, { cdnProxy: true, decompress: "manual" });
        resolve(res);
      });
      const erPromise = new Promise<Response>(async (resolve) => {
        const res = await cache.get(erCacheUrl);
        resolve(res);
      });
      const cacheRes = await Promise.race([cdnPromise, erPromise]);

      //判断缓存时间
      if (!cacheRes) return refreshCacheUpdate(); //未获取到缓存时刷新
      const lastModified = cacheRes?.headers?.get('Last-Modified'); //缓存写入时间
      const hasExpired = Date.now() > new Date(lastModified).getTime() + cacheDuration * 60 * 1000; //缓存是否过期
      if (hasExpired) return refreshCacheUpdate(); //超时刷新
      return cacheRes;
    };

    //遍历关键参数和动态参数获取缓存
    for (const urlKey of urlKeys) {
      if (!searchParams.has(urlKey)) continue; //关键参数未匹配退出
      if (urlKey === 'noLogin' && userId !== '') continue; //已登录退出
      let cacheKey = urlKey;
      for (const urlParam of urlParams) {
        const urlParamValue = searchParams.get(urlParam);
        if (urlParamValue) cacheKey += `-${urlParam}:${urlParamValue}`;
      }

      const cdnRes = await fetchCdnCache(cacheKey);
      if (cdnRes) return cdnRes;
    }

    return false;
  } catch (e) {
    await logger.record({
      logKey: 'cdnCacheReadError',
      logName: 'CDN预加载读取错误',
      content: { tracker: e.message },
      context
    });
    return false;
  }
}

//写入CDN缓存
export const addCdnCache = async ({ event, context, cacheKey, cacheDuration, erCacheUrl }) => {
  try {
    //写入缓存函数
    const cacheFunc = async ({ context, cacheKey, erCacheUrl, cacheDuration }) => {
      //写入OSS缓存
      const { reqUrl, isPre } = context;
      const { pathname, search } = reqUrl;
      const originUrl = new URL(`https://${isPre ? SSR_PRE_ORIGIN_HOST : SSR_ORIGIN_HOST}`);
      originUrl.pathname = pathname;
      originUrl.search = search;
      originUrl.searchParams.set('_up_snapshot', 'true');
      originUrl.searchParams.set('cdnCacheKey', cacheKey);
      const originRes = await fetch(originUrl.toString(), {});
      //写入ER缓存
      try {
        const htmlText = await (originRes.clone()).text();
        const resHTML = htmlText.replace('</head>', '<script>window._er_cache=true;try{if(!window.location.href.includes("_er_cache")){history.replaceState(null, "", window.location.href + (window.location.href.includes("?") ? "&_er_cache=true" : "?_er_cache=true"))}}catch(e){}</script></head>');
        const pressrCache = new Response(resHTML, {
          headers: originRes.headers,
          status: 200
        });
        const cacheSeconds = cacheDuration || SSR_CACHE_AGE;
        removeErCacheHeaders(pressrCache.headers);
        removeHtmlCacheHeaders(pressrCache.headers);
        pressrCache.headers.set('cache-control', `max-age=${cacheSeconds}`);
        pressrCache.headers.set('x-cache-time', `${Date.now()}`);
        pressrCache.headers.set('Last-Modified', new Date().toUTCString());
        pressrCache.headers.set('content-type', 'text/html; charset=utf-8');
        pressrCache.headers.set('x-er-hit-cache', 'true');
        await cache.put(erCacheUrl, pressrCache);
      } catch (e) {
        await logger.record({
          logKey: 'erCacheWriteError',
          logName: 'ER写入缓存失败',
          content: { tracker: e.message },
          context
        });
      }
    }
    event.waitUntil(cacheFunc({ context, cacheKey, erCacheUrl, cacheDuration }));
  } catch (e) {
    await logger.record({
      logKey: 'cdnCacheWriteError',
      logName: 'CDN预加载写入错误',
      content: { tracker: e.message },
      context
    });
  }
}