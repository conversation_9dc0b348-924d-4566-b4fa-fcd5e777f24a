/**
 * 边缘预加载
*/

import { getPreloadCacheKey, removeErCacheHeaders } from '../../utils';
import { logger } from '../../utils/xj';
import { CACHE_TIMEOUT, SSR_CACHE_AGE } from '../../constant/xj';

//读取边缘预加载
export const checkEdgeCache = async ({ context, event }) => {
  try {
    const { request } = event;
    const { reqUrl } = context;
    const { searchParams } = reqUrl;
    const hasPreSSR = searchParams.has('_pressr'); //是否预加载
    const hasFlAutoPreloadSpm = searchParams.has('_fl_auto_preload_spm'); //是否自动预加载
    const preCacheUrl = hasFlAutoPreloadSpm ? '' : getPreloadCacheKey(request, reqUrl); //缓存key

    if (!preCacheUrl || hasPreSSR) return false;

    //读缓存
    const cachePromise = new Promise<Response>(async (resolve) => {
      const res = await cache.get(preCacheUrl);
      resolve(res);
    })
    const timeoutPromise = new Promise<false>((resolve) => {
      setTimeout(() => resolve(false), CACHE_TIMEOUT);
    });
    const pressrCacheRes = await Promise.race([cachePromise, timeoutPromise]);

    //处理响应头
    if (pressrCacheRes) {
      pressrCacheRes.headers.set('content-type', 'text/html; charset=utf-8');
      pressrCacheRes.headers.set('x-er-hit-cache', 'true');
      if (!pressrCacheRes.headers.get('x-er-cache-not-clear')) {
        await cache.delete(preCacheUrl);
      }
      return pressrCacheRes;
    }
    return false;
  } catch (e) {
    await logger.record({
      logKey: 'edgeCacheReadError',
      logName: '边缘预加载读取错误',
      content: { tracker: e.message },
      context
    });
    return false;
  }
}

//写入边缘预加载
export const addEdgeCache = async ({ originRes, context, event }) => {
  const { request } = event;
  const { reqUrl } = context;
  const { searchParams } = reqUrl;
  const hasFlAutoPreloadSpm = searchParams.has('_fl_auto_preload_spm'); //是否自动预加载
  const hasPreSSR = searchParams.has('_pressr'); //是否预加载
  const preCacheUrl = hasFlAutoPreloadSpm ? false : getPreloadCacheKey(request, reqUrl); //缓存key

  if (!hasPreSSR || !preCacheUrl) return;

  try {
    //写入缓存函数
    const cacheFunc = async ({ originRes, searchParams }) => {
      const htmlText = await (originRes.clone()).text();
      const resHTML = htmlText.replace('</head>', '<script>window._er_cache=true;try{if(!window.location.href.includes("_er_cache")){history.replaceState(null, "", window.location.href + (window.location.href.includes("?") ? "&_er_cache=true" : "?_er_cache=true"))}}catch(e){}</script></head>');
      const pressrCache = new Response(resHTML, {
        headers: originRes.headers,
        status: 200
      })
      const cacheSeconds = searchParams.get('_preMaxAge') || SSR_CACHE_AGE;
      removeErCacheHeaders(pressrCache.headers);
      pressrCache.headers.set('cache-control', `max-age=${cacheSeconds}`);
      pressrCache.headers.set('x-cache-time', `${Date.now()}`);
      if (searchParams.has('_preNotClear')) {
        pressrCache.headers.set('x-er-cache-not-clear', 'true');
      }
      await cache.put(preCacheUrl, pressrCache);
    }
    event.waitUntil(cacheFunc({ originRes, searchParams }));
  } catch (e) {
    await logger.record({
      logKey: 'edgeCacheWriteError',
      logName: '边缘预加载写入错误',
      content: { tracker: e.message },
      context
    });
  }
}

//删除边缘预加载缓存
export const deleteEdgeCache = async ({ event, context }) => {
  const { request } = event;
  const { reqUrl } = context;
  const preCacheUrl = getPreloadCacheKey(request, reqUrl); //缓存key
  if (preCacheUrl) {
    await cache.delete(preCacheUrl);
  }
}