/**
 * 网关逻辑
*/

import { DEFAULT_REDIRECT_URL, XUANJI_PATH_REG, JS_MAP_REG, URC_KV_CONFIG_KEY, SSR_PRE_ORIGIN_HOST, SSR_ORIGIN_HOST } from '../../constant/xj';
import { logger, getKvCache } from '../../utils/xj';

//处理 URC
export const handleManifest = async ({ reqUrl, context }) => {
  const { pathname } = reqUrl;
  const { isPre, pathSplit = {} } = context
  try {
    if (!pathname.includes('index.html.fmanifest.json')) return false;
    const urcConfig = await getKvCache({ key: URC_KV_CONFIG_KEY, context }) || {}; //kv配置
    const { enabled = false, siteWhiteList = [], groupWhiteList = [], pageBlackList = [] } = urcConfig;
    const { site, group, page } = pathSplit;
    if (!enabled) return false; //全局开关
    if (!siteWhiteList.includes(site)) return false; //站点白名单
    if (!groupWhiteList.includes(group)) return false; //分组白名单
    if (pageBlackList.includes(`/${site}/${group}/${page}`)) return false; //页面黑名单
    const manifestPath = pathname.replace(/^\/xj(-csr)?/, '').replace(/index\.html\.fmanifest\.json$/, 'fmanifest.json');
    const manifestUrl = `https://xjcdn.fliggy.com/${isPre ? 'prepub' : 'release'}${manifestPath}`;
    const manifestRes = await fetch(manifestUrl, { cdnProxy: true, decompress: 'manual' });
    return manifestRes;
  } catch (e) {
    await logger.record({
      logKey: 'urcProxyError',
      logName: 'URC代理错误',
      content: { tracker: e.message },
      context
    });
    return false;
  }
}

//处理预览请求
export const handlePreview = async ({ context, request }) => {
  const { reqUrl, isPre } = context;
  const { method, headers, body } = request;
  const { pathname, search, searchParams } = reqUrl;

  if (method.toUpperCase() !== 'POST' || searchParams.get('phecda_local_preview') !== 'true') return false;

  //请求回源
  const originUrl = new URL(`https://${isPre ? SSR_PRE_ORIGIN_HOST : SSR_ORIGIN_HOST}`);
  originUrl.pathname = pathname;
  originUrl.search = search;
  const originRes = await fetch(originUrl.toString(), { method: 'POST', headers, body });
  return originRes;
}

//处理统一渲染页
export const handleUpr = ({ reqUrl }) => {
  const { searchParams } = reqUrl;
  const wh_pid = searchParams.get('wh_pid');
  if (wh_pid) {
    searchParams.delete('wh_pid');
    reqUrl.pathname = reqUrl.pathname.replace(/[^/]+\/[^/]+$/, wh_pid);
  }
  return reqUrl;
}

//路由准入过滤
export const handleFillter = async ({ reqUrl, context }) => {
  try {
    const { pathname, href } = reqUrl;

    //璇玑路由校验
    const isXuanji = XUANJI_PATH_REG.test(pathname);
    if (!isXuanji) throw new Error('不符合璇玑路由规范');

    //js.map 过滤
    const isJsMap = JS_MAP_REG.test(href);
    if (isJsMap) throw new Error('js.map 过滤');

    return false;
  } catch (e) {
    await logger.record({
      logKey: 'urlVerificationError',
      logName: '路由过滤',
      topic: 'filter',
      context,
      content: { message: e.message }
    });
    return Response.redirect(DEFAULT_REDIRECT_URL);
  }
}