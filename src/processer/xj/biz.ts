/**
 * 业务逻辑
*/

import { processTBSeachParams } from '../../utils';

//处理淘宝首猜参数
export const handleTBSeach = ({ reqUrl, request }) => {
  reqUrl.search = processTBSeachParams(reqUrl.search);
  if (
    request.url.includes('utparam') ||
    request.url.includes('_fli_nav_dynamic_router_start_time') ||
    request.url.includes('_fli_nav_dynamic_router_end_time') ||
    request.url.includes('imei')
  ) {
    request.headers.set(
      'ali-proxy-consistent-hash',
      `${reqUrl.pathname}${reqUrl.search}`,
    );
    request.headers.set(
      'ali-origin-real-url',
      `${reqUrl.origin}${reqUrl.pathname}${reqUrl.search}`,
    );
  }
}