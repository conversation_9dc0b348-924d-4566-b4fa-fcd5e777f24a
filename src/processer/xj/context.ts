/** 构造上下文 */
import { getTerminal, getOSName, getAppName } from '../../utils';
import { splitPath } from '../../utils/xj';
import { WX_HOST } from '../../constant';

export const getContext = ({ reqUrl, request }) => {
  const { headers } = request;
  const { pathname, hostname, href, } = reqUrl;

  const ua = headers.get('user-agent') || ''; //UA信息
  const appName = getAppName(ua) || 'OTHER'; //客户端
  const proxyRealHost = WX_HOST.includes(hostname) ? reqUrl.searchParams.get('_fm_real_host_') : '';
  const proxyPreHost = WX_HOST.filter(_host => _host.startsWith('pre-'));
  const isXhsMiniAppH5 = appName?.toLowerCase() == 'xhsmini';
  const isWeChatMiniProgramH5 = appName?.toLowerCase() == 'wxmini';
  const isByteDanceMicroApp = appName?.toLowerCase() == 'douyinmini';
  const isMiniProgramH5 = isXhsMiniAppH5 || isWeChatMiniProgramH5 || isByteDanceMicroApp;
  const isTaobao = /^outfliggys\.(m|wapa)\.taobao\.com/.test(hostname) || /^outfliggys\.(m|wapa)\.taobao\.com/.test(proxyRealHost); //是否淘宝域名
  const isPre = /\.wapa\.(fliggy|taobao)\.com/.test(hostname) || proxyPreHost.includes(hostname); //是否预发环境
  const isCSR = isTaobao ? pathname.startsWith('/xj-csr/') : hostname.indexOf('ssr') === -1; //是否CSR
  const terminal = getTerminal(ua); //移动端/PC
  const osName = getOSName(ua); //系统信息
  const ip = headers.get("Ali-Cdn-Real-Ip") || ''; //IP
  const cookie = headers.get('cookie') || ''; //cookie信息
  const unbMatches = cookie.match(/unb=(\d+);/);
  const munbMatches = cookie.match(/munb=(\d+);/);
  const userId = (unbMatches && unbMatches[1]) || (munbMatches && munbMatches[1]) || '' //userId
  const url = href; //完整链接
  const pathSplit = splitPath(pathname); //path拆分

  return { isTaobao, isPre, isCSR, ua, terminal, appName, osName, ip, cookie, userId, url, reqUrl, pathSplit, isMiniProgramH5 }
}