// css内联能力
export default async function (ssrResponse: Response, request: Request) {
  try {
    const reqUrl = new URL(request.url);
    const { searchParams } = reqUrl;
    if (searchParams.has('_css_inline')) {
      // 获取html文档内容
      const htmlText = await (ssrResponse.clone()).text();
      // 提取所有的css link
      const regex = /<link[^>]+href=['"]([^'"]+\.css)['"][^>]*>/gi;
      const cssList = htmlText.match(regex);
      if (cssList && cssList.length > 0) {
        let promiseList = [];
        cssList.forEach((item) => {
          const cssStrReg = item.match(/\s*href=['"](.*?)['"]\s*/);
          if (cssStrReg && cssStrReg.length > 1) {
            let cssUrl = cssStrReg[1];
            // 拼接css完整链接
            if (cssUrl.indexOf('https') < 0) {
              cssUrl = `https:${cssUrl}`;
            }
            const promiseItem = new Promise(async (resolve) => {
              const cssRes = await fetch(cssUrl);
              const cssText = await cssRes.text();
              resolve(cssText)
            })
            promiseList.push(promiseItem);
          } else {
            const promiseItem = new Promise(async (resolve) => {
              resolve('');
            })
            promiseList.push(promiseItem);
          }
        })
        const promiseRes = await Promise.all(promiseList);
        let resHTML = htmlText;
        cssList.forEach((item, index) => {
          if (promiseRes[index]) {
            resHTML = resHTML.replace(item, `<style>${promiseRes[index]}</style>`)
          }
        })
        ssrResponse = new Response(resHTML, {
          headers: ssrResponse.headers,
          status: 200
        })
      }
    }
    return ssrResponse;
  } catch (e) {
    return ssrResponse;
  }
}