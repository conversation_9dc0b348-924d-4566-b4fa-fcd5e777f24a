import {
  CDN_PAGE_HOST,
  PRE_CDN_PAGE_HOST,
  CDN_CACHE_PAGE,
  PRE_CDN_CACHE_PAGE
} from '../../constant/rax';
import { checkIsPre } from '../../utils/rax';

// cdn缓存获取
export async function checkCdnCache(event: FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const { pathname, searchParams } = reqUrl;
    const isPre = checkIsPre(reqUrl);
    const cdnConfig = isPre ? PRE_CDN_CACHE_PAGE[pathname] : CDN_CACHE_PAGE[pathname];
    const cdnHost = isPre ? PRE_CDN_PAGE_HOST : CDN_PAGE_HOST;
    if (searchParams.get('_fli_snapshot') !== 'false'
    && !searchParams.get('_up_snapshot') && cdnConfig) {
      // 获取
      const groupName = pathname.match(/\/app\/trip\/(\S*)\/pages\//) && pathname.match(/\/app\/trip\/(\S*)\/pages\//)[1];
      const pageName = pathname.match(/\/pages\/(\S*)/) && pathname.match(/\/pages\/(\S*)/)[1];
      const cdnKey = cdnConfig.cdnKey || [];

      if (groupName && pageName) {
        let keyStr = '';
        if (cdnKey.length) {
          cdnKey.forEach((item, index) => {
            keyStr = `${keyStr}${searchParams.get(item) || ''}${index === cdnKey.length - 1 ? '' : '_'}`
          })
        } else {
          keyStr = 'index';
        }
        const cdnUrl = `${cdnHost}${groupName}-${pageName}/${keyStr}.html`;
        const cdnResponse = await fetch(cdnUrl, {
          cdnProxy: true,
          decompress: "manual"
        });
        if (cdnResponse.status === 200) {
          return cdnResponse;
        }
      }
    }
  } catch (e) { }

}