import {
  RAX_CACHE_PAGE_LIST
} from '../../constant/rax';
import {
  getPreloadCacheKey,
  removeErCacheHeaders,
  removeHtmlCacheHeaders,
  getKvCache
} from '../../utils';
import { SlsLog } from '../../utils/rax';

// 静态缓存逻辑
export async function checkStaticCache(event:FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const { pathname, searchParams } = reqUrl;
    if (RAX_CACHE_PAGE_LIST[pathname] && !searchParams.get('_force_online')
    && (!RAX_CACHE_PAGE_LIST[pathname].requiredKey || searchParams.get(RAX_CACHE_PAGE_LIST[pathname].requiredKey))) {
      const timeStamp: any = {};
      // 读缓存
      timeStamp.static_cache_start = Date.now();
      const cacheUrl = getPreloadCacheKey(request, reqUrl, RAX_CACHE_PAGE_LIST[pathname]);
      const cacheRes = cacheUrl ? await cache.get(cacheUrl): false;
      timeStamp.static_cache_end = Date.now();
      timeStamp.static_cache_duration = timeStamp.static_cache_end - timeStamp.static_cache_start;
      SlsLog.setTimeLog({
        timeStamp: {
          ...SlsLog.getTimeLog().timeStamp,
          ...timeStamp
        }
      })

      // 缓存存在直接返回结果，异步读kv处理过期判断和日志上报
      if (cacheRes) {
        const kvFunc = async (cacheRes, cacheUrl) => {
          let staticConfig = {};
          if (RAX_CACHE_PAGE_LIST[pathname].kvName) {
            const staticKvCache = await getKvCache('fliggyrax_124215', RAX_CACHE_PAGE_LIST[pathname].kvName);
            if (staticKvCache.data) {
              try {
                staticConfig = JSON.parse(staticKvCache.data);
              } catch (e) {
              }
              if (staticConfig[pathname]) {
                // 获取缓存的时间戳
                const cacheTime = parseInt(cacheRes.headers.get('x-cache-time'));
                // 获取更新时间
                const updateTime = staticConfig[pathname].updateTime;
                let targetUpdateTime;
                if (staticConfig[pathname].key && staticConfig[pathname].updateConfig) {
                  const targetKey = searchParams.get(staticConfig[pathname].key);
                  if (staticConfig[pathname].updateConfig[targetKey]) {
                    targetUpdateTime = staticConfig[pathname].updateConfig[targetKey]
                  }
                }
                // 过期删除缓存
                if ((updateTime && cacheTime < updateTime) || (targetUpdateTime && cacheTime < targetUpdateTime)) {
                  await cache.delete(cacheUrl);
                }
              } else {
                await cache.delete(cacheUrl);
              }
            } else {
              await cache.delete(cacheUrl);
            }
          }
        }
        const timeLog = SlsLog.getTimeLog();
        cacheRes.headers.set('content-type', 'text/html; charset=utf-8');
        cacheRes.headers.set('server-timing', `t;desc="sc", time;desc=${Date.now() - timeLog.timeStamp.er_start}`)
        cacheRes.headers.set('x-er-hit-cache', 'true');
        removeErCacheHeaders(cacheRes.headers);
        removeHtmlCacheHeaders(cacheRes.headers);
        SlsLog.sendLog({
          event,
          reqUrl,
          logConfig: {
            logData: {
              ...timeLog,
              timeStamp: { 
                ...timeLog.timeStamp,
                er_end: Date.now()
              },
              er_duration: Date.now()- timeLog.timeStamp.er_start,
              hit_cache: '1'
            }
          }
        });
        event.waitUntil(kvFunc(cacheRes, cacheUrl));
        return cacheRes;
      }
    }
  } catch(e) {
  }
}