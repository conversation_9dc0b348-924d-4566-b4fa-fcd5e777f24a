import { checkRaxRedirectUrl } from "../../utils/rax";
import { RAX_ORIGIN_HOST } from "../../constant/rax";

export default async function (request) {
  const reqUrl = new URL(request.url);

  // [debug] For Test 302 测试
  if (reqUrl.searchParams.get('bypassRedirect') === 'true') {
    const origin = new URL(`https://${RAX_ORIGIN_HOST}`);
    const searchParams = reqUrl.searchParams;
    origin.pathname = reqUrl.pathname;
    origin.search = '?' + searchParams.toString();
    checkRaxRedirectUrl(origin, reqUrl.hostname, request);
    return Response.redirect(origin.toString());
  }

  // [debug] For Test 失败测试
  if (reqUrl.searchParams.has('bypassFail')) {
    throw new Error('bypass fail');
  }

  // [debug] For Test 获取headers测试
  if (reqUrl.searchParams.has('bypassGetHeaders')) {
    let headersData = {};
    for (let key of request.headers.keys()) {
      headersData[key] = request.headers.get(key);
    }
    return JSON.stringify(headersData);
  }

  // [debug] For Test 获取url
  if (reqUrl.searchParams.has('bypassUrl')) {
    return JSON.stringify(reqUrl.hostname);
  }
}