import {
  getPreloadCacheKey,
  removeErCacheHeaders,
  removeHtmlCacheHeaders
} from '../../utils';
import { SlsLog } from '../../utils/rax/slsLog';
import { RAX_CACHE_PAGE_LIST, RAX_CACHE_BLACK_LIST } from '../../constant/rax';

// 边缘预加载读逻辑
export async function checkEdgeCache(event: FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const {pathname, searchParams} = reqUrl;
    const kvConfig = RAX_CACHE_PAGE_LIST[pathname];
    const timeStamp: any = {};
    // 读缓存
    timeStamp.pre_cache_start = Date.now();
    const preCacheUrl = searchParams.has('_fl_auto_preload_spm') || RAX_CACHE_BLACK_LIST[reqUrl.pathname] ? '' : getPreloadCacheKey(request, reqUrl, kvConfig);
    const prePromise = [new Promise(async (resolve, reject) => {
      const cRes = await cache.get(preCacheUrl);
      resolve(cRes);
    }), new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve(false);
      }, 50);
    })];
    const pressrCacheResponse: any = searchParams.has('_pressr') || !preCacheUrl || kvConfig ? false : await Promise.race(prePromise);
    timeStamp.pre_cache_end = Date.now();
    timeStamp.pre_cache_duration = timeStamp.pre_cache_end - timeStamp.pre_cache_start;
    
    SlsLog.setTimeLog({
      preCacheUrl: preCacheUrl,
      timeStamp: {
        ...SlsLog.getTimeLog().timeStamp,
        ...timeStamp
      }
    })
    
    if (pressrCacheResponse) {
      pressrCacheResponse.headers.set('content-type', 'text/html; charset=utf-8')
      pressrCacheResponse.headers.set('server-timing', `t;desc="sc", time;desc=${Date.now() - SlsLog.getTimeLog().timeStamp.er_start}`)
      pressrCacheResponse.headers.set('x-er-hit-cache', 'true');
      removeErCacheHeaders(pressrCacheResponse.headers);
      removeHtmlCacheHeaders(pressrCacheResponse.headers);
      timeStamp.pre_delete_start = Date.now();
      if (!pressrCacheResponse.headers.get('x-er-cache-not-clear')) {
        await cache.delete(preCacheUrl);
      }
      timeStamp.pre_delete_end = Date.now();
      timeStamp.pre_delete_duration = timeStamp.pre_delete_end - timeStamp.pre_delete_start;
      const timeLog = SlsLog.getTimeLog();
      SlsLog.sendLog({
        event,
        reqUrl,
        logConfig: {
          logData: {
            ...timeLog,
            timeStamp: {
              ...timeLog.timeStamp,
              ...timeStamp,
              er_end: Date.now()
            },
            er_duration: Date.now() - timeLog.timeStamp.er_start,
            hit_cache: '1'
          }
        }
      })
      return pressrCacheResponse;
    }
  } catch (e) {
  }
}

// 边缘预加载写逻辑
export async function addEdgeCache(ssrResponse: Response, event?: FetchEvent, customConfig?: any) {
  const request = customConfig && customConfig.url ? customConfig : event.request;
  const reqUrl = new URL(request.url);
  const { pathname, searchParams } = reqUrl;
  const kvConfig = RAX_CACHE_PAGE_LIST[pathname];
  const preCacheUrl = getPreloadCacheKey(request, reqUrl, kvConfig);
  ssrResponse.headers.set('x-er-hit-cache', 'false');

  // 验证是否是霸下的验证页面，如果是的话，不做预加载缓存
  const isBaXiaCheckPage = ssrResponse.headers.get('cache-control') === 'no-cache' ? true : false;

  // ssr预加载
  if ((searchParams.has('_pressr') || kvConfig) && preCacheUrl && !isBaXiaCheckPage) {
    try {
      const cacheFunc = async (ssrResponse, searchParams, kvConfig) => {
        if (ssrResponse.headers.get('x-precache-remain')) {
          return;
        }
        // 获取html文档内容
        const htmlText = await (ssrResponse.clone()).text();
        if (!htmlText.includes('<html>')) {
          return;
        }
        // 打点
        const resHTML = searchParams.has('_pressr') ? htmlText.replace('</head>', '<script>window._er_cache=true;try{if(!window.location.href.includes("_er_cache")){history.replaceState(null, "", window.location.href + (window.location.href.includes("?") ? "&_er_cache=true" : "?_er_cache=true"))}}catch(e){}</script></head>') :
          htmlText.replace('</head>', '<script>window._er_static=true;try{if(!window.location.href.includes("_er_static")){history.replaceState(null, "", window.location.href + (window.location.href.includes("?") ? "&_er_static=true" : "?_er_static=true"))}}catch(e){}</script></head>');
        ssrResponse = new Response(resHTML, {
          headers: ssrResponse.headers,
          status: 200
        })
        // 获取缓存key
        const pressrCache = ssrResponse.clone();
        const cacheSeconds = kvConfig ? `${kvConfig.cacheDuration * 60}` : searchParams.get('_preMaxAge') || '120';
        pressrCache.headers.set('cache-control', `max-age=${cacheSeconds}`);
        pressrCache.headers.set('x-cache-time', `${Date.now()}`);
        if (searchParams.has('_preNotClear')) {
          pressrCache.headers.set('x-er-cache-not-clear', 'true');
        }
        await cache.put(preCacheUrl, pressrCache);
      }
      event.waitUntil(cacheFunc(ssrResponse, searchParams, kvConfig));
    } catch (e) {
    }
  } 
}