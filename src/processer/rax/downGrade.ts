import {
  SlsLog,
  checkIsPre,
  checkRaxRedirectUrl
} from '../../utils/rax';
import {
  RAX_ORIGIN_HOST,
  PRE_RAX_ORIGIN_HOST
} from '../../constant/rax';

// 异常降级
export default async function (event: FetchEvent, e: any) {
  const { request } = event;
  const reqUrl = new URL(request.url);
  const timeLog = SlsLog.getTimeLog();
  const isPre = checkIsPre(reqUrl);

  if (request.url.includes('&_pressr=true')) {
    return 'console.log("pressr false")'
  }
 
  SlsLog.sendLog({
    event,
    reqUrl,
    logConfig: {
      logData: {
        ...timeLog,
        timeStamp: {
          ...timeLog.timeStamp,
          er_end: Date.now(),
        },
        er_duration: Date.now() - timeLog.timeStamp.er_start,
        erErrorMessage: `handleRaxSSRCacheError|${(e && e.message) || JSON.stringify(e)}`,
      }
    }
  });

  const origin = new URL(`https://${isPre? PRE_RAX_ORIGIN_HOST : RAX_ORIGIN_HOST}`);
  origin.pathname = reqUrl.pathname;
  origin.search = reqUrl.search;
  checkRaxRedirectUrl(origin, reqUrl.hostname, request);

  const queryStr = origin.href && origin.href.indexOf('?') > -1 ? '&_er_failback=1' : '?_er_failback=1';
  const redirectOriginUrl = origin.href + queryStr;

  return Response.redirect(redirectOriginUrl);
}