import {
  SlsLog
} from '../../utils/rax';

// 协商缓存检测逻辑
export async function checkForceCache(event:FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const headerCookie = request.headers.get('cookie') || '';
    if (request.headers.get('if-none-match')) {
      const etagReg = headerCookie.match(/_er_etag=(\S+);/) || headerCookie.match(/_er_etag=(\S+)$/);
      // 获取cookie中写入的etag值
      const etagCookie = etagReg && etagReg.length > 1 ? etagReg[1] : 'initial';
      const etagArray = request.headers.get('if-none-match').replace(`"`, '').replace(`W/`, '').split('-');
      if (etagArray.length > 1) {
        // 过期时间戳
        const etagTime = parseInt(etagArray[0]);
        const etagValue = etagArray[1];
        // 有效期内且etag值匹配正确走缓存
        if (etagTime >= Date.now() && etagCookie === etagValue) {
          const timeLog = SlsLog.getTimeLog();
          SlsLog.sendLog({
            event,
            reqUrl,
            logConfig: {
              logData: {
                ...timeLog,
                timeStamp: { 
                  ...timeLog.timeStamp,
                  er_end: Date.now()
                },
                isForceCache: true,
                er_duration: Date.now()- timeLog.timeStamp.er_start
              }
            }
          })
          return new Response('', {status: 304});
        }
      }
    }
  } catch(e) {
  }
}

// 协商缓存写
export async function addForceCache(ssrResponse: Response, request: Request) {
  try {
    const reqUrl = new URL(request.url);
    const { searchParams } = reqUrl;
    // 协商缓存
    if (searchParams.has('_http_etag')) {
      const headerCookie = request.headers.get('cookie') || '';
      const etagReg = headerCookie.match(/_er_etag=(\S+);/) || headerCookie.match(/_er_etag=(\S+)$/);
      const etagValue = etagReg && etagReg.length > 1 ? etagReg[1] : 'initial';
      const etagTime = Date.now() + parseInt(searchParams.get('_http_etag')) * 1000;
      // etag规范：过期时间戳-etag标识，加单个双引号用于弱匹配校验，详见https://aone.alibaba-inc.com/v2/project/1109730/req/55582160
      ssrResponse.headers.set('etag', `"${etagTime}-${etagValue}`);
    }
  } catch(e) {
  } 
}

// 强缓存写
export async function addForceHttpCache(ssrResponse: Response, request: Request) {
  try {
    const reqUrl = new URL(request.url);
    const { searchParams } = reqUrl;
    // 强缓存
    if (searchParams.has('_http_cache')) {
      ssrResponse.headers.set('cache-control', `max-age=${searchParams.get('_http_cache')}`)
    }
  } catch(e) {
  } 
}