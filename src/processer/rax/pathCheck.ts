// 路径规则校验
export default async function (event: FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const { pathname } = reqUrl;
    if (pathname.includes('_____tmd_____')) {
      return ;
    }

    // URL规则检测，异常url直接降级
    const groupName = pathname.match(/\/app\/trip\/(\S*)\/pages\//) && pathname.match(/\/app\/trip\/(\S*)\/pages\//)[1];
    const serviceName = pathname.match(/\/pages\/(\S*)/) && pathname.match(/\/pages\/(\S*)/)[1];
    const raxReg = /^[a-zA-Z0-9_-]+$/;
    const reactReg = /^[a-zA-Z0-9_\-/]+\.html$/;
    if (!groupName || !serviceName || groupName === 'null' || serviceName === 'null'
      || groupName === 'undefined' || serviceName === 'undefined' || !groupName.match(raxReg) ||
      (!serviceName.match(raxReg) && !serviceName.match(reactReg))) {
        throw new Error('pathError');
    }
  } catch (e) {
    throw new Error(e.message);
  }
}