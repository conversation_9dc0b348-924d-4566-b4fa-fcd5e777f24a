import {
  getPreloadCacheKey
} from '../../utils';
import { getHost } from '../../utils/rax';

export default async function (event: FetchEvent) {
  const { request } = event;
  const reqUrl = new URL(request.url);
  const { pathname, searchParams } = reqUrl;
  const searchString = '?' + searchParams.toString();
  const ssrOrigin = new URL(`https://${getHost(reqUrl)}`);
  ssrOrigin.pathname = pathname;
  ssrOrigin.search = searchString;
  const reqPath = ssrOrigin.toString();

  const preCacheUrl = getPreloadCacheKey(request, reqUrl);
  if (preCacheUrl) {
    // 删除边缘缓存
    await cache.delete(preCacheUrl);
    // 删除源站缓存
    await fetch(reqPath, {
      headers: request.headers,
        decompress: 'manual'
    })
    return 'success'
  }
  return 'failed' 
}