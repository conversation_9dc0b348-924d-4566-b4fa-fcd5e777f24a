import { SlsLog, checkIsPre, checkRaxRedirectUrl, getHost, judgeIsStream } from '../../utils/rax';
import { removeHtmlCacheHeaders, isWechat } from '../../utils';
import {
  RAX_CACHE_PAGE_LIST,
  SEO_PATH_LIST,
  STREAM_FORCE_MAP,
  STREAM_FORCE_CLOSE_ARR,
  WX_HOST,
} from '../../constant/rax';
import {
  addForceCache,
  addForceHttpCache
} from './forceCache';
import { addEdgeCache } from './edgeCache';
import cssInlineProcesser from './cssInline';
import pathMapProcesser from './pathMap';
import seoHandleProcesser from './seoHandle';
import useStreamRender from '../stream/useStreamRender';
// import streamProcesser from '../stream';

// 回源主链路
export default async function (event: FetchEvent) {
  try {
    const { request } = event;
    const reqUrl = new URL(request.url);
    const { pathname, searchParams } = reqUrl;

    const isPre = checkIsPre(reqUrl);
    const searchString = '?' + searchParams.toString();
    const oriHost = getHost(reqUrl);
    const ssrOrigin = new URL(`https://${oriHost}`);
    const kvConfig = RAX_CACHE_PAGE_LIST[pathname];

    const timeLog = SlsLog.getTimeLog();
    
    ssrOrigin.pathname = reqUrl.pathname;
    ssrOrigin.search = searchString;

    // 路径映射
    let reqPath = ssrOrigin.toString();
    const pathMapRes = await pathMapProcesser(reqPath, pathname, request, isPre, timeLog );
    reqPath = pathMapRes.reqPath;
    const csrSameSite = pathMapRes.csrSameSite;
    request.headers.set('x-csr-same-site', `${csrSameSite}`);
    

    // 微信不走流式(TODO:临时调试，通过url参数wx_er_stream=true来判断，打开微信限制)
    const isWxEr = WX_HOST.includes(reqUrl.hostname)
    const isStream = judgeIsStream(reqUrl, request.headers);

    checkRaxRedirectUrl(ssrOrigin, reqUrl.hostname, request, isStream, isPre);
    // 若是存在生产环境指定IP的标识，则回源到nativeHsf
    if(searchParams.has('hsfTypeIsNative')){reqPath = reqPath.replace(/(https:\/\/[^/]+\/)(?:[^/]+\/)?(app)/, '\$1nativehsf/\$2')}
    timeLog.backSSRPath = reqPath;

    // 通用流式SSR逻辑
    if (isStream) {
      const result = await useStreamRender(event, {
        reqUrl,
        ssrUrl: reqPath,
        secondPartSsrUrl: reqPath.indexOf('?') > -1 ? `${reqPath}&_only_show_second_part=1` : `${reqPath}?_only_show_second_part=1`,
        raxUrl: ssrOrigin.toString(),
        request,
        _startTime: Date.now(),
        isPre,
        timeLog
      });
      if (result !== false) {
        return result;
      }
    }

    // 增加请求头告之 SSR Faas 服务是从边缘节点来的
    request.headers.set('fli-bk', '1');
    // 回源5秒超时
    const timeoutDuration = isPre ? 20000 : 5000;
    const promiseList = [
      new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve('timeout');
        }, timeoutDuration);
      })
    ];

    if (searchParams.has('_wx_ssr_test_debug')) {
      return reqPath;
    }

    if (searchParams.has('_css_inline') || searchParams.has('_pressr') ||
      SEO_PATH_LIST.includes(reqUrl.pathname) || kvConfig || isWxEr) {
      promiseList.push(fetch(reqPath, {
        headers: request.headers
      }))
    } else {
      promiseList.push(fetch(reqPath, {
        headers: request.headers,
        decompress: 'manual'
      }))
    }

    // 回源请求
    timeLog.timeStamp.faas_req = Date.now();
    let ssrResponse: any = await Promise.race(promiseList);
    timeLog.timeStamp.faas_res = Date.now();
    timeLog.faas_duration = timeLog.timeStamp.faas_res - timeLog.timeStamp.faas_req;

    if (ssrResponse && ssrResponse.headers && ssrResponse.headers.get('X-Ssr-Start') && ssrResponse.headers.get('X-Ssr-End')) {
      timeLog.timeStamp.ssr_start = parseInt(ssrResponse.headers.get('X-Ssr-Start'));
      timeLog.timeStamp.ssr_end = parseInt(ssrResponse.headers.get('X-Ssr-End'));
      timeLog.ssr_duration = timeLog.timeStamp.ssr_end - timeLog.timeStamp.ssr_start;
      timeLog.faas_req_duration = timeLog.timeStamp.ssr_start - timeLog.timeStamp.faas_req;
      timeLog.faas_res_duration = timeLog.timeStamp.faas_res - timeLog.timeStamp.ssr_end;
    }

    // 源站返回302
    if (ssrResponse && ssrResponse.status === 302) {
      // 打日志
      SlsLog.sendLog({
        event,
        reqUrl,
        logConfig: {
          logData: {
            ...timeLog,
            timeStamp: {
              ...timeLog.timeStamp,
              er_end: Date.now()
            },
            er_duration: Date.now() - timeLog.timeStamp.er_start
          }
        }
      })
      if (searchParams.has('_pressr') && !searchParams.has('_sw_prefetch')) {
        return 'console.log("pressr false")'
      }
      return ssrResponse;
    }

    // 结果异常，源站返回异常
    if (typeof ssrResponse === 'string' || !ssrResponse || (ssrResponse && !ssrResponse.ok)) {
      let errorMessage = ssrResponse || 'ssrResponse error';
      try {
        errorMessage = await ssrResponse.text();
      } catch (err) {
        errorMessage = (err && err.message) || JSON.stringify(err);
      }
      SlsLog.setTimeLog({
        ...timeLog
      })
      throw new Error(errorMessage);
    }

    // css内联
    ssrResponse = await cssInlineProcesser(ssrResponse, request);

    // 针对首页seo做字符串替换
    ssrResponse = await seoHandleProcesser(ssrResponse, request);

    // 移除缓存相关的 header，禁止客户端缓存（否则首屏数据也无法更新）
    removeHtmlCacheHeaders(ssrResponse.headers);

    // 边缘预加载
    addEdgeCache(ssrResponse, event);

    // 强缓存
    addForceHttpCache(ssrResponse, request);

    // 协商缓存
    addForceCache(ssrResponse, request);

    // 对返回数据进行过滤，如果是预请求，返回的结果是霸下的内容，就返回302，用于兜底客户端缓存错误的场景
    try{
      const { searchParams } = new URL(request.headers.get('originurl') || '') || {};
      if(ssrResponse.headers.get('cache-control') === 'no-cache' && searchParams.get('_fli_preload_from') && searchParams.get('_fli_preload_from') !== 'prefetch'){
        ssrResponse.status = 302;
      }
    }catch(e){}


    // 打日志
    SlsLog.sendLog({
      event,
      reqUrl,
      logConfig: {
        logData: {
          ...timeLog,
          timeStamp: {
            ...timeLog.timeStamp,
            er_end: Date.now(),
            originurl: request.headers.get('originurl'),
            cacheControl: ssrResponse.headers.get('cache-control')
          },
          er_duration: Date.now() - timeLog.timeStamp.er_start
        }
      }
    })

    // 预加载仅返回固定字符串
    if (searchParams.has('_pressr') && !searchParams.has('_sw_prefetch')) {
      return 'console.log("pressr success")'
    }

    return ssrResponse;

  } catch (e) {
    throw new Error(e.message);
  }
}
