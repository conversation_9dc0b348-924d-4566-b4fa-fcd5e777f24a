import {
  getKvCache,
  getAppName,
  isWechat
} from '../../utils';
import {
  checkIsPre,
  SlsLog
} from '../../utils/rax';
import {
  RAX_SSR_HOST,
  NEW_RAX_SSR_HOST,
  NEW_PRE_RAX_SSR_HOST
} from '../../constant/rax'
import { PATH_MAP_NEW_BACK_UP } from '../../constant/pathMapNew'

// 路径映射，行业函数分发，25ms-75ms随机触发
export default async function (reqPath: string, pathname: string, request, isPre: boolean, timeLog: any) {
  try {
    const timeStamp: any = {};
    timeStamp.path_kv_start = Date.now();
    const pathPromise = [
      new Promise(async (resolve, reject) => {
        const cRes = await getKvCache('fliggyrax_124215', 'path-map-new');
        // 预发无KV，不返回错误结果
        if(!isPre){resolve(cRes)}
      }), 
      new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve({source: 'timeout', data: PATH_MAP_NEW_BACK_UP});
        }, 50);
      }), 
      new Promise(async (resolve, reject)=>{
        try{
          const result: any = await fetch(`https://cdnoss.fliggy.com/path-map-new/config.html`,{cdnProxy: true,decompress: "manual"}).then(r=> r&& r.json()).catch(e=> {return {}})
          if(result && result.newFuncConfig){resolve({data: result, source: 'oss'})}
        }catch(e){}
      })
    ];

    const pathKvCache: any = await Promise.race(pathPromise);
    timeStamp.path_kv_end = Date.now();
    timeStamp.path_source = pathKvCache?.source || 'kv';
    timeStamp.path_kv_duration = timeStamp.path_kv_end - timeStamp.path_kv_start;
    const pathRes = (!pathKvCache?.source && pathKvCache.data) ? JSON.parse(pathKvCache.data) : pathKvCache.data || PATH_MAP_NEW_BACK_UP;
    timeStamp.path_new = pathRes[pathname] ? pathRes[pathname].path : 'none';

    // 获取userid
    const cookie = request.headers.get('cookie') || '';
    const matches = cookie.match(/unb=(\d+);/);
    let cookieUserId = '';
    if (matches && matches.length > 1) {
      cookieUserId = (matches[1])
    }
    const cookieUserIdSuf = cookieUserId ? cookieUserId.slice(-2) : '99';

    // 老函数全部切流到公共新源站函数
    reqPath = reqPath.replace(RAX_SSR_HOST, NEW_RAX_SSR_HOST);

    try{
      // 分发源站流量
      // 获取行业
      const businessType = pathRes[pathname]?.path.split("/")?.[1] || 'app';
      // 行业切流配置
      const businessConfig = pathRes.newRenderSdkConfig?.[isPre ? 'pre' : 'online']?.[businessType] || {};
      // 在人群内，切流
      const isInUserList = (businessConfig.uidList || []).includes(cookieUserId)
      // 在url列表内，根据灰度切流
      const isInUrlList = (businessConfig.urls?.[pathname] || 0) > (cookieUserIdSuf ? parseInt(cookieUserIdSuf) : 0)
      // 行业整体切流，根据灰度切流
      const isInBusinessGray = (businessConfig.businessGray || 0) > (cookieUserIdSuf ? parseInt(cookieUserIdSuf) : 0)
      timeStamp.testNewMap = JSON.stringify({
        isPre,
        businessType,
        businessConfig,
        isInUserList,
        isInUrlList,
        isInBusinessGray,
      })
      // 满足切流逻辑，切到行业函数
      if((isInUserList || isInUrlList || isInBusinessGray) && businessConfig.raxUrl){
        timeStamp.testNewMapReplace = JSON.stringify({in: true, raxUrl: businessConfig.raxUrl, NEW_RAX_SSR_HOST})
        // 统一替换新的行业源站域名
        if(isPre){
          reqPath = reqPath.replace(NEW_PRE_RAX_SSR_HOST, businessConfig.raxUrl);
        }else{
          reqPath = reqPath.replace(NEW_RAX_SSR_HOST, businessConfig.raxUrl);
        }
      }
    }catch(e){console.log("new_map_err", e.message)}

    timeStamp.reqPath = reqPath;

    // 将er_start拉出复制进来(兜底使用path_kv_start)
    const er_start = timeLog.timeStamp.er_start || timeStamp.path_kv_start;
    timeStamp.er_start = er_start;

    timeLog.timeStamp = timeStamp;

    // TODO函数切换配置
    const newFuncConfig = pathRes.newFuncConfig || {};
    let csrSameSite = false;

    if (newFuncConfig.vm && newFuncConfig.vm[pathname]) {
      // vm链路手动切换
      reqPath = reqPath.replace(pathname, `/vm${pathname}`);
    } else if (pathRes[pathname] && pathRes[pathname].path) {
      reqPath = reqPath.replace(pathname, pathRes[pathname].path);
      csrSameSite = pathRes[pathname].csrSameSite || false;
    } else if (pathRes[pathname]) {
      csrSameSite = pathRes[pathname].csrSameSite || false;
    }

    // 店铺框架页 reqPath 处理
    if (reqPath.includes('/app/trip/rx-shop-fone/pages/home')) {
      reqPath = reqPath.replace('/app/trip/rx-shop-fone/pages/home', '/shop/app/trip/rx-shop-fone/pages/home');
    }

    // 获取APPName
    const ua = request.headers.get('user-agent') || '';

    // 度假旅游频道 reqPath 处理（旧链接兜底跳新版）
    // 微信入口暂时不切，产品适配还没发布
    if (reqPath.includes('/app/trip/rx-channels-2023/pages/main') && !isWechat(ua)) {
      reqPath = reqPath.replace('/app/trip/rx-channels-2023/pages/main', '/app/trip/rx-channels-2024/pages/main');
    }

    // 酒店详情 reqPath 处理
    const reg = /(\/hotel)?\/app\/trip\/rx-hotel-detail\/pages\/detail/i;

    if (reg.test(reqPath)) {
      reqPath = reqPath.replace(reg, '/travelshop/app/trip/rx-hotel-detail/pages/detail');
    }

    return { reqPath, csrSameSite };
  } catch (e) {
    return { reqPath, csrSameSite: false, msg: e.message };
  }

}
