// 针对首页seo做字符串替换
export default async function (ssrResponse: Response, request: Request) {
  try {
    const reqUrl = new URL(request.url);
    if (reqUrl.pathname === '/app/trip/h5-lazada-weather-seo/pages/detail/index.html' || reqUrl.pathname === '/app/trip/rx-weather-seo-mobile/pages/detail') {
      // 获取html文档内容
      const htmlText = await (ssrResponse.clone()).text();
      // 获取变量
      // 城市POI辖区县全名
      const totalNameStrArr = htmlText.match(/_er_replace_start_\{\{(.+?)\}\}_er_replace_end_/);
      const totalName = totalNameStrArr && totalNameStrArr.length > 1 && totalNameStrArr[1] || '';
      // 特殊title
      const sepcialTitleStrArr = htmlText.match(/_er_special_title_start_\{\{(.+?)\}\}_er_special_title_end_/);
      const specialTitle = sepcialTitleStrArr && sepcialTitleStrArr.length > 1 && sepcialTitleStrArr[1] || '';
      // 特殊desc
      const sepcialDescStrArr = htmlText.match(/_er_special_desc_start_\{\{(.+?)\}\}_er_special_desc_end_/);
      const specialDesc = sepcialDescStrArr && sepcialDescStrArr.length > 1 && sepcialDescStrArr[1] || '';
      // 省份(放location区块里的)
      const provinceStrArr = htmlText.match(/_er_replace_province_start_\{\{(.+?)\}\}_er_replace_province_end_/);
      const provinceToDisplay = provinceStrArr && provinceStrArr.length > 1 && provinceStrArr[1] || '';
      // 城市(放location区块里的)
      const cityLocationStrArr = htmlText.match(/_er_replace_city_start_\{\{(.+?)\}\}_er_replace_city_end_/);
      const cityLocationToDisplay = cityLocationStrArr && cityLocationStrArr.length > 1 && cityLocationStrArr[1] || '';
      // 经度(放location区块里的)
      const longitudeStrArr = htmlText.match(/_er_replace_longitude_start_\{\{(.+?)\}\}_er_replace_longitude_end_/);
      const longitudeToDisplay = longitudeStrArr && longitudeStrArr.length > 1 && longitudeStrArr[1] || '';
      // 经度(放location区块里的)
      const latitudeStrArr = htmlText.match(/_er_replace_latitude_start_\{\{(.+?)\}\}_er_replace_latitude_end_/);
      const latitudeToDisplay = latitudeStrArr && latitudeStrArr.length > 1 && latitudeStrArr[1] || '';
      // picture区块
      const pictureStrArr = htmlText.match(/_er_replace_picture_start_\{\{(.+?)\}\}_er_replace_picture_end_/);
      const pictureToDisplay = pictureStrArr && pictureStrArr.length > 1 && pictureStrArr[1] || '';
      // canonical url区块
      const canonicalUrlStrArr = htmlText.match(/_er_replace_canonical_url_start_\{\{(.+?)\}\}_er_replace_canonical_url_end_/);
      const canonicalUrl = canonicalUrlStrArr && canonicalUrlStrArr.length > 1 && canonicalUrlStrArr[1] || '';
      // h5 url区块
      const h5UrlStrArr = htmlText.match(/_er_replace_h5_url_start_\{\{(.+?)\}\}_er_replace_h5_url_end_/);
      const h5Url = h5UrlStrArr && h5UrlStrArr.length > 1 && h5UrlStrArr[1] || '';

      // 字符串替换
      const resHTML = htmlText.replace(/_city_or_spot_name_/g, totalName)
        .replace(/_special_title_/g, specialTitle)
        .replace(/_special_desc_/g, specialDesc)
        .replace(/_location_province_/g, provinceToDisplay)
        .replace(/_location_city_/g, cityLocationToDisplay)
        .replace(/_location_longitude_/g, longitudeToDisplay)
        .replace(/_location_latitude_/g, latitudeToDisplay)
        .replace(/_picture_in_meta_/g, pictureToDisplay)
        .replace(/https:\/\/www\.canonicalplaceholder\.html/g, canonicalUrl)
        .replace(/https:\/\/www\.h5urlplaceholder\.html/g, h5Url);
      // 重写res
      ssrResponse = new Response(resHTML, {
        headers: ssrResponse.headers,
        status: 200
      });
    }
    // 针对内容图文详情首页seo
    else if (reqUrl.pathname === '/app/trip/rx-content-seo/pages/detail') {
      // 获取html文档内容
      const htmlText = await (ssrResponse.clone()).text();
      // 获取内容标题，变量
      const contentTitleArr = htmlText.match(/content_title_start_\{\{(.+?)\}\}_content_title_end/);
      const contentTitle = contentTitleArr && contentTitleArr.length > 1 && contentTitleArr[1] || '';
      // 获取keywords
      const poiNameList = htmlText.match(/poi_name_start_\{\{(.+?)\}\}_poi_name_end/);
      const poiName = poiNameList && poiNameList.length > 1 && poiNameList[1] || '';
      const contentTextArr = htmlText.match(/content_text_start_\{\{(.+?)\}\}_content_text_end/);
      const contentText = contentTextArr && contentTextArr.length > 1 && contentTextArr[1] || '';
      // 变量替换
      const resHTML = htmlText.replace(/_fliggy_content_title_/g, contentTitle)
        .replace(/_poi_name_/g, poiName)
        .replace(/_content_text_/g, contentText)

      // 重写res
      ssrResponse = new Response(resHTML, {
        headers: ssrResponse.headers,
        status: 200
      });
    }

    return ssrResponse;
  } catch (e) {
    return ssrResponse;
  }
}