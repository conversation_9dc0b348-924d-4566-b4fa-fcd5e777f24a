interface ICache {
  put(k: Request | string, v: Response): Promise<void>;
  get(k: Request | string): Promise<Response>;
  delete(k: Request | string): Promise<boolean>;
}

declare var cache: ICache;

declare class EdgeKV {
  constructor({ namespace: string });

  get(
    k: string,
    op?: { type: 'stream' | 'text' | 'json' | 'arrayBuffer' },
  ): Promise<any>;

  delete(k: string): Promise<boolean>;
}

declare class HTMLStream {
  constructor(a: any, b: any[]);
}

declare interface Console {
  alert: (msg: string) => void;
}

interface IErFetchRequestInit extends RequestInit {
  cdnProxy?: boolean;
  decompress?: string;
}
declare function fetch(
  input: RequestInfo | URL,
  init?: IErFetchRequestInit,
): Promise<Response>;

declare var process: any;
