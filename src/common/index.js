const MATCH_UA = [
  'iphone',
  'aliapp',
];

const SSR_ORIGIN_HOST = 'fliggyssr.taobao.com';
const CACHE_ORIGIN_HOST = 'f.m.taobao.com';

/**
 * 处理手淘首猜参数
 * @param {String} searchString
 */
function handleSearch(searchString) {
  try {
    let result = searchString;
    if (searchString) {
      result = result.replace('?', '');
      result = result.split('&');
      result = result.filter(item => {
        return item.indexOf('utparam=') < 0 &&
          item.indexOf('_fli_nav_dynamic_router_start_time=') < 0 &&
          item.indexOf('_fli_nav_dynamic_router_end_time=') < 0 &&
          item.indexOf('imei=') < 0
      });
      if (result.length > 0) {
        result = `?${result.join('&')}`;
      } else {
        result = '';
      }
    }
    return result;
  } catch (err) {
    return searchString;
  }
}

/**
 * 处理参数,只保留有效参数
 * @param {String} params
 */
function handleSearchParams(params) {
  const wh_pid = params.get('wh_pid');
  const itemIds = params.get('itemIds');
  const rid = params.get('rid');
  const topTabId = params.get('topTabId');
  const source_name = params.get('source_name');
  const ttid = params.get('ttid');
  const titleBarHidden = params.get('titleBarHidden');
  const disableNav = params.get('disableNav');

  const paramsObj = {
    wh_pid,
    itemIds,
    rid,
    topTabId,
    source_name,
    ttid,
    titleBarHidden,
    disableNav
  }
  // 保证顺序一致用于静态SSR缓存
  const paramsList = [
    'wh_pid',
    'itemIds',
    'rid',
    'topTabId',
    'source_name',
    'ttid',
    'titleBarHidden',
    'disableNav'
  ]
  let result = '?';
  paramsList.forEach(item => {
    if (paramsObj[item]) {
      result = `${result}${item}=${paramsObj[item]}&`
    }
  })
  return result;
}

/**
 * Make a response to client
 * @param {Event} event
 */
export async function handleRequest(event, redictReqUrl) {
  const { request } = event;

  // 删减一下多余的headers，防止超过8K导致ssr降级
  try {
    [
      'ali-origin-real-url',
      'ali-swift-private-hash-key',
      'ali-proxy-consistent-hash',
      'ali-tengine-hash-key',
    ].forEach(key => {
      request.headers.delete(key);
    });
  } catch {}

  const isPre = !(request.url.indexOf('outfliggys.m.taobao.com') >= 0 ||
    request.url.indexOf('proxy-er.feizhu.com') >= 0 ||
    request.url.indexOf('proxy-er.fzwxxcx.com') >= 0 ||
    request.url.indexOf('proxy-er.fzwxxcx.cn') >= 0);

  let reqUrl = null;
  try {
    reqUrl = reqUrl = redictReqUrl ? new URL(redictReqUrl): new URL(request.url);
  } catch (err) {
    // 链接超长g了 https://aliyuque.antfin.com/trip_plat/pu6xpg/tds6kbq3n4i7y22y
    // 非线上环境
    if (isPre) {
      return err.message;
    }
    // 缩短链接再来一次orz
    // 实验结果大约在4000字符长度，但后续处理要加参数肯定又g了，所以选择在3800左右保险
    // 但万一以后3800也报错了呢，兜底截取当前链接的3/4
    const nextUrl = request.url.length > 3800 ? request.url.slice(0,3800) : request.url.slice(0,Math.floor(request.url.length*0.75))
    return Response.redirect(`${nextUrl}&_er_error=urltoolong`);
  }


  /**
   * 新版本chrome（>= 122.0.6261.69），Accept-Encoding 会增加 zstd 压缩，而 ER 目前不支持 zstd 会导致解压失败。
   * https://chromestatus.com/feature/6186023867908096
   *
   * 解决办法是：先重写 Accept-Encoding，去掉zstd压缩，等630后 ER 会兼容，再去掉这段逻辑。
   * */
  if (request.headers) {
    request.headers.set('Accept-Encoding', 'gzip');
  }

  // sid更新专用 by @紫期
  if (reqUrl.pathname.startsWith('/updateSid/')) {
    try {
      const searchParams = reqUrl.searchParams;
      const sid = searchParams.get('sid');
      const sidRes = await fetch(`https://fliggyrax.taobao.com/updateSid/index?sid=${sid}`, {
        headers: request.headers
      });
      return sidRes;
    } catch(e) {
      return ''
    }
  }

  // 针对 Rax 源码项目做判断 by @愚舟
  if (reqUrl.pathname.startsWith('/app/')) {
    // app/trip 飞猪通用仓库
    // app/rxplugin 飞猪一码多端仓库
    return await handleRaxSSRCache(request, event);
  }

  // ssr同域名降级逻辑
  if (reqUrl.pathname.startsWith('/csr/')) {
    const hostname = reqUrl.hostname;
    let csrHost = RAX_ORIGIN_HOST;
    if(WX_HOST.includes(hostname)){
      csrHost = hostname.replace('proxy-er', 'proxy-h5')
    }else if(GD_HOST.includes(hostname)){
      csrHost = hostname.replace('front-traffic-fliggy-er', 'front-traffic-fliggy')
    }
    const csrUrl = `https://${csrHost}${reqUrl.pathname}`.replace('/csr/', '/');
    const csrRes = await fetch(csrUrl, { cdnProxy: true, decompress: 'manual' });
    return csrRes;
  }

  // 针对资源链路处理流程 by @极战
  if (reqUrl.pathname.startsWith('/resource/')) {
    return await handleResource(request, event);
  }

  const origin = new URL(`https://${CACHE_ORIGIN_HOST}`)
  const searchParams = reqUrl.searchParams;
  const searchString = handleSearch(reqUrl.search);
  origin.pathname = reqUrl.pathname;
  origin.search = searchString;
  checkRaxRedirectUrl(origin, reqUrl.hostname);
  if (!reqUrl.pathname.startsWith('/wow/')) {
    return Response.redirect(origin.toString());
  }
  

  try {
    // 快速失败测试
    if (request.url.includes('bypassRedirect')) {
      return Response.redirect(origin.toString());
    }
    if (request.url.includes('bypassFail')) {
      throw new Error('bypass fail');
    }

    // ssr预加载读缓存,25ms-75ms随机触发
    const preCacheUrl = searchParams.has('_fl_auto_preload_spm') ? false : getPreloadCacheKey(request, reqUrl);
    const prePromise = [new Promise(async(resolve, reject) => {
      const cRes = await cache.get(preCacheUrl);
      resolve(cRes);
    }), new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve(false);
      }, 50);
    })];
    const pressrCacheResponse = searchParams.has('_pressr') || !preCacheUrl ? false : await Promise.race(prePromise);
    if (pressrCacheResponse) {
      pressrCacheResponse.headers.set('content-type', 'text/html; charset=utf-8')
      pressrCacheResponse.headers.set('x-er-hit-cache', 'true');
      removeErCacheHeaders(pressrCacheResponse.headers);
      removeHtmlCacheHeaders(pressrCacheResponse.headers);
      if (!pressrCacheResponse.headers.get('x-er-cache-not-clear')) {
        await cache.delete(preCacheUrl);
      }
      return pressrCacheResponse;
    }

    //会场静态预加载 @兆兆
    const whpid = searchParams.get('wh_pid');
    const ua = request.headers.get('user-agent') || '';
    const appName = getAppName(ua);
    const sb_redirect_auto = searchParams.get('sb_redirect_auto');
    const push_flag = searchParams.get('push_flag');
    const share_source = searchParams.get('shareId');
    const isPush = sb_redirect_auto || push_flag || share_source === 'weixin_friend_card' || appName === 'AP' ? true : false;

    //是否在缓存配置中
    let isStaticeSSR = false;
    try {
      const edgeKv = new EdgeKV({ namespace: "fliggypcraft" });
      const pageConfig = await edgeKv.get('act_ssr_config', { type: 'json' }) || [];
      const hitConfig = pageConfig.find(item => item?.whpid === whpid) || {};

      if (hitConfig) {
        const { refreshTimeList = [], status = 'init', type = 'push' } = hitConfig;
        isStaticeSSR = (status === 'enabled' || request.url.includes('forceCache')) && (type === 'push' ? isPush : true);
        const searchCacheString = handleSearchParams(searchParams);
        const originUrl = `http://${SSR_ORIGIN_HOST}${reqUrl.pathname}${searchCacheString}`;
        const prePromise = [new Promise(async(resolve, reject) => {
          const cRes = cache.get(originUrl);
          resolve(cRes);
        }), new Promise((resolve, reject) => {
          setTimeout(() => {
            resolve(false);
          }, 50);
        })];
        const cacheResponse = await Promise.race(prePromise);
        const cacheTime = cacheResponse?.headers?.get('x-cache-time') || null; //缓存时间

        let hitRefreshTime = null; //当前命中刷新时间
        refreshTimeList.forEach(item => {
          if (item <= Date.now()) hitRefreshTime = item;
        })
        const needRefresh = hitRefreshTime > cacheTime; //是否需要刷新缓存

        //返回缓存页面
        if (isStaticeSSR && cacheResponse && !needRefresh) {
          removeErCacheHeaders(cacheResponse.headers)
          removeHtmlCacheHeaders(cacheResponse.headers)
          cacheResponse.headers.set('content-type', 'text/html; charset=utf-8')
          return cacheResponse;
        }
      }
    } catch (err) {
    }

    // 缓存清理测试
    if (request.url.includes('pcraftCacheDelete')) {
      const searchCacheString = handleSearchParams(searchParams);
      const originUrl = `http://${SSR_ORIGIN_HOST}${reqUrl.pathname}${searchCacheString}`;
      await cache.delete(originUrl);
    }

    // 处理手淘首猜链接带的utparam
    if (request.url.includes('utparam') ||
      request.url.includes('_fli_nav_dynamic_router_start_time') ||
      request.url.includes('_fli_nav_dynamic_router_end_time') ||
      request.url.includes('imei')) {
      request.headers.set('ali-proxy-consistent-hash', `${reqUrl.pathname}${searchString}`)
      request.headers.set('ali-origin-real-url', `${reqUrl.origin}${reqUrl.pathname}${searchString}`)
    }

    request.headers.delete('host');

    const ssrOrigin = new URL(`https://${SSR_ORIGIN_HOST}`);
    ssrOrigin.pathname = reqUrl.pathname;
    ssrOrigin.search = isStaticeSSR ? `${searchString}&fromCDN=true` : searchString;

    request.headers.delete('x-client-scheme');
    // 开始处理真正 SSR 部分
    try {
      let taskResult;
      // let promiseList = [cachePromise];
      let promiseList = [];
      promiseList.push(fetch(ssrOrigin.toString(), {
        headers: request.headers,
      }))

      if (promiseList.length > 1) {
        taskResult = await Promise.race(promiseList);
      } else {
        taskResult = await promiseList[0];
      }

      // 命中缓存即返回
      if (taskResult.headers.get('x-er-hit-cache-interval')) {
        return taskResult;
      }

      if (!taskResult.ok) {
        return Response.redirect(origin.toString());
      }

      const htmlStream = searchParams.has('_pressr') || isStaticeSSR ?
      new HTMLStream(
        taskResult.body,
        [[
          'head',
          {
            element(e) {
              e.append('<script>window._er_cache=true;try{if(!window.location.href.includes("_er_cache")){history.replaceState(null, "", window.location.href + (window.location.href.includes("?") ? "&_er_cache=true" : "?_er_cache=true"))}}catch(e){}</script>', {
                html: true,
              });
            }
          }
        ]]
      ) : '';
      removeHtmlCacheHeaders(taskResult.headers)
      let pcraftRes = searchParams.has('_pressr') || isStaticeSSR ? new Response(htmlStream, {
        headers: taskResult.headers,
        status: 200,
      }) : taskResult;

      // 静态缓存测试
      if (isStaticeSSR) {
        const searchCacheString = handleSearchParams(searchParams);
        const originUrl = `http://${SSR_ORIGIN_HOST}${reqUrl.pathname}${searchCacheString}`;
        try {
          const cacheRes = pcraftRes.clone();
          cacheRes.headers.set('cache-control', `max-age=10800`);
          cacheRes.headers.set('x-cache-time', `${Date.now()}`)
          await cache.put(originUrl, cacheRes);
        } catch (err) {
        }
      }

      // ssr预加载
      if (searchParams.has('_pressr') && preCacheUrl) {
        try {
          // 获取缓存key
          const pressrCache = pcraftRes.clone();
          const cacheSeconds = searchParams.get('_preMaxAge') || '120';
          pressrCache.headers.set('cache-control', `max-age=${cacheSeconds}`);
          pressrCache.headers.set('x-cache-time', `${Date.now()}`);
          if (searchParams.has('_preNotClear')) {
            pressrCache.headers.set('x-er-cache-not-clear', 'true');
          }
          await cache.put(preCacheUrl, pressrCache);
          // return new Response("Preload Success");
        } catch (e) {
          // return new Response("Preload fail");
        }
      }
      pcraftRes.headers.set('x-er-hit-cache', 'false');
      return pcraftRes;
    } catch (err) {
      return Response.redirect(origin.toString());
    }
  } catch (e) {
    // 解决链接参数异常取searchParams报错问题
    const reqUrl = redictReqUrl ? new URL(redictReqUrl): new URL(request.url);
    const origin = new URL(`https://${CACHE_ORIGIN_HOST}`);
    origin.pathname = reqUrl.pathname;
    origin.search = reqUrl.search;
    checkRaxRedirectUrl(origin, reqUrl.hostname);
    const redirectOriginUrl = origin.href;
    return Response.redirect(redirectOriginUrl);
  }
}


/**
 * ==========================================================
 * 以下是资源链路处理流程 for极战
 * ==========================================================
 */

async function handleResource(request, event) {
  const reqUrl = new URL(request.url);
  const searchParams = reqUrl.searchParams;
  const key = searchParams.get('key');
  const isPre = searchParams.get('isPre');
  try {
    if (key && isPre) {
      // 预发链路
      const resourceRes = await fetch(`https://fliggyrax.taobao.com/resource?key=${key}`, {
        headers: {
          'content-type': 'text/plain; charset=utf-8',
        }
      })
      const resourceText = await resourceRes.text();
      return new Response(resourceText, {
        headers: {
          'content-type': 'text/plain; charset=utf-8'
        },
        status: 200
      });
    } else if (key) {
      // 线上
      const resourceKvCache = await getKvCache('fliggy_resource', key);
      if (resourceKvCache.data) {
        try {
          const resourceRes = JSON.parse(resourceKvCache.data);
          let reqUrl = ''
          // 灰度
          if (resourceRes.gray && resourceRes.gray.path && resourceRes.gray.ratio) {
            const headers = request.headers;
            const cookie = headers.get('cookie') || '';
            const matches = cookie.match(/unb=(\d+);/);
            if (matches && matches.length > 1) {
              const cookieUserId = (matches[1] || '').slice(-2);
              if (parseInt(cookieUserId) < resourceRes.gray.ratio) {
                reqUrl = resourceRes.gray.path;
              } else {
                reqUrl = resourceRes.prod;
              }
            } else {
              reqUrl = resourceRes.prod;
            }
          } else {
            reqUrl = resourceRes.prod;
          }
          if (reqUrl) {
            // 请求resource数据
            const rRes = await fetch(reqUrl, {
              headers: {
                'content-type': 'text/plain; charset=utf-8',
              }
            });
            const resourceText = await rRes.text();
            return new Response(resourceText, {
              headers: {
                'content-type': 'text/plain; charset=utf-8'
              },
              status: 200
            });
          } else {
            return ''
          }
        } catch (e) {
          return '';
        }
      }
    }
    return ''
  } catch (e) {
    return '';
  }
}

/**
 * ==========================================================
 * 资源链路处理流程结束
 * ==========================================================
 */






/**
 * ==========================================================
 * 以下是源码链路 SSR 处理流程
 * ==========================================================
 */

// 通用工具
const UTIL = {
  /**
   * 从html文本中获取meta标签的值
   * <meta name="aaa" content="bbb" />
   * @param {String} htmlString html文本
   * @param {String} metaName 对应meta标签的name值
   * @returns String
   */
  getMetaData,
  /**
   * 获取简易缓存的json
   * @param {String} cacheKey 访问的pathname+自定义search
   * @returns {Promise<Object>} 缓存结果
   *   - cacheKey {string} 对应入参cacheKey
   *   - errorMessage {string} 失败时才有的错误信息
   *   - data {Object} 默认是json
   */
  getJsonCache,
  /**
   * 设置简易缓存
   * @param {String} cacheKey 访问的pathname+自定义search
   * @param {Object} jsonData
   * @param {number} seconds 缓存
   * @returns {Promise<Object>} 缓存结果
   *   - cacheKey {string} 对应入参cacheKey
   *   - errorMessage {string} 失败时才有的错误信息
   */
  setJsonCache,
  /**
   * 简单模板处理函数，将模板中的{{key}}替换为data.key对应的值
   * @param {Stirng} templateString 模板文本
   * @param {Object} data 待替换的数据
   */
  parseTemplate
}
const RAX_SSR_HOST = 'fliggyrax.taobao.com';
const PRE_RAX_SSR_HOST = 'pre-fliggyrax.wapa.taobao.com';
const RAX_ORIGIN_HOST = 'market.m.taobao.com';
const HOLDER_START_REG = /\<div\s+id=['"]ssr-er-holder['"]/g;
const ROOT_BEFORE_START_REG = /\<div\s+id=['"]root_before['"]/g;
const ROOT_START_REG = /\<div\s+id=['"]root['"]/g;
const SECOND_STREAM_END = /\<div\s+id=['"]__SECOND_STREAM_END__['"].*?>\<\/div\>/g
const SERVER_START_REG = /\<script\s+data-from=['"]server['"]/g;
const SLS_URL = 'https://log.fliggy.com/api/rax/record';
const WX_HOST = [
  'proxy-er.feizhu.com',
  'proxy-er.fzwxxcx.com',
  'proxy-er.fzwxxcx.cn',
  'pre-proxy-er.feizhu.com',
  'pre-proxy-er.fzwxxcx.com',
  'pre-proxy-er.fzwxxcx.cn'
]
// 火车票高德域名
const GD_HOST = [
  'front-traffic-fliggy-er.amap.com',
  'pre-front-traffic-fliggy-er.amap.com'
]

// 静态SSR页面白名单
const RAX_CACHE_PAGE_LIST = {
  "/app/trip/rx-trip-ticket/pages/detail": {
    key: ['poiId'],
    cacheDuration: 14400,
    requiredKey: 'poiId',
    kvName: 'static-config'
  },
  "/app/trip/rx-ssr-bundle-test/pages/detail": {
    key: ['snapshoot', 'depLocation', 'arrLocation', 'depDate', 'passengerType',
    'depLocationCode','depAreaName', 'depAreaCode', 'depType', 'arrType',
    'arrLocationCode', 'arrAreaName', 'arrAreaCode', 'onlyGD'],
    cacheDuration: 1440,
    requiredKey: 'snapshoot'
  },
  "/app/trip/rx-flight-test-ssr/pages/listing": {
    key: ['snapshot', 'leaveDate', 'depCityName', 'arrCityCode', 'arrCityName', 'crossRights'],
    cacheDuration: 1440,
    requiredKey: 'snapshot'
  }
};

// 缓存读取黑名单，黑名单内页面不走缓存
const RAX_CACHE_BLACK_LIST = {
  "/app/trip/rx-miniapp-home/pages/home": true
};

// 用户黑名单，直接降级
const USER_BLACK_LIST = [
  "411605170",
  "2212616903381"
]

// 手淘PHA白名单
const TAOBAO_PHA_WHITE_LIST = [
  "2215073891875",
  "2213195372323",
  "2212505824856",
  "2212616903381"
]

/**
 * 处理源码 SSR 逻辑
 * @param request
 * @param event
 * @returns {Promise<Response|string>}
 */
async function handleRaxSSRCache(request, event) {
  const timeLog = {
    timeStamp: {
      er_start: Date.now()
    }
  };
  const reqUrl = new URL(request.url);
  const isPre = !(reqUrl.hostname === 'outfliggys.m.taobao.com' ||
  (WX_HOST.includes(reqUrl.hostname) && !reqUrl.hostname.includes('pre-')));
  const server_log_name = isPre ? 'ssr_pre_server_log' : 'ssr_server_log';
  try {
    const headerCookie = request.headers.get('cookie') || '';
    // 协商缓存
    if (request.headers.get('if-none-match')) {
      const etagReg = headerCookie.match(/_er_etag=(\S+);/) || headerCookie.match(/_er_etag=(\S+)$/);
      // 获取cookie中写入的etag值
      const etagCookie = etagReg && etagReg.length > 1 ? etagReg[1] : 'initial';
      const etagArray = request.headers.get('if-none-match').replace(`"`, '').replace(`W/`, '').split('-');
      if (etagArray.length > 1) {
        // 过期时间戳
        const etagTime = parseInt(etagArray[0]);
        const etagValue = etagArray[1];
        // 有效期内且etag值匹配正确走缓存
        if (etagTime >= Date.now() && etagCookie === etagValue) {
          return new Response('', {status: 304});
        }
      }
    }
    const { pathname, searchParams } = reqUrl;
    timeLog.url = reqUrl.toString();
    // 获取userid
    const matchesReg = headerCookie.match(/unb=(\d+);/);
    let headerCookieUserId = '';
    if (matchesReg && matchesReg.length > 1) {
      headerCookieUserId = (matchesReg[1]);
      if (headerCookieUserId === '*************') {
        // 安全扫描账号直接降级
        throw new Error('test account downgrade');
      }
      timeLog.userId = headerCookieUserId || '';
    }

    // 白名单匹配
    if (!RAX_CACHE_PAGE_LIST[pathname] || !searchParams.get(RAX_CACHE_PAGE_LIST[pathname].requiredKey) || searchParams.get('_force_online')) {
      // URL规则检测，异常url直接降级
      const groupName = pathname.match(/\/app\/trip\/(\S*)\/pages\//) && pathname.match(/\/app\/trip\/(\S*)\/pages\//)[1];
      const serviceName = pathname.match(/\/pages\/(\S*)/) && pathname.match(/\/pages\/(\S*)/)[1];
      const raxReg = /^[a-zA-Z0-9_-]+$/;
      const reactReg = /^[a-zA-Z0-9_\-/]+\.html$/;
      if (!groupName || !serviceName || groupName === 'null' || serviceName === 'null'
        || groupName === 'undefined' || serviceName === 'undefined' || !groupName.match(raxReg) ||
        (!serviceName.match(raxReg) && !serviceName.match(reactReg))) {
        const dOrigin = new URL(`https://${RAX_ORIGIN_HOST}`);
        dOrigin.pathname = reqUrl.pathname;
        dOrigin.search = reqUrl.search;
        dOrigin.searchParams.set('_er_failback', '1');
        checkRaxRedirectUrl(dOrigin, reqUrl.hostname);
        const dRedirectOriginUrl = dOrigin.href;
        try {
          event.waitUntil(ssrLog(request.headers, reqUrl, {
            logName: server_log_name,
            logType: 'server',
            logData: {
              ...timeLog,
              timeStamp: {
                ...timeLog.timeStamp,
                er_end: Date.now(),
              },
              er_duration: Date.now() - timeLog.timeStamp.er_start,
              erErrorMessage: 'pathError'
            }
          }));
        } catch {}
        return Response.redirect(dRedirectOriginUrl);
      }
      return await handleRaxSSR(request, event, timeLog);
    }

    // 读缓存
    timeLog.timeStamp.static_cache_start = Date.now();
    const cacheUrl = getPreloadCacheKey(request, reqUrl, RAX_CACHE_PAGE_LIST[pathname]);
    const cacheRes = cacheUrl ? await cache.get(cacheUrl): false;
    timeLog.timeStamp.static_cache_end = Date.now();
    timeLog.timeStamp.static_cache_duration = timeLog.timeStamp.static_cache_end - timeLog.timeStamp.static_cache_start;

    // 缓存存在直接返回结果，异步读kv处理过期判断和日志上报
    if (cacheRes) {
      const kvFunc = async (cacheRes, cacheUrl) => {
        let staticConfig = {};
        if (RAX_CACHE_PAGE_LIST[pathname].kvName) {
          const staticKvCache = await getKvCache('fliggyrax_124215', RAX_CACHE_PAGE_LIST[pathname].kvName);
          if (staticKvCache.data) {
            try {
              staticConfig = JSON.parse(staticKvCache.data);
            } catch (e) {
            }
            if (staticConfig[pathname]) {
              // 获取缓存的时间戳
              const cacheTime = parseInt(cacheRes.headers.get('x-cache-time'));
              // 获取更新时间
              const updateTime = staticConfig[pathname].updateTime;
              let targetUpdateTime;
              if (staticConfig[pathname].key && staticConfig[pathname].updateConfig) {
                const targetKey = searchParams.get(staticConfig[pathname].key);
                if (staticConfig[pathname].updateConfig[targetKey]) {
                  targetUpdateTime = staticConfig[pathname].updateConfig[targetKey]
                }
              }
              // 过期删除缓存
              if ((updateTime && cacheTime < updateTime) || (targetUpdateTime && cacheTime < targetUpdateTime)) {
                await cache.delete(cacheUrl);
              }
            } else {
              await cache.delete(cacheUrl);
            }
          } else {
            await cache.delete(cacheUrl);
          }
        }
      }
      cacheRes.headers.set('content-type', 'text/html; charset=utf-8');
      cacheRes.headers.set('server-timing', `t;desc="sc", time;desc=${Date.now() - timeLog.timeStamp.er_start}`)
      cacheRes.headers.set('x-er-hit-cache', 'true');
      removeErCacheHeaders(cacheRes.headers);
      removeHtmlCacheHeaders(cacheRes.headers);
      event.waitUntil(ssrLog(request.headers, reqUrl, {
        logName: server_log_name,
        logType: 'server',
        logData: {
          ...timeLog,
          timeStamp: {
            ...timeLog.timeStamp,
            er_end: Date.now()
          },
          er_duration: Date.now()- timeLog.timeStamp.er_start,
          hit_cache: '1'
        }
      }))
      event.waitUntil(kvFunc(cacheRes, cacheUrl));
      return cacheRes;
    } else {
      return await handleRaxSSR(request, event, timeLog, RAX_CACHE_PAGE_LIST[pathname]);
    }
  } catch (e) {
    try {
      event.waitUntil(ssrLog(request.headers, reqUrl, {
        logName: server_log_name,
        logType: 'server',
        logData: {
          ...timeLog,
          timeStamp: {
            ...timeLog.timeStamp,
            er_end: Date.now(),
          },
          er_duration: Date.now() - timeLog.timeStamp.er_start,
          erErrorMessage: `handleRaxSSRCacheError|${(e && e.message) || JSON.stringify(e)}`,
        }
      }));
    } catch {}

    const origin = new URL(`https://${RAX_ORIGIN_HOST}`);
    origin.pathname = reqUrl.pathname;
    origin.search = reqUrl.search;

    const queryStr = origin.href && origin.href.indexOf('?') > -1 ? '&_er_failback=1' : '?_er_failback=1';
    checkRaxRedirectUrl(origin, reqUrl.hostname);
    const redirectOriginUrl = origin.href + queryStr;

    return Response.redirect(redirectOriginUrl);
  }
}

async function handleRaxSSR(request, event, timeLog, kvConfig) {
  timeLog.timeStamp.func_start = Date.now();
  const _startTime = Date.now();
  const reqUrl = new URL(request.url);
  const isPre = !(reqUrl.hostname === 'outfliggys.m.taobao.com' ||
  (WX_HOST.includes(reqUrl.hostname) && !reqUrl.hostname.includes('pre-')));
  const server_log_name = isPre ? 'ssr_pre_server_log' : 'ssr_server_log';
  const origin = new URL(`https://${RAX_ORIGIN_HOST}`);
  const searchParams = reqUrl.searchParams;
  origin.pathname = reqUrl.pathname;

  let searchString = '?' + searchParams.toString();
  if (searchString === '?') {
    searchString = '';
  }

  // 微信套壳域名新增headers标识
  // if (WX_HOST.includes(reqUrl.hostname)) {
  //   request.headers.set('from-wx-er', 'true');
  // }

  // 处理手淘首猜链接带的utparam
  if (searchParams.has('utparam')) {
    searchParams.delete('utparam');
    // 删除相关字段后，重新赋值 searchString
    searchString = searchParams.toString().length ? '?' + searchParams.toString() : '';
    request.headers.set('ali-proxy-consistent-hash', `${reqUrl.pathname}${searchString}`)
    request.headers.set('ali-origin-real-url', `${reqUrl.origin}${reqUrl.pathname}${searchString}`)
  }

  // 透传给回源地址URL参数
  origin.search = searchString;
  checkRaxRedirectUrl(origin, reqUrl.hostname);

  // [debug] 请求头抓取
  if (reqUrl.pathname.startsWith('/app/inspect/')) {
    try {
      if (searchParams.get('code') === 'black_sheep_wall') {
        const header = {};
        for (let [_k, _v] of request.headers) {
          header[_k] = _v;
        }
        return JSON.stringify(header);
      }
    } catch (e) {
      return 'error debug';
    }
  }

  // [debug] For Test 302 测试
  if (searchParams.has('bypassRedirect')) {
    return Response.redirect(origin.toString());
  }
  // [debug] For Test 失败测试
  if (searchParams.has('bypassFail')) {
    throw new Error('bypass fail');
  }
  // [debug] For Test 获取headers测试
  if (searchParams.has('bypassGetHeaders')) {
    let headersData = {};
    for (let key of request.headers.keys()) {
      headersData[key] = request.headers.get(key);
    }

    return JSON.stringify(headersData);
  }

  // 不清楚这个逻辑，copy过来的
  request.headers.delete('host');
  request.headers.delete('x-client-scheme');

  timeLog.timeStamp.pre_cache_start = Date.now();
  // ssr预加载读缓存,25ms-75ms随机触发
  const preCacheUrl = searchParams.has('_fl_auto_preload_spm') || RAX_CACHE_BLACK_LIST[reqUrl.pathname] ? false : getPreloadCacheKey(request, reqUrl, kvConfig);
  const prePromise = preCacheUrl ? [new Promise(async(resolve, reject) => {
    const cRes = await cache.get(preCacheUrl);
    resolve(cRes);
  }), new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve(false);
    }, 50);
  })] : [];
  timeLog.preCacheUrl = preCacheUrl || '';
  const pressrCacheResponse = searchParams.has('_pressr') || !preCacheUrl || kvConfig ? false : await Promise.race(prePromise);
  timeLog.timeStamp.pre_cache_end = Date.now();
  timeLog.timeStamp.pre_cache_duration = timeLog.timeStamp.pre_cache_end - timeLog.timeStamp.pre_cache_start;
  if (pressrCacheResponse) {
    pressrCacheResponse.headers.set('content-type', 'text/html; charset=utf-8')
    pressrCacheResponse.headers.set('server-timing', `t;desc="sc", time;desc=${Date.now() - _startTime}`)
    pressrCacheResponse.headers.set('x-er-hit-cache', 'true');
    removeErCacheHeaders(pressrCacheResponse.headers);
    removeHtmlCacheHeaders(pressrCacheResponse.headers);
    timeLog.timeStamp.pre_delete_start = Date.now();
    if (!pressrCacheResponse.headers.get('x-er-cache-not-clear')) {
      await cache.delete(preCacheUrl);
    }
    timeLog.timeStamp.pre_delete_end = Date.now();
    timeLog.timeStamp.pre_delete_duration = timeLog.timeStamp.pre_delete_end - timeLog.timeStamp.pre_delete_start;
    event.waitUntil(ssrLog(request.headers, reqUrl, {
      logName: server_log_name,
      logType: 'server',
      logData: {
        ...timeLog,
        timeStamp: {
          ...timeLog.timeStamp,
          er_end: Date.now(),
        },
        er_duration: Date.now() - timeLog.timeStamp.er_start,
        hit_cache: '1'
      }
    }))
    return pressrCacheResponse;
  }

  // 开始获取 SSR 结果
  try {
    timeLog.timeStamp.main_start = Date.now();
    // 生成源站请求链接（兜底使用）
    const ssrOrigin = new URL(`https://${isPre && !searchParams.has('_faas_online') ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}`);
    ssrOrigin.pathname = reqUrl.pathname;
    ssrOrigin.search = searchString;

    // 增加请求头告之 SSR Faas 服务是从边缘节点来的
    request.headers.set('fli-bk', '1');

    // _force_cache 强制走缓存轮询
    // ---- 商详流式渲染逻辑 ----
    if (!searchParams.has('_pressr') && !searchParams.has('_force_cache') &&(streamRenderConfig[reqUrl.pathname] || searchParams.get('_enable_trip_stream'))) {
      // const result = await checkStreamRender(event, {
      //   isPre,
      //   reqUrl,
      //   ssrUrl: ssrOrigin,
      //   raxUrlString: origin.toString(),
      //   request,
      //   startTime: timeLog.timeStamp.er_start
      // });
      // if (result !== false) {
      //   return result;
      // }

    }

    let ssrResponse;
    let toHandler;

    let reqPath = ssrOrigin.toString();
    // 路径映射，行业函数分发，25ms-75ms随机触发
    timeLog.timeStamp.path_kv_start = Date.now();
    const pathPromise = [new Promise(async(resolve, reject) => {
      const cRes = await getKvCache('fliggyrax_124215', 'path-map');
      resolve(cRes);
    }), new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({});
      }, 50);
    })];
    const pathKvCache = await Promise.race(pathPromise);
    timeLog.timeStamp.path_kv_end = Date.now();
    timeLog.timeStamp.path_kv_duration = timeLog.timeStamp.path_kv_end - timeLog.timeStamp.path_kv_start;
    let pathRes = {};
    if (pathKvCache.data) {
      try {
        pathRes = JSON.parse(pathKvCache.data);
       } catch (e) {
        pathRes = {};
      }
    }

    if (reqUrl.pathname === '/app/trip/rx-mileage-center/pages/home' && timeLog.userId && TAOBAO_PHA_WHITE_LIST.indexOf(timeLog.userId) >= 0) {
      // TODO 手淘测试
      reqPath = reqPath.replace(reqUrl.pathname, "/app/trip/rx-mileage-center/pages/detail")
    } else if (searchString.includes('_isVMMode')) {
      // vm模式
      reqPath = reqPath.replace(reqUrl.pathname, `/vm${reqUrl.pathname}`)
    } else if (pathRes[reqUrl.pathname]) {
      reqPath = reqPath.replace(reqUrl.pathname, pathRes[reqUrl.pathname])
    }

    timeLog.timeStamp.path_parse = Date.now();

    // [debug]通用流式SSR逻辑
    const secondPartSsrUrl = reqPath.indexOf('?') > -1 ? `${reqPath}&_only_show_second_part=1` : `${reqPath}?_only_show_second_part=1`;
    if (!searchParams.has('_pressr') && !searchParams.has('_force_cache') && searchParams.has('_use_stream')) {
      const result = await useStreamRender(event, {
        reqUrl,
        ssrUrl: reqPath,
        secondPartSsrUrl,
        raxUrl: origin.toString(),
        request,
        _startTime,
        isPre,
        timeLog
      });
      if (result !== false) {
        return result;
      }
    }

    // 5秒超时
    let promiseList = [
      new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve('timeout');
        }, isPre ? 10000 : 5000);
      })
    ];
    const seoPathList = [
      '/app/trip/h5-lazada-weather-seo/pages/home/<USER>',
      '/app/trip/h5-lazada-weather-seo/pages/detail/index.html',
      '/app/trip/h5-lazada-weather-seo/pages/notfound/index.html',
      '/app/trip/rx-weather-seo-mobile/pages/home',
      '/app/trip/rx-weather-seo-mobile/pages/detail',
      '/app/trip/rx-weather-seo-mobile/pages/notfound',
      '/app/trip/rx-weather-seo-mobile/pages/sitemap',
      '/app/trip/h5-lazada-pc-fliggy-home-ssr/pages/home/<USER>',
      '/app/trip/h5-lazada-pc-fliggy-home-ssr/pages/sitemap/index.html',
      '/app/trip/h5-lazada-pc-fliggy-home-ssr/pages/test/index.html'
    ];
    if (searchParams.has('_css_inline') || searchParams.has('_pressr') || seoPathList.includes(reqUrl.pathname) || kvConfig) {
      promiseList.push(fetch(reqPath, {
        headers: request.headers
      }))
    } else {
      promiseList.push(fetch(reqPath, {
        headers: request.headers,
        decompress: 'manual'
      }))
    }

    timeLog.timeStamp.faas_req = Date.now();
    ssrResponse = await Promise.race(promiseList);
    timeLog.timeStamp.faas_res = Date.now();
    timeLog.faas_duration = timeLog.timeStamp.faas_res - timeLog.timeStamp.faas_req;
    if (typeof ssrResponse === 'string') {
      throw new Error(ssrResponse);
    }
    if (ssrResponse.headers.get('X-Ssr-Start') && ssrResponse.headers.get('X-Ssr-End')) {
      timeLog.timeStamp.ssr_start = parseInt(ssrResponse.headers.get('X-Ssr-Start'));
      timeLog.timeStamp.ssr_end = parseInt(ssrResponse.headers.get('X-Ssr-End'));
      timeLog.ssr_duration = timeLog.timeStamp.ssr_end - timeLog.timeStamp.ssr_start;
      timeLog.faas_req_duration = timeLog.timeStamp.ssr_start - timeLog.timeStamp.faas_req;
      timeLog.faas_res_duration = timeLog.timeStamp.faas_res - timeLog.timeStamp.ssr_end;
    }

    // 命中缓存即返回
    if (ssrResponse.headers.get('x-er-hit-cache-interval')) {
      event.waitUntil(ssrLog(request.headers, reqUrl, {
        logName: server_log_name,
        logType: 'server',
        logData: {
          ...timeLog,
          timeStamp: {
            ...timeLog.timeStamp,
            er_end: Date.now()
          },
          er_duration: Date.now()- timeLog.timeStamp.er_start,
          hit_cache: '1'
        }
      }))
      return ssrResponse;
    }

    if (toHandler) {
      clearTimeout(toHandler);
      toHandler = null;
    }

    if (ssrResponse && ssrResponse.status === 302) {
      return ssrResponse;
    }

    if (!ssrResponse || (ssrResponse && !ssrResponse.ok)) {
      let errorMessage = 'ssrResponse error';
      try {
        errorMessage = await ssrResponse.text();
      } catch (err) {
        errorMessage = (err && err.message) || JSON.stringify(err);
      }
      throw new Error(errorMessage);
    }

    // css内联
    if (searchParams.has('_css_inline')) {
      try {
        // 获取html文档内容
        const htmlText = await (ssrResponse.clone()).text();
        // 获取combo css
        const comboStr = htmlText.match(/\<link tag=.*?>/g);
        if (comboStr && comboStr.length) {
          const cssStrReg = comboStr[0].match(/\s*href=['"](.*?)['"]\s*/)
          if (cssStrReg && cssStrReg.length > 1) {
            let cssUrl = cssStrReg[1];
            // 拼接css完整链接
            if (cssUrl.indexOf('https') < 0) {
              cssUrl = `https:${cssUrl}`;
            }
            let cssText = '';
            // 走实时链路避免出错
            const cssRes = await fetch(cssUrl);
            cssText = await cssRes.text();
            // css替换
            const resHTML = htmlText.replace(comboStr[0], `<style>${cssText}</style><script>window._er_fliggy_ssr_inline=true;window._er_ssr_inline_url="${cssUrl}"</script>`);
            ssrResponse = new Response(resHTML, {
              headers: ssrResponse.headers,
              status: 200
            })
          }
        }
      } catch (e) {
      }
    }

    // 针对首页seo做字符串替换
    try {
      if (reqUrl.pathname === '/app/trip/h5-lazada-weather-seo/pages/detail/index.html' || reqUrl.pathname === '/app/trip/rx-weather-seo-mobile/pages/detail') {
        // 获取html文档内容
        const htmlText = await (ssrResponse.clone()).text();
        // 获取变量
        // 城市POI辖区县全名
        const totalNameStrArr = htmlText.match(/_er_replace_start_\{\{(.+?)\}\}_er_replace_end_/);
        const totalName = totalNameStrArr && totalNameStrArr.length > 1 && totalNameStrArr[1] || '';
        // 特殊title
        const sepcialTitleStrArr = htmlText.match(/_er_special_title_start_\{\{(.+?)\}\}_er_special_title_end_/);
        const specialTitle = sepcialTitleStrArr && sepcialTitleStrArr.length > 1 && sepcialTitleStrArr[1] || '';
        // 特殊desc
        const sepcialDescStrArr = htmlText.match(/_er_special_desc_start_\{\{(.+?)\}\}_er_special_desc_end_/);
        const specialDesc = sepcialDescStrArr && sepcialDescStrArr.length > 1 && sepcialDescStrArr[1] || '';
        // 省份(放location区块里的)
        const provinceStrArr = htmlText.match(/_er_replace_province_start_\{\{(.+?)\}\}_er_replace_province_end_/);
        const provinceToDisplay = provinceStrArr && provinceStrArr.length > 1 && provinceStrArr[1] || '';
        // 城市(放location区块里的)
        const cityLocationStrArr = htmlText.match(/_er_replace_city_start_\{\{(.+?)\}\}_er_replace_city_end_/);
        const cityLocationToDisplay = cityLocationStrArr && cityLocationStrArr.length > 1 && cityLocationStrArr[1] || '';
        // 经度(放location区块里的)
        const longitudeStrArr = htmlText.match(/_er_replace_longitude_start_\{\{(.+?)\}\}_er_replace_longitude_end_/);
        const longitudeToDisplay = longitudeStrArr && longitudeStrArr.length > 1 && longitudeStrArr[1] || '';
        // 经度(放location区块里的)
        const latitudeStrArr = htmlText.match(/_er_replace_latitude_start_\{\{(.+?)\}\}_er_replace_latitude_end_/);
        const latitudeToDisplay = latitudeStrArr && latitudeStrArr.length > 1 && latitudeStrArr[1] || '';
        // picture区块
        const pictureStrArr = htmlText.match(/_er_replace_picture_start_\{\{(.+?)\}\}_er_replace_picture_end_/);
        const pictureToDisplay = pictureStrArr && pictureStrArr.length > 1 && pictureStrArr[1] || '';
        // canonical url区块
        const canonicalUrlStrArr = htmlText.match(/_er_replace_canonical_url_start_\{\{(.+?)\}\}_er_replace_canonical_url_end_/);
        const canonicalUrl = canonicalUrlStrArr && canonicalUrlStrArr.length > 1 && canonicalUrlStrArr[1] || '';
        // h5 url区块
        const h5UrlStrArr = htmlText.match(/_er_replace_h5_url_start_\{\{(.+?)\}\}_er_replace_h5_url_end_/);
        const h5Url = h5UrlStrArr && h5UrlStrArr.length > 1 && h5UrlStrArr[1] || '';

        // 字符串替换
        const resHTML = htmlText.replace(/_city_or_spot_name_/g, totalName)
        .replace(/_special_title_/g, specialTitle)
        .replace(/_special_desc_/g, specialDesc)
        .replace(/_location_province_/g, provinceToDisplay)
        .replace(/_location_city_/g, cityLocationToDisplay)
        .replace(/_location_longitude_/g, longitudeToDisplay)
        .replace(/_location_latitude_/g, latitudeToDisplay)
        .replace(/_picture_in_meta_/g, pictureToDisplay)
        .replace(/https:\/\/www\.canonicalplaceholder\.html/g, canonicalUrl)
        .replace(/https:\/\/www\.h5urlplaceholder\.html/g, h5Url);
        // 重写res
        ssrResponse = new Response(resHTML, {
          headers: ssrResponse.headers,
          status: 200
        });
      }
    } catch {}


    // 移除缓存相关的 header，禁止客户端缓存（否则首屏数据也无法更新）
    removeHtmlCacheHeaders(ssrResponse.headers)

    // 更新缓存
    if (ssrResponse.headers.get('fli-type') === 'to') {
      ssrResponse.headers.set('server-timing', `t;desc="lc", time;desc=${Date.now() - _startTime}`)
    } else {
      ssrResponse.headers.set('server-timing', `t;desc="bk", time;desc=${Date.now() - _startTime}`)
    }
    // ssr预加载
    if ((searchParams.has('_pressr') || kvConfig) && preCacheUrl) {
      try {
        const cacheFunc = async (ssrResponse, searchParams, kvConfig) => {
          // 获取html文档内容
          const htmlText = await (ssrResponse.clone()).text();
          // 打点
          const resHTML = searchParams.has('_pressr') ? htmlText.replace('</head>', '<script>window._er_cache=true;try{if(!window.location.href.includes("_er_cache")){history.replaceState(null, "", window.location.href + (window.location.href.includes("?") ? "&_er_cache=true" : "?_er_cache=true"))}}catch(e){}</script></head>') :
            htmlText.replace('</head>', '<script>window._er_static=true;try{if(!window.location.href.includes("_er_static")){history.replaceState(null, "", window.location.href + (window.location.href.includes("?") ? "&_er_static=true" : "?_er_static=true"))}}catch(e){}</script></head>');
          ssrResponse = new Response(resHTML, {
            headers: ssrResponse.headers,
            status: 200
          })
          // 获取缓存key
          const pressrCache = ssrResponse.clone();
          const cacheSeconds = kvConfig ? `${kvConfig.cacheDuration * 60}` : searchParams.get('_preMaxAge') || '120';
          pressrCache.headers.set('cache-control', `max-age=${cacheSeconds}`);
          pressrCache.headers.set('x-cache-time', `${Date.now()}`);
          if (searchParams.has('_preNotClear')) {
            pressrCache.headers.set('x-er-cache-not-clear', 'true');
          }
          await cache.put(preCacheUrl, pressrCache);
        }
        event.waitUntil(cacheFunc(ssrResponse, searchParams, kvConfig));
      } catch (e) {
      }
    }
    ssrResponse.headers.set('x-er-hit-cache', 'false');
    event.waitUntil(ssrLog(request.headers, reqUrl, {
      logName: server_log_name,
      logType: 'server',
      logData: {
        ...timeLog,
        timeStamp: {
          ...timeLog.timeStamp,
          er_end: Date.now()
        },
        er_duration: Date.now()- timeLog.timeStamp.er_start
      }
    }));
    // 协商缓存
    if (searchParams.has('_http_etag')) {
      const headerCookie = request.headers.get('cookie') || '';
      const etagReg = headerCookie.match(/_er_etag=(\S+);/) || headerCookie.match(/_er_etag=(\S+)$/);
      const etagValue = etagReg && etagReg.length > 1 ? etagReg[1] : 'initial';
      const etagTime = Date.now() + parseInt(searchParams.get('_http_etag')) * 1000;
      // etag规范：过期时间戳-etag标识，加单个双引号用于弱匹配校验，详见https://aone.alibaba-inc.com/v2/project/1109730/req/55582160
      ssrResponse.headers.set('etag', `"${etagTime}-${etagValue}`);
      // 手淘PHA文档预加载协商缓存无效，通过写cookie形式实现
      ssrResponse.headers.set('set-cookie', `_if_none_match=${etagTime}-${etagValue}; Max-Age=${parseInt(searchParams.get('_http_etag'))}`);
    }
    // 强缓存
    if (timeLog.userId && TAOBAO_PHA_WHITE_LIST.indexOf(timeLog.userId) >= 0) {
      // 手淘测试场景，全量开启
      ssrResponse.headers.set('cache-control', `max-age=10800`);
    } else if (searchParams.has('_http_cache')) {
      ssrResponse.headers.set('cache-control', `max-age=${searchParams.get('_http_cache')}`)
    }
    return ssrResponse;
  } catch (err) {
    // return err.message;

    try {
      event.waitUntil(ssrLog(request.headers, reqUrl, {
        logName: server_log_name,
        logType: 'server',
        logData: {
          ...timeLog,
          timeStamp: {
            ...timeLog.timeStamp,
            er_end: Date.now(),
          },
          er_duration: Date.now() - timeLog.timeStamp.er_start,
          erErrorMessage: (err && err.message) || JSON.stringify(err),
        }
      }));
    } catch {}

    origin.searchParams.set('_er_failback', '1');
    origin.searchParams.set('ssr_fail_back', '1');
    const redirectOriginUrl = origin.href;
    return Response.redirect(redirectOriginUrl);
  }
}

// 获取ssr后台的流式配置
async function getMiniworkErConfig(projectName, pageName) {
  if (!projectName || !pageName) return '';

  const miniworkUrl = `https://fl-miniwork.fc.alibaba-inc.com/api/ssr-detail/ssr-list?current=1&pageSize=20&projectName=${projectName}&tabType=all`;
  const miniworkData = await fetch(miniworkUrl).then(res => res.json()).catch(() => { return {}; });
  const ssrPageList = miniworkData && miniworkData.data && miniworkData.data.data || [];

  let streamConfig = '';
  ssrPageList.forEach(item => {
    const childrenList = item.children || [];
    childrenList.forEach(cItem => {
      const path_project_name = cItem.path_project_name || cItem.project_name || '';
      const path_page_name = cItem.path_page_name || cItem.page_name || '';
      if (path_project_name === projectName && path_page_name === pageName) {
        streamConfig = cItem.pre_stream_config || '';
      }
    });
  });

  return streamConfig;
}

// [debug]sls日志告警
function sendSlsLog(err, reqUrl, timeLog = {}) {
  let groupName = '';
  let serviceName = '';
  let newTimeLog = {};
  try {
    // "/app/trip/rx-fliggy-sqk/pages/home" --> "rx-fliggy-sqk_home"
    const reqPathArr = reqUrl.pathname.split('/');
    groupName = reqPathArr[3];
    serviceName = reqPathArr.slice(5).join('_');

    Object.keys(timeLog).forEach(item => {
      newTimeLog[item] = JSON.stringify(timeLog[item]);
    });
  } catch { }

  const data = {
    groupName,
    serviceName,
    logData: {
      slsNew: true,
      reqUrl: reqUrl.toString(),
      errorMsg: (err && err.message) || JSON.stringify(err),
      ...newTimeLog,
    },
  };

  fetch(SLS_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({param: data}),
    mode: 'cors',
  });
}

// [debug]获取SSR文档
async function getSsrHtml(ssrUrl, request, timeLog, logPrefix = '', isPre) {
  try {
    timeLog.timeStamp[`${logPrefix}faas_req`] = Date.now();
    const res = await Promise.race([
      fetch(ssrUrl, {
        headers: request.headers,
      }),
      new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve('get faas request is timeout.Timeout values is 4000');
        }, isPre ? 10000 : 5000);
      })
    ]);
    if (res.headers && res.headers.get('X-Ssr-Start') && res.headers.get('X-Ssr-End')) {
      timeLog.timeStamp[`${logPrefix}faas_res`] = Date.now();
      timeLog[`${logPrefix}faas_duration`] = timeLog.timeStamp[`${logPrefix}faas_res`] - timeLog.timeStamp[`${logPrefix}faas_req`];
      timeLog.timeStamp[`${logPrefix}ssr_start`]  = parseInt(res.headers.get('X-Ssr-Start'));
      timeLog.timeStamp[`${logPrefix}ssr_end`]  = parseInt(res.headers.get('X-Ssr-End'));
      timeLog[`${logPrefix}ssr_duration`] = timeLog.timeStamp[`${logPrefix}ssr_end`] - timeLog.timeStamp[`${logPrefix}ssr_start`];
      timeLog[`${logPrefix}faas_req_duration`]  = timeLog.timeStamp[`${logPrefix}ssr_start`] - timeLog.timeStamp[`${logPrefix}faas_req`];
      timeLog[`${logPrefix}faas_res_duration`]  = timeLog.timeStamp[`${logPrefix}faas_res`] - timeLog.timeStamp[`${logPrefix}ssr_end`];
    }

    if (typeof res === 'string') {
      return res;
    }

    timeLog.timeStamp[`html_stringify_start`] = Date.now();
    const ssrHtml = await res.text();
    timeLog.timeStamp[`html_stringify_end`] = Date.now();

    return ssrHtml
  } catch (err) {
    return (err && err.message) || JSON.stringify(err);
  }
  // const ssrHtml = await fetch(ssrUrl, {
  //   headers: request.headers,
  // }).then(res => res.text()).catch(() => { return ''; });
  // return ssrHtml;
}

// [debug]拼接一屏骨架图所需的基础html，添加对css和js的preload
function getFirstPartHtml(ssrHtml) {
  const [holderTag] = ssrHtml.match(HOLDER_START_REG) || [];
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || [];
  if (!rootTag) {
    return '';
  }
  const holderStartPosition = holderTag ? ssrHtml.indexOf(holderTag) : -1;
  const contentStartPosition = rootTag ? ssrHtml.indexOf(rootTag) : -1;
  if (contentStartPosition === -1) {
    return '';
  }

  // 如果存在骨架图，先不拼接骨架图的html，后续会单独拼接。
  const position = holderStartPosition > -1 ? holderStartPosition : contentStartPosition;
  const topContent = ssrHtml.slice(0, position);
  const bottomContent = ssrHtml.slice(position);

  const tags = [...bottomContent.matchAll(/<(script|link).+?(src|href)='((?:https:)?\/\/(dev\.|)g\.alicdn\.com[^']+)'/g)];
  const preloadTags = tags.reduce((acc, cur) => {
    if (cur && cur[1] && cur[3]) {
      if (cur[1] === 'script') {
        acc += `<link tag="combojs" rel="preload" href="${cur[3]}" as="script" crossorigin="anonymous" />`;
      } else if (cur[1] === 'link') {
        acc += `<link tag="combocss" rel="preload" href="${cur[3]}" as="style" crossorigin="anonymous" />`;
      }
    }
    return acc;
  }, '');

  if (preloadTags) {
    const firstStylePosition = topContent.indexOf('<style');
    return [
      topContent.slice(0, firstStylePosition),
      preloadTags,
      topContent.slice(firstStylePosition)
    ].join('');
  }
  return topContent;
}

// [debug]设置首屏缓存
async function setFristPartCache({ ssrHtml, pageErConfig = {}, firstPartCacheKey, isPre = false }) {
  // 获取ssr文档中，骨架图数据
  let holderData = getMetaData(ssrHtml, 'er-cache-data');
  const ssrVersion = getMetaData(ssrHtml, 'alitrip-project-version');

  // 如果ssr版本 和 er后台配置的版本 不一致，则不缓存
  if (ssrVersion !== pageErConfig.version) {
    return false;
  }

  try {
    holderData = decodeURIComponent(decodeURIComponent(holderData));
    holderData = JSON.parse(holderData);
  } catch (err) {
    holderData = {};
  }

  // 骨架图HTML
  let holderTemp = '';
  try {
    holderTemp = decodeURIComponent(pageErConfig.template);
  } catch (err) {
    holderTemp = '';
  }

  // er配置解析失败
  if (!holderTemp) {
    return false;
  }

  /**
   * iOS 上 HTTP 已经返回第一个 chunk，但是流式渲染不生效（不上屏）。
   * 这是ios浏览器特性，在需要上屏的 HTML 片段中增加真实的文本节点 + 图片节点，
   * 文本样式设为 font-size: 0，不能 display: none。图片节点设置opacity: 0，可以触发浏览器立即执行渲染策略
   * 所以需要在骨架图上拼接一个图片和文字
   * */
  const str = `<div id="ssr-er-additional-holder"><div style="font-size:0;">%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D</div><img src="https://gw.alicdn.com/imgextra/i1/O1CN01MwAFGF1wu8c1qURA5_!!6000000006367-2-tps-42-42.png" style="opacity:0;width:100vw;position:absolute;" /></div>`;
  holderTemp += str;

  // 拼接数据和骨架图
  const holderHtmlData = parseTemplate(holderTemp, holderData);

  // 拼接 <head> + 骨架图
  const holder = getFirstPartHtml(ssrHtml) + holderHtmlData.data;

  // 存入缓存（暂定3天）
  await setJsonCache(firstPartCacheKey, {
    version: pageErConfig.version,
    updateTime: Date.now(),
    holder: encodeURIComponent(holder),
  }, 3 * 24 * 60 * 60, isPre);

  return holder;
}

// [debug]获取第一屏数据缓存Key
function getfirstPartCacheKey(reqUrl, keys = [], isGray = false) {
  for (let i = 0; i < keys.length; i++) {
    const data = reqUrl.searchParams.get(keys[i]);
    if (data) {
      return `${reqUrl.pathname}?key=${keys[i]}_${data}${isGray ? '&is_gray=1' : ''}`;
    }
  }

  return `${reqUrl.pathname}${isGray ? '?is_gray=1' : ''}`;
}

// [debug]获取debug数据
async function getDebugInfo(data) {
  const {
    ssrHtml,
    firstPartCacheHolder,
    erConfigTemplate,
  } = data;

  return `
  <script id="debug">
    window._er_debug_message = {
      ssrHtml: decodeURIComponent("${encodeURIComponent(ssrHtml || '')}"),
      firstPartCacheHolder: decodeURIComponent("${encodeURIComponent(firstPartCacheHolder || '')}"),
      erConfigTemplate: decodeURIComponent("${erConfigTemplate || ''}"),
    }
  </script>
  `;
}

// [debug]返回一屏的骨架图
async function sendFirstPartHtml(firstPartHtml, raxUrl, _startTime, titleBarOpt = {}, writeHtml, timeLog = {}, isDebug = false, isPre) {
  const newUrl = new URL(raxUrl);
  newUrl.searchParams.set('_er_failback', 'timeout');

  let newFirstPartHtml = firstPartHtml;
  // 如果有沉浸式参数 且 当前端支持沉浸式，则替换「标识」为真正的高度
  if (titleBarOpt.hasImmersiveQuery && titleBarOpt.canUseImmersive) {
    newFirstPartHtml = firstPartHtml
      .replace('_cookie_status_bar_height_er_', `${titleBarOpt['statusBarHeight'] || 0}px`)
      .replace('_cookie_total_bar_height_er_', `${titleBarOpt['totalBarHeight'] || 44}px`);
  }

  const list = [
    newFirstPartHtml,
    `<script data-id="first-part-success">
      window._first_part_show_time = Date.now();
      if(window.performance && typeof window.performance.mark == 'function'){
        window.performance.mark('stage-first-chunk')
      }
      if (window.performance && window.performance.timing && window.performance.timing.navigationStart) {
        window._first_part_time = window._first_part_show_time - window.performance.timing.navigationStart;
      }
      setTimeout(function(){
        if (!window._er_next_part_render && ${isDebug ? 'false' : 'true'}) {
          location.replace("${newUrl.toString()}")
        }
      }, ${isPre ? 10000 : 5000});
    </script>`,
    '\n',
  ];
  await writeHtml(list.join(''));
  timeLog.timeStamp[`first_part_end`] = Date.now();
}

// [debug]获取第二屏HTML文档
async function getSecondPartHtml(data) {
  const {
    ssrHtml = '',
  } = data;
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || []; // '<div id="root"';
  const [secondPartEndTag] = ssrHtml.match(SECOND_STREAM_END) || []; // '<div id="__SECOND_STREAM_END__"></div>'

  if (rootTag && secondPartEndTag) {
    const startPosition = ssrHtml.indexOf(rootTag);
    const endPosition = ssrHtml.indexOf(secondPartEndTag);
    let html = ssrHtml.slice(startPosition, endPosition);
    html += html ?
      `<script data-id="second-part-success">
        window._er_second_part = Date.now();
        var firstErHolder = document.getElementById('ssr-er-holder');
        var erAddHolder = document.getElementById('ssr-er-additional-holder');
        if (firstErHolder) {
          firstErHolder.style.display = "none";
        }
        if (erAddHolder) {
          erAddHolder.style.display = "none";
        }
      </script>` : '';

    return html;
  }

  return '';
}

// [debug]获取最后一屏HTML文档
async function getLastPartHtml(data) {
  const {
    ssrHtml = '',
    firstPartCacheHolder = '',
    erConfigTemplate = '',
    isDebug,
    useThreePartStream,
    isTest,
    timeLog,
  } = data;

  const [secondPartEndTag] = ssrHtml.match(SECOND_STREAM_END) || []; // '<div id="__SECOND_STREAM_END__"></div>'
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || []; // '<div id="root"';
  const [serverTag] = ssrHtml.match(SERVER_START_REG) || []; // <script data-from="server"

  // 如果是三段流式，则从【<div id="__SECOND_STREAM_END__"></div>】开始分割
  // 如果是二段流式，则从【<div id="root"】开始分割
  const startTag = useThreePartStream ? secondPartEndTag : rootTag;

  if (startTag && serverTag) {
    const startPosition = ssrHtml.indexOf(startTag);
    const serverPosition = ssrHtml.indexOf(serverTag);
    const list = [
      ssrHtml.slice(startPosition, serverPosition),
      `<script data-id="last-part-success">
        window._er_next_part_render = true;
        window._er_last_part = Date.now();
        var lastErHolder = document.getElementById('${useThreePartStream ? '__SECOND_STREAM_HOLDER__' : 'ssr-er-holder'}');
        var erAddHolder = document.getElementById('ssr-er-additional-holder');
        if (lastErHolder) {
          lastErHolder.remove();
        }
        if (erAddHolder) {
          erAddHolder.remove();
        }
      </script>`,
      `<script>
        if(window.performance && typeof window.performance.mark == 'function'){
          window.performance.mark('stage-second-chunk')
        }
        console.log('SSR 函数端到端耗时：${timeLog && timeLog.faas_duration || 'null'}');
        console.log('SSR 函数执行耗时：${timeLog && timeLog.ssr_duration || 'null'}');
      </script>`,
    ];
    if (isDebug) {
      const debugInfo = await getDebugInfo({
        ssrHtml,
        firstPartCacheHolder,
        erConfigTemplate,
      });
      list.push(debugInfo);
    }
    list.push(ssrHtml.slice(serverPosition));

    return list.join('');
  }

  throw new Error('getLastPartHtml error');
}

// PHA首页定制测试，获取最后一屏HTML文档
async function getLastPartHtmlForHome(data) {
  const {
    ssrHtml = '',
    firstPartCacheHolder = '',
    erConfigTemplate = '',
    isDebug,
    useThreePartStream,
    isTest,
    timeLog,
  } = data;

  const [secondPartEndTag] = ssrHtml.match(SECOND_STREAM_END) || []; // '<div id="__SECOND_STREAM_END__"></div>'
  const [rootTag] = ssrHtml.match(ROOT_BEFORE_START_REG) || ssrHtml.match(ROOT_START_REG) || []; // '<div id="root"';
  const [serverTag] = ssrHtml.match(SERVER_START_REG) || []; // <script data-from="server"

  // 如果是三段流式，则从【<div id="__SECOND_STREAM_END__"></div>】开始分割
  // 如果是二段流式，则从【<div id="root"】开始分割
  const startTag = useThreePartStream ? secondPartEndTag : rootTag;

  if (startTag && serverTag) {
    const startPosition = ssrHtml.indexOf(startTag);
    const serverPosition = ssrHtml.indexOf(serverTag);
    const list = [
      ssrHtml.slice(startPosition, serverPosition),
      `<script data-id="last-part-success">
        window._er_next_part_render = true;
        window._er_last_part = Date.now();
        var lastErHolder = document.getElementById('${useThreePartStream ? '__SECOND_STREAM_HOLDER__' : 'ssr-er-holder'}');
        var erAddHolder = document.getElementById('ssr-er-additional-holder');
        if (lastErHolder) {
          ${
            isTest
            ? 'lastErHolder.remove();'
            : 'lastErHolder.style.display = "none";'
          }
        }
        if (erAddHolder) {
          erAddHolder.style.display = "none";
        }
      </script>`,
      `<script>
        if(window.performance && typeof window.performance.mark == 'function'){
          window.performance.mark('stage-second-chunk')
        }
        console.log('SSR 函数端到端耗时：${timeLog && timeLog.faas_duration || 'null'}');
        console.log('SSR 函数执行耗时：${timeLog && timeLog.ssr_duration || 'null'}');
      </script>`
    ];

    return {
      firstPart: list.join(''),
      secondPart: ssrHtml.slice(serverPosition)
    }
  }

  throw new Error('getLastPartHtml error');
}

// [debug]渲染流式页面
async function writeStream(opt) {
  const {
    writer = {},
    pageErConfig = {},
    titleBarOpt = {},
    reqUrl,
    ssrUrl = '',
    secondPartSsrUrl = '',
    raxUrl = '',
    request,
    _startTime,
    isPre,
    isGray,
    isDebug,
    isTest,
    isHomeTest,
    disableCache,
    onlyShowFirstPart,
    onlyShowSecondPart,
    useThreePartStream,
    timeLog,
    event
  } = opt;
  timeLog.timeStamp[`er_cache_start`] = Date.now();

  const encoder = new TextEncoder();
  const writeHtml = (e) => writer.write(encoder.encode(e));

  // er配置的缓存更新时间
  const erConfigUpdateTime = pageErConfig.updateTime || 0;

  // 获取第一屏数据缓存
  const firstPartCacheKey = getfirstPartCacheKey(reqUrl, pageErConfig.cacheKey, isGray);
  // 缓存版本、缓存更新时间、缓存内容
  let firstPartCacheVersion = '';
  let firstPartCacheUpdateTime = 0;
  let firstPartCacheHolder = '';
  try {
    // 获取第一屏缓存，50ms超时
    const firstPromise = [new Promise(async(resolve, reject) => {
      const cRes = await getJsonCache(firstPartCacheKey, isPre);
      resolve(cRes);
    }), new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({});
      }, 50);
    })];
    let firstPartCacheData = await Promise.race(firstPromise);
    firstPartCacheData = firstPartCacheData.data;
    firstPartCacheVersion = firstPartCacheData.version;
    firstPartCacheUpdateTime = firstPartCacheData.updateTime || 0;
    firstPartCacheHolder = decodeURIComponent(firstPartCacheData.holder);
  } catch (e) { }
  timeLog.timeStamp[`er_cache_end`] = Date.now();

  try {
    // 如果 使用缓存 且 成功获取第一屏数据缓存 且 版本号一致 且 缓存更新时间大于er配置的时间，则返回首屏数据
    if (!disableCache &&
      firstPartCacheHolder &&
      (firstPartCacheVersion === pageErConfig.version) &&
      firstPartCacheUpdateTime > erConfigUpdateTime
    ) {
      // 如果是三段流式，则先异步请求完整的html文档
      let getCompleteSsrHtml = () => { };
      if (useThreePartStream) {
        getCompleteSsrHtml = getSsrHtml(ssrUrl, request, timeLog, 'three_', isPre);
      }

      // 1. 写一屏骨架 + 获取ssr文档
      timeLog.timeStamp[`first_part_start`] = Date.now();
      const [writeReason, ssrHtml] = await Promise.all([
        sendFirstPartHtml(firstPartCacheHolder, raxUrl, _startTime, titleBarOpt, writeHtml, timeLog, isDebug, isPre),
        getSsrHtml(
          useThreePartStream ? secondPartSsrUrl : ssrUrl,
          request,
          timeLog,
          '',
          isPre
        ),
      ]);

      timeLog.timeStamp[`other_part_start`] = Date.now();

      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = ssrHtml && ssrHtml.indexOf('<body') > -1;
      if (!ssrHtmlValid) {
        throw new Error(`ssrHtml invalid(use cache)|${ssrHtml}`);
      }

      // 只返回第一屏数据，debug使用
      if (onlyShowFirstPart) {
        await writeHtml('</body></html>');
        await writer.close();
        return;
      }

      // 2. 如果是三段流式，则返回二屏数据
      if (useThreePartStream) {
        const secondPartHtml = await getSecondPartHtml({
          ssrHtml,
        });

        await writeHtml(secondPartHtml);

        // 只返回第一屏 + 第二屏数据，debug使用
        if (onlyShowSecondPart) {
          await writer.close();
          return;
        }
      }

      // 如果是三段流式，则最多延迟5s，等待完整的ssr文档返回
      let completeSsrHtml = '';
      if (useThreePartStream) {
        completeSsrHtml = await Promise.race([
          getCompleteSsrHtml,
          new Promise((resolve, reject) => {
            setTimeout(() => {
              resolve('getCompleteSsrHtml timeout 5000');
            }, isPre ? 10000 : 5000);
          })
        ]);

        // 如果完整的ssr文档为空，则抛出错误，强制回源
        const completeSsrHtmlValid = completeSsrHtml && completeSsrHtml.indexOf('<body') > -1;
        if (!completeSsrHtmlValid) {
          throw new Error(`completeSsrHtml invalid(use cache)|${completeSsrHtml}`);
        }
      }

      // TODO首页定制
      if (ssrUrl.includes('/rx-miniapp-home/pages/home') && isHomeTest) {
        // 3. 返回最后一屏数据
        const lastPartHtml = await getLastPartHtmlForHome({
          ssrHtml: useThreePartStream ? completeSsrHtml : ssrHtml,
          firstPartCacheHolder,
          erConfigTemplate: pageErConfig.template,
          isDebug,
          useThreePartStream,
          isTest,
          timeLog,
        });

        await writeHtml(lastPartHtml.firstPart);
        await writeHtml(lastPartHtml.secondPart);
      } else {
        // 3. 返回最后一屏数据
        const lastPartHtml = await getLastPartHtml({
          ssrHtml: useThreePartStream ? completeSsrHtml : ssrHtml,
          firstPartCacheHolder,
          erConfigTemplate: pageErConfig.template,
          isDebug,
          useThreePartStream,
          isTest,
          timeLog,
        });

        await writeHtml(lastPartHtml);
      }

      timeLog.timeStamp[`other_part_end`] = Date.now();
    } else if (disableCache && isDebug) {
      // 日常开发 或者 查问题使用。
      const ssrHtml = await getSsrHtml(ssrUrl, request, timeLog, '', isPre);
      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = ssrHtml && ssrHtml.indexOf('<body') > -1;
      if (!ssrHtmlValid) {
        throw new Error(`ssrHtml invalid(disable cache)|${ssrHtml}`);
      }

      // 获取一屏骨架图
      const firstPartHolder = await setFristPartCache({
        ssrHtml,
        pageErConfig,
        firstPartCacheKey,
        isPre,
        isDebug,
        writeHtml,
      });

      // 如果骨架图解析失败，则抛出异常
      if (!firstPartHolder) {
        throw new Error('decode erConfig template error');
      }

      // 返回一屏骨架图
      await writeHtml(firstPartHolder);

      // 只返回第一屏数据
      if (onlyShowFirstPart) {
        await writeHtml('</body></html>');
        await writer.close();
        return;
      }

      // 如果是三段流式，则返回二屏数据
      if (useThreePartStream) {
        const secondSsrHtml = await getSsrHtml(secondPartSsrUrl, request, timeLog, 'three_', isPre);
        const secondPartHtml = await getSecondPartHtml({
          ssrHtml: secondSsrHtml,
        });

        await writeHtml(secondPartHtml);

        // 只返回第一、第二屏数据
        if (onlyShowSecondPart) {
          await writer.close();
          return;
        }
      }

      // 返回最后一屏数据
      const lastPartHtml = await getLastPartHtml({
        ssrHtml,
        firstPartCacheHolder: firstPartHolder,
        erConfigTemplate: pageErConfig.template,
        isDebug,
        timeLog,
      });

      await writeHtml(lastPartHtml);
    } else {
      // 当第一屏数据缓存获取失败 或 版本号不一致 或 缓存更新时间小于等于er配置时间，则请求ssr文档
      const ssrHtml = await getSsrHtml(ssrUrl, request, timeLog, '', isPre);
      // 如果ssr文档为空，则抛出错误，强制回源
      const ssrHtmlValid = ssrHtml && ssrHtml.indexOf('<body') > -1;
      if (!ssrHtmlValid) {
        throw new Error(`ssrHtml invalid(no cache)|${ssrHtml}`);
      }

      // 返回完整ssr文档
      await writeHtml(ssrHtml);

      // 存入首屏缓存
      await setFristPartCache({
        ssrHtml,
        pageErConfig,
        firstPartCacheKey,
        isPre,
        isDebug,
        writeHtml,
      });
    }
  } catch (e) {
    // 如果catch，则跳转market页面
    if (isDebug) {
      await writeHtml(`<div id="error">${e && e.message}</div></body></html>`)
    } else {
      sendSlsLog(e, reqUrl, timeLog);
      const newUrl = new URL(raxUrl);
      newUrl.searchParams.set('_er_failback', '1');
      newUrl.searchParams.set('ssr_fail_back', '1');
      await writeHtml(`<script>location.replace("${newUrl.toString()}")</script></body></html>`);
    }
  }

  event.waitUntil(ssrLog(request.headers, reqUrl, {
    logName: isPre ? 'ssr_pre_server_log' : 'ssr_server_log',
    logType: 'server',
    logData: {
      ...timeLog,
      timeStamp: {
        ...timeLog.timeStamp,
        er_end: Date.now(),
        stream_end: Date.now(),
      },
      er_duration: Date.now() - timeLog.timeStamp.er_start,
      stream_duration: Date.now() - timeLog.timeStamp.stream_start,
      er_config_duration: timeLog.timeStamp.er_config_end - timeLog.timeStamp.er_config_start,
      er_cache_duration: timeLog.timeStamp.er_cache_end - timeLog.timeStamp.er_cache_start,
      html_stringify_duration: timeLog.timeStamp.html_stringify_end - timeLog.timeStamp.html_stringify_start,
      first_part_duration: timeLog.timeStamp.first_part_end - timeLog.timeStamp.first_part_start,
      other_part_duration: timeLog.timeStamp.other_part_end - timeLog.timeStamp.other_part_start,
    }
  }));
  await writer.close();
}

// [debug]判断是否开启流式渲染
async function useStreamRender(event, opt) {
  const {
    reqUrl,
    ssrUrl,
    secondPartSsrUrl,
    raxUrl,
    request,
    _startTime,
    isPre,
    timeLog
  } = opt;
  timeLog.timeStamp[`stream_start`] = Date.now();

  const useStream = reqUrl.searchParams.get('_use_stream');
  const useThreePartStream = reqUrl.searchParams.get('_use_three_part_stream');
  const isDebug = reqUrl.searchParams.get('_debug_er');
  const disableCache = reqUrl.searchParams.get('_disable_cache');
  const onlyShowFirstPart = reqUrl.searchParams.get('_debug_first_screen');
  const onlyShowSecondPart = reqUrl.searchParams.get('_debug_second_screen');
  const getErConfig = reqUrl.searchParams.get('_get_er_config');
  const isTest = reqUrl.searchParams.get('_tusi_test');
  const httpCache = reqUrl.searchParams.get('_http_cache');
  const etagCache = reqUrl.searchParams.get('_http_etag');
  const isHomeTest = reqUrl.searchParams.get('_home_er_test');

  // 获取页面配置
  timeLog.timeStamp[`er_config_start`] = Date.now();
  let pageErConfig = null;
  let isGray = false;
  try {
    // "/app/trip/rx-fliggy-sqk/pages/home" --> "rx-fliggy-sqk_home"
    const reqPathArr = reqUrl.pathname.split('/');
    let erDataStr;
    // 预发拿不到 ER 后台配置，只能请求ssr后台获取配置
    if (isPre) {
      erDataStr = await getMiniworkErConfig(reqPathArr[3], reqPathArr[5]);
      pageErConfig = JSON.parse(erDataStr);
    } else {
      const pageErConfigKey = `${reqPathArr[3]}_${reqPathArr[5]}`;
      // 获取流式配置，50ms超时
      const pagePromise = [new Promise(async(resolve, reject) => {
        const kvRes = await getKvCache('fliggyrax_124215', pageErConfigKey);
        resolve(kvRes);
      }), new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve({});
        }, 50);
      })];
      const pageErConfigData = await Promise.race(pagePromise);
      erDataStr = pageErConfigData.data;
      pageErConfig = JSON.parse(erDataStr);

      // 如果命中灰度，则使用灰度中的骨架配置
      isGray = checkIsGray(pageErConfig, request);
      if (isGray && pageErConfig.grayTemplate && pageErConfig.grayVersion) {
        pageErConfig = {
          cacheKey: pageErConfig.grayCacheKey,
          template: pageErConfig.grayTemplate,
          version: pageErConfig.grayVersion,
          updateTime: pageErConfig.grayUpdateTime,
        };
      }
    }
  } catch (err) {
    pageErConfig = false;
  }

  if (getErConfig) {
    return JSON.stringify(pageErConfig);
  }

  // 如果流式配置不存在 或 不开启流式渲染，则不开启流式
  if (!pageErConfig || !pageErConfig.template || !useStream) return false;

  // 获取titlebar数据
  const titleBarOpt = getTitleBarDefaultHeight(request, reqUrl.searchParams);

  timeLog.timeStamp[`er_config_end`] = Date.now();

  try {
    const { writable, readable } = new TransformStream();
    // 流式的拼接html文本
    event.waitUntil(writeStream({
      writer: writable.getWriter(),
      pageErConfig,
      titleBarOpt,
      reqUrl,
      ssrUrl,
      secondPartSsrUrl,
      raxUrl,
      request,
      _startTime,
      isPre,
      isGray,
      isDebug,
      isTest,
      isHomeTest,
      disableCache,
      onlyShowFirstPart,
      onlyShowSecondPart,
      useThreePartStream,
      timeLog,
      event
    }).catch(e => {
      sendSlsLog(e, reqUrl, timeLog);
    }));

    let cacheConfig = {};
    // 协商缓存
    if (etagCache) {
      const headerCookie = request.headers.get('cookie') || '';
      const etagReg = headerCookie.match(/_er_etag=(\S+);/) || headerCookie.match(/_er_etag=(\S+)$/);
      const etagValue = etagReg && etagReg.length > 1 ? etagReg[1] : 'initial';
      const etagTime = Date.now() + parseInt(reqUrl.searchParams.get('_http_etag')) * 1000;
      // etag规范：过期时间戳-etag标识，加单个双引号用于弱匹配校验，详见https://aone.alibaba-inc.com/v2/project/1109730/req/55582160
      // 手淘PHA文档预加载协商缓存无效，通过写cookie形式实现
      cacheConfig = {
        'etag': `"${etagTime}-${etagValue}`,
        'set-cookie': `_if_none_match=${etagTime}-${etagValue}; Max-Age=${parseInt(reqUrl.searchParams.get('_http_etag'))}`
      }
    }
    // 强缓存
    if (timeLog.userId && TAOBAO_PHA_WHITE_LIST.indexOf(timeLog.userId) >= 0) {
       // 手淘测试场景，全量开启
      cacheConfig = {
        'cache-control': `max-age=10800`
      }
    } else if (httpCache) {
      cacheConfig = {
        'cache-control': `max-age=${httpCache}`
      }
    }

    return new Response(readable, {
      headers: {
        'content-type': 'text/html; charset=utf-8',
        'use-stream': '1.0.8',
        "Transfer-Encoding": "chunked",
        "streaming-parser": "open",
        ...cacheConfig
      },
      status: 200,
    });
  } catch (err) {
    if (isDebug) {
      return err.message;
    } else {
      // 增加降级标识
      let redirectRaxUrl = '';
      try {
        const raxUrlObj = new URL(raxUrl);
        raxUrlObj.searchParams.set('ssr_fail_back', '1');
        redirectRaxUrl = raxUrlObj.href;
      } catch {
        redirectRaxUrl = raxUrl;
      }
      return Response.redirect(redirectRaxUrl);
    }
  }
}

// ------ 度假风格流式渲染 ------
const streamRenderConfig = {
  '/app/trip/rx-travel-detail/pages/index': {
    // 强制开启, 或 url上_enable_stream 动态开启
    enable: true,
    // --- FCP模板配置
    // 远程基础顶部html，和发布关联上传+预热cdn
    remoteBasicHtmlUrl: 'https://traveldetail.feizhu.com/streambasic/rx-travel-detail.html',
    // 静态兜底模板，无数据渲染仅比白屏好一点
    staticTemplate: '<div style="position:fixed;background:#f3f3f3;overflow:hidden;height:100vh;width:100vw;z-index:10;top:0;left:0;right:0;bottom:0;"><div style=height:100vw;width:100vw;margin-bottom:2.4vw></div><div style="font-size:0;">%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D%E6%88%91%E6%98%AF%E4%B8%80%E4%B8%AA%E5%8D%A0%E4%BD%8D</div><img src="https://gw.alicdn.com/imgextra/i1/O1CN01MwAFGF1wu8c1qURA5_!!6000000006367-2-tps-42-42.png" style="opacity:0;width:100vw;position:absolute;" /><div><div style="background-color:rgb(250,250,250);padding:3.2vw;margin:0 2.4vw 2.4vw;border-radius:2.1333vw;box-sizing:border-box"><div style="margin-top:1.06667vw;height:6.4vw;margin-bottom:1.6vw;width:26.66667vw;background-color:rgb(240,240,240);border-radius: 1.6vw;"></div><div style="height:21.7333vw;line-height:5.86667vw;padding:0 0 2.13333vw;background-color:rgb(240,240,240);border-radius: 1.6vw;"></div></div><div style="background-color:rgb(250,250,250);padding:3.2vw;margin:0 2.4vw 2.4vw;border-radius:2.1333vw;box-sizing:border-box"><div style="margin-top:1.06667vw;height:6.4vw;margin-bottom:1.6vw;width:26.66667vw;background-color:rgb(240,240,240);border-radius: 1.6vw;"></div><div style="height:21.7333vw;line-height:5.86667vw;padding:0 0 2.13333vw;background-color:rgb(240,240,240);border-radius: 1.6vw;"></div></div></div></div>',
    // 动态渲染模板，@ali/mpi-detailer-screen-parse的编译结果
    // --- FSP数据配置
    // 每次访问的数据缓存根据url什么字段区分存储，
    cacheKey: ['id', 'itemId', 'item_id'],
    // 获取最终faas调用时的链接，可处理一些超长参数过滤/faas调用路径定制
    // 定制请求faas的url，入参是 URL对象，返回值需是string
    // 远程数据
    remoteCacheUrl: 'https://traveldetail.feizhu.com/detailsnapshot',
    // 试验手淘端缓存开关
    fccConfig: {
      rule: "id",
      expire: Date.now() + 1000 * 60 * 5
    },
    // 兜底模板数据
    defaultData: {
      action: [
        ['客服', 'https://gw.alicdn.com/imgextra/i1/O1CN011eO3fb23lcO6TstkM_!!6000000007296-2-tps-144-144.png_60x60.jpg'],
        ['店铺', 'https://gw.alicdn.com/imgextra/i4/O1CN017wocSy1NaQwyrs3wR_!!6000000001586-2-tps-54-54.png'],
        ['收藏', 'https://gw.alicdn.com/imgextra/i4/O1CN01UFdeG01Ha52oy6Hh4_!!6000000000773-2-tps-84-84.png'],
      ],
    },
    getFaasRequestUrl(urlInstance) {
      // 过滤一些各渠道不需要给服务端的埋点参数，超长导致奇奇怪怪问题
      const filterMap ={
        referer:1, // 路由层加的，坑
        headInfo: 1, // 啥玩意
        utparam: 1, // 手淘埋点参数
        s_share_url:1, // 联盟端外推广参数
        eurl:1, // 妈妈的广告埋点
        // 手淘详情的参数
        title: 1,
        pic_path: 1,
        search_keyword: 1,
        list_param: 1,
        detailAlgoParam: 1
        //
      }
      const entries = urlInstance.searchParams.entries();
      for(let p of entries) {
        const [key, val] = p;
        if (filterMap[key] || key[0]=='_') {
          urlInstance.searchParams.delete(key);
        }
      }
      // 定制faas调用函数
      return urlInstance.toString().replace('/app/trip/rx-travel-detail/pages/index', '/detail/app/trip/rx-travel-detail/pages/index')
    },
  }
}
/*
 * stream-stage1, 前置检测，不满足条件回退源码SSR
 */
async function checkStreamRender(event, { isPre, startTime, reqUrl, ssrUrl, raxUrlString, request }) {
  const enableStream = reqUrl.searchParams.get('_enable_trip_stream');
  const enableTaobaoCache = reqUrl.searchParams.get('_enable_fcc');
  const isDebug = reqUrl.searchParams.get('_debug_er');
  const codeV = '30';
  try {
    let pageConfig = streamRenderConfig[reqUrl.pathname];
    // 非内置配置
    if (!pageConfig) {
      // 检测远程配置
      if (!enableStream){
        return false;
      }
      const remoteConfig = await loadRemoteStreamConfig(isPre);
      pageConfig = remoteConfig[reqUrl.pathname];
      if (!pageConfig) {
        return false;
      }
    }
    // 实验阶段，没有参数的不开启
    if (!pageConfig.enable) {
      return false;
    }
    const renderContext = getRenderContext({
      isPre,
      codeV,
      startTime,
      isDebug,
      pageConfig,
      // csr完整链接
      raxPageUrlString: raxUrlString,
      request: reqUrl,
      requestFaasHeadersInstance: request.headers,
      requestFassUrlInstance: ssrUrl
    });

    if (!renderContext) {
      return false;
    }
    const { writable, readable } = new TransformStream();
    // 根据配置和缓存获取项目信息
    const headers = {
      'er-code-v': codeV,
      'content-type': 'text/html; charset=utf-8',
      'use-stream': '1.0.8',
      "Transfer-Encoding": "chunked",
      "streaming-parser": "open",
    }
    // 试验端缓存
    if (enableTaobaoCache && pageConfig.fccConfig) {
      const fccCfg = pageConfig.fccConfig;
      headers["fcc-enable"] = "true";
      if (fccCfg) {
        if (fccCfg.rule) {
          headers["fcc-url-query-rule"] = fccCfg.rule;
        }
      }
    }
    // 流式的拼接html文本
    event.waitUntil(renderStreamPage(renderContext, { writable }).catch(onErrorWarning));

    return new Response(readable, {
      headers,
      status: 200,
    });
  } catch (e) {
    if (isDebug) {
      return `checkStreamRender error:${e.message}`;
    }
    onErrorWarning(e);
  }
  return false;

    // 流式渲染，出错钉钉告警
  function onErrorWarning(err) {
    fetch('https://oapi.dingtalk.com/robot/send?access_token=364245ba6cc3527183ee1b09b77c4ca2fb8d4ec10860c93ea627e3796b8b85eb',
      {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: `{"msgtype": "text","text": {"content":"er-stream-error[${isDebug?'debug':'online'}]: ${err.message || err}"}}`, mode: 'no-cors'
      }).catch(() => {});
  }
}
async function loadRemoteStreamConfig(isPre) {
  try {
    return await fetch(`https://traveldetail.feizhu.com/config/${isPre?'pre-':''}stream.json`, {
      cdnProxy: true // 必须是接入阿里cdn的域名才有加速
    }).then(res => res.json())
  } catch (err) {
    return {};
  }
}
/*
 * stream-stage2，环境抹平/上下文工具
*/
function getRenderContext({  isPre, codeV, startTime, isDebug, pageConfig, raxPageUrlString, request, requestFaasHeadersInstance, requestFassUrlInstance }) {
  // 获取本次请求的key
  const cacheKeyValue = getDataCacheKey(pageConfig);
  if (!cacheKeyValue) {
    return false;
  }
  const tokenRender = initTokenRender();
  const log = initLog();
  const perf = initPerf(startTime);
  const pageCachKey = `${request.pathname}/base`;
  const dataCachKey = `${request.pathname}?key=${cacheKeyValue}`;
  const isPreLoadSSR = request.searchParams.get('_fli_pressr');
  const isPreLoadCssInline = isPreLoadSSR && request.searchParams.get('_css_inline');
  const useOnlineData = request.searchParams.get('_online_data');
  // 预热
  const isPreheat = request.searchParams.get('_er_preheat');
  // 静态兜底元素
  const staticHolderId = '_stream_exholder';
  // 动态渲染元素
  const dynamicHolderId = '_stream_holder';
  return {
    errorId: codeV,
    perf,
    log,
    isPre,
    isDebug,
    isPreheat,
    // 飞猪端内页面上游预加载html
    isPreLoadSSR,
    isPreLoadCssInline,
    isForceUpdateBasic: request.searchParams.get('_debug_er_base'),
    // 页面自定义配置
    pageConfig,
    // 本次请求的缓存Key
    cacheKeyValue,
    // ssr内容起点
    ROOT_START_REG: /\<div\s+id=['"]root['"]/g, // '<div id="root"'
    // Faas数据起点
    FAAS_START_REG: /\<script\s+data-from=['"]server['"]/g, // <script data-from="server"
    staticHolderId,
    dynamicHolderId,
    // 加载基础html，用以头部拼接
    getBasicHtml,
    // 更新基础html
    setBasicHtml,
    // 当ssr文档下载完全时，解析项目数据缓存
    loadFaasHtml,
    // 获取首屏内容
    getFirstContent,
    // 获取降级csr的链接
    getFallBackUrl,
    // 预热一些静态资源拉到cdn上
    preheatResource
  }


  async function preheatResource() {
    return Promise.all([
      getBasicHtml(),
      getTemplateData(),
    ]).then(() => 'success').catch(e => e.message)
  }

  async function getFirstContent() {
    const renderData = await getTemplateData();
    if (pageConfig.templateToken) {
      try {
        return tokenRender.renderHtmlString(pageConfig.templateToken, renderData)
      } catch (err) {
        log.add('parseTokenFail', err.message);
      }
    }

     // KV失败率挺高的，这里直接返回值
     if (pageConfig.dynamicTemplate){
      const result = UTIL.parseTemplate(pageConfig.dynamicTemplate,renderData)
      return result.data;
    }
  }

  function getFallBackUrl(exParams) {
    const split = raxPageUrlString.indexOf('?') > -1 ? '&' :'?';
    const addtionString = Object.keys((exParams || {})).reduce((memo, key) => {
      if (typeof exParams[key] !=='undefined') {
        memo.push(`${key}=${exParams[key]}`)
      }
      return memo
    }, []).join('&')
    return `${raxPageUrlString}${split}${addtionString}`
  }

  async function getBasicHtml() {
    const topHtmlString = await Promise.race(
      [
        loadRemote(),
        loadLocal(),
        new Promise((resolve, reject) => {
          setTimeout(() => {
            resolve('');
          }, 100)
        })
      ]
    )

    const v = getVersionFromHtml(topHtmlString);
    if (!v) {
      return "";
    }

    return topHtmlString;

    function loadLocal() {
      return new Promise(function (res){
        const releasePerf = perf.stage('loadLocalBase');
        UTIL.getJsonCache(pageCachKey).then(result => {
          releasePerf();
          const e = (result.data || {}).htmlText;
          if (e) {
            res(e)
          }
          log.add('loadLocalBase', function () {
            return {
              url: pageCachKey,
              html: e ? encodeURIComponent(e) : '',
            }
          });
        })
      })
    }

    async function loadRemote() {
      if (!pageConfig.remoteBasicHtmlUrl) {
        return '';
      }
      const releasePerf = perf.stage('loadRemoteBase');
      const e = await fetch(pageConfig.remoteBasicHtmlUrl, { cdnProxy: true }).then((res) => res.text()).catch(() => '');
      releasePerf();
      log.add('loadRemoteBase', function (){
        return {
          url: pageConfig.remoteBasicHtmlUrl,
          html: encodeURIComponent(e)
        }
      });
      return e;
    }
  }

  async function setBasicHtml(htmlText) {
    // 二次访问会自更新长缓存1月
    const cacheTime = 60 * 60 * 24 * 30;
    const e = await UTIL.setJsonCache(pageCachKey, {
      htmlText
    }, cacheTime);
    log.add('setBasicHtml',function () {
      return {
        v: getVersionFromHtml(htmlText),
        length: htmlText.length,
        errorMessage: e.errorMessage,
      }
    })
  }

  async function loadFaasHtml() {
    let faasUrlString = '';
    try {
      faasUrlString = pageConfig.getFaasRequestUrl ? pageConfig.getFaasRequestUrl(requestFassUrlInstance) : requestFassUrlInstance.toString();
    } catch(err) {
      faasUrlString = requestFassUrlInstance.toString();
      log.add('getFaasRequestUrlError',  err.message);
    }
    const timeout = 'timeout';
    const releasePerf = perf.stage('loadFaasHtml');
    const htmlText = await Promise.race([
      fetch(faasUrlString, {
        headers: filterLongHeaders(),
      }).then(res => res.text())
      ,
      // 5秒超时
      new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(timeout);
        }, 5000);
      })
    ]);
    // 暴露错误类型上报日志
    if (htmlText == timeout) {
      const err = new Error(timeout);
      err.code = timeout;
      throw err;
    }
    perf.set('faaslength', htmlText.length);
    releasePerf();

    setDataCacheFromSsr();

    return htmlText || '';

    async function setDataCacheFromSsr(){
      if (htmlText) {
        let message = [];
        let data = null;
        const dataString = getMetaData(htmlText, "er-cache-data");
        // 最多尝试2次解码
        if (dataString) {
          try {
            data = JSON.parse(decodeURIComponent(dataString))
          } catch (err) {
            message.push(err.message);
            try {
              data = JSON.parse(decodeURIComponent(decodeURIComponent(dataString)));
            } catch (err2) {
              message.push(err2.message)
            }
          }
        }
        // 每次请求都会更新掉缓存，所以这里久点没影响
        if (data) {
          const e = await UTIL.setJsonCache(dataCachKey, data, 60 * 60 * 24 * 30);
          message.push(e.errorMessage);
        }
        log.add('setDataCacheFromSsr', {
          dataString,
          message
        })
      }
    }

    function filterLongHeaders() {
      const e = requestFaasHeadersInstance.entries();
      const newHeaders = new Headers();
      const usedKey = [];
      const origin = [];
      const whiteList = {
        's-cookie':1,
        'cookie':1,
        'user-agent':1,
      }
      try {
        for (const p of e) {
          const [key, val] = p;
          origin.push(p)
          if (whiteList[key] || val.length < 500) {
            newHeaders.append(key, val);
            usedKey.push(key);
          }
        }
      } catch(err) {
        return requestFaasHeadersInstance;
      }
      return newHeaders
    }

  }

  async function getTemplateData() {
    const logData = {};
    // 同时请求本地和远程，谁有数据用谁
    // 当本地没有数据就等远程返回直到超时
    const renderData = await Promise.race([
    loadRemote(),
    loadLocal(),
      // 300ms超时，不可能有这么长吧
    new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve('');
      }, 300);
    })]);
    logData.useType = renderData ? renderData.__type : '';
    const e = formatData(renderData);
    logData.templateData = e;
    log.add('getTemplateData', logData);
    return e;

    async function loadRemote() {
      const remoteCacheUrl = pageConfig.remoteCacheUrl;
      if (!remoteCacheUrl) {
        return null;
      }
      const url = `${remoteCacheUrl}/${isPre&&!useOnlineData?'pre-':''}${cacheKeyValue}.json`;
      const releasePerf = perf.stage('loadRemoteData');
      const result = await fetch(url, {
        cdnProxy: true // 必须是接入阿里cdn的域名才有加速
      }).then(res => res.json()).catch((err) => {
        logData.remoteErrorMessage = err.message;
        return ''
      })
      logData.remote = result;
      if (!result) {
        return null;
      }
      releasePerf();
      result.__type = 'remote'
      return result;
    }

    function loadLocal(){
      return new Promise((res) => {
        // cdn本地缓存
        const releasePerf = perf.stage('loadLocalData');
        UTIL.getJsonCache(dataCachKey).then(cacheResult => {
            releasePerf();
            logData.local = cacheResult;
            // 本地有数据就释放，无数据就一直Hold
            if (cacheResult.data) {
              cacheResult.data.__type ="local";
              res(cacheResult.data)
            }
        })
      })
    }

     // 根据是否是ios系统，给予额外的入参控制底部按钮高度
     function formatData(data) {
      data = data || {}
      const userAgent = requestFaasHeadersInstance.get('user-agent') || requestFaasHeadersInstance.get("User-Agent");
      const os = getOSName(userAgent);
      if (os == 'ios') {
        const iosPadding = "5.8667vw";
        data.bottomBar = `padding-bottom:${iosPadding};`;
        data.iosPadding = iosPadding;
      }
      if (data.expire > 0 && data.e) {
        const current = getBeiJingDateNow();
        // 过期删除
        if (current > data.expire) {
          const { e, ...others } = data;
          log.add('expireData', e);
          return {
            ...(pageConfig.defaultData || {}),
            ...others
          }
        }
      }
      return {
        ...(pageConfig.defaultData || {}),
        ...(data||{})
      }
    }
  }

  function initPerf(requestStartTime) {
    const origin = Date.now();
    const state = {
      origin,
      wait: origin - requestStartTime
    };
    return {
      set(key, val) {
        state[key] = val;
      },
      stage(key) {
        const statrt = Date.now();
        return () => {
          state[key] = statrt - origin;
          state[`${key}Cost`] = Date.now() - statrt;
        }
      },
      toJsonString(){
        try {
          return JSON.stringify(state)
        } catch (err) {
          return ''
        }
      }
    }
  }

  function initLog() {
    const logTask = [];
    return {
      add(key, val, lazy = false) {
        if (!isDebug) {
          return;
        }
        let result = null;
        if (typeof val == 'function') {
          const run = () => {
            try {
              return val({})
            } catch (err) {
              return {
                errorMessage: err.message
              }
            }
          }
          result = lazy ? run : run();
        } else if (typeof val !== 'undefined') {
          result = val;
        } else {
          return
        }
        logTask.push([
          key,
          result
        ])
      },
      toJsonString() {
        const total = logTask.reduce((memo, task) => {
          const [key, value] = task;
          let result = '';
          try {
            if (typeof value == 'function') {
              result = JSON.stringify(value());
            } else {
              result = JSON.stringify(value);
            }
          } catch (err) {
            result = `"${err.message}"`
          }
          memo.push(`"${key}":${result}`)
          return memo;
        },[])
        return `{${total.join(',')}}`
      },
      remoteLog(e) {
        sendSlsLog(e, request);
      }
    }
  }

  // 流式渲染，获取本次请求的数据缓存Key
  function getDataCacheKey(config) {
    const keys = config.cacheKey || []
    for (let i = 0, len = keys.length; i < len; i++) {
      const data = request.searchParams.get(keys[i]);
      if (data) {
        return data;
      }
    }
    return null
  }
}
/*
 * stream-stage3，处理流式渲染
 */
async function renderStreamPage(ctx, { writable }) {
  const writer = writable.getWriter();
  const encoder = new TextEncoder();
  const writeHtml = (e) => writer.write(encoder.encode(e));
  let hasFcp = false;
  try {
    if (ctx.isPreheat) {
      const message = await ctx.preheatResource();
      await writeHtml(message)
      await writer.close();
      return;
    }
    // 渲染FCP+并行回源
    const [basicHtmlString, ssrHtmlString] = await Promise.all([
      renderFirstViewHtml(), // 绘制FCP
      ctx.loadFaasHtml() // 实时拉取SSR
    ]);
    // 回源数据有问题，直接降级
    const ssrContentValid = ssrHtmlString && ssrHtmlString.indexOf('<body') > -1;
    if (!ssrContentValid) {
      throw new Error('invalid')
    }

    // fcp渲染成功，截取部分ssr数据拼接渲染
    if (hasFcp) {
      await renderSecondViewHtml(ssrHtmlString);
    } else {
      ctx.log.add('backSSR', "1");
      // 没有fcp，全量回退SSR数据
      await writeHtml(ctx.isPreLoadCssInline ? await makeResourceInline(ssrHtmlString) : ssrHtmlString);
    }
    // 校验ssr页面版本是否需要更新
    await checkBasic(basicHtmlString, ssrHtmlString)

    if (ctx.isDebug) {
      await writeHtml(
        `<script data-id="steam-message-${ctx.errorId}">window._er_message=${ctx.log.toJsonString()}</script></body></html>`)
    }

  } catch (e) {
    if (ctx.isDebug) {
      await writeHtml(`<div data-id="steam-error-${ctx.errorId}">${e && e.message}</div></body></html>`)
    } else {
      // 可能上面都写html写一半了，已经不能302了，所以要输出个脚本让html执行去降级
      const fallBackUrl = ctx.getFallBackUrl({ _er_failback: e.code || 'error' })
      await writeHtml(`<script>location.replace("${fallBackUrl}")</script></body></html>`);
      ctx.log.remoteLog(e);
    }
  }
  await writer.close();

  // 首chunk渲染，先传最小兜底，cdn渲染完后再增量渲染
  async function renderFirstViewHtml() {
    // 预加载不做渲染
    if (ctx.isPreLoadSSR) {
      return
    }
    const releaseFirstPerf = ctx.perf.stage('renderFirst');
    // 最小最优避免白屏渲染 + 并行构造基于快照的渲染（快照数据源于ssr）
    const [baseContent, firstContent] = await Promise.all([
      renderBasic(),
      renderFirstContent()
    ]);

    try {
      if (hasFcp) {
        await writeHtml(firstContent);
        ctx.perf.set('firstlength', firstContent.length)
        releaseFirstPerf();
      } else {
        ctx.log.add('renderFirstError', 'nobase');
      }
    } catch (err) {
      // 很神奇的只有预发环境首次访问才报错，难道是2个writeHtml调用间隔太小了？
      ctx.log.add('renderFirstError', err.message);
    }
    return baseContent;

    async function renderBasic() {
      const releasePerf = ctx.perf.stage('renderBasic');
      const basic = await ctx.getBasicHtml();
      if (basic) {
        ctx.log.add('renderBasic', function (){
          return {
            htmlText: encodeURIComponent(basic)
          }
        })
        const group = [basic];
        const fallBackUrl = ctx.getFallBackUrl({ _er_failback: "timeout" });
        const backupScript = `<script data-id="backhandler">setTimeout(function (){if (!window._er_stream_success&&${ctx.isDebug?'false':'true'}){location.replace("${fallBackUrl}");console.info('er超时降级');}},4000)</script>`
        if (ctx.pageConfig.staticTemplate) {
          group.push(`<div id="${ctx.staticHolderId}">${ctx.pageConfig.staticTemplate}</div>`)
        }
        group.push(backupScript)
        await writeHtml(group.join(''));
        hasFcp = true;
        releasePerf();
      }
      return basic;
    }

    async function renderFirstContent() {
      // 绘制FCP
      const htmlString = await ctx.getFirstContent();

      const group = [];
      if (htmlString) {
        group.push(
          `<div id="${ctx.dynamicHolderId}">${htmlString}</div>`)
      }
      const perfScript = `<script data-id="steam-first-success">if(window.performance&&typeof window.performance.mark == 'function'){performance.mark('er_firstscreen');};window._er_firstscreen=Date.now();</script>`;
      group.push(perfScript)

      return group.join('');
    }
  }

  // 二屏渲染
  async function renderSecondViewHtml(htmlContent) {
    const [rootTag] = htmlContent.match(ctx.ROOT_START_REG) || [];
    const [faasTag] = htmlContent.match(ctx.FAAS_START_REG);
    if (rootTag && faasTag) {
      // ssr文本开始位置
      const contentStartPosition = htmlContent.indexOf(rootTag);
      // ssr服务端数据开始位置
      const scriptStartPosition = htmlContent.indexOf(faasTag);
      // 1. 因首段流式已经渲染了页面上半部分，这里只截取下半部分展示
      // 2. 在ssr可见模板之后，ssr数据赋值之前，插入耗时计算，并隐藏首段流式展示真正ssr内容
      const list = [
        htmlContent.slice(contentStartPosition, scriptStartPosition),
        `<script data-id="second-success">window._er_streamgap = Date.now() - window._er_firstscreen;window._er_stream_success = true;var _hide_els = function (els) {var len = els.length;for (var i = 0; i < len; i++) {try {var el = document.getElementById(els[i]);if (el) { el.style.display = "none" } } catch (err) { }}};var _target = ["${ctx.staticHolderId}", "${ctx.dynamicHolderId}"];_hide_els(_target);window._er_perf = ${ ctx.perf.toJsonString() }</script>`,
        htmlContent.slice(scriptStartPosition),
      ]
      return writeHtml(list.join(''));
    }
    throw new Error('ssr server failed')
  }

  // 校验HTML Top内容是否需要更新
  async function checkBasic(basicString, ssrString) {
    // 没有首屏渲染或ssr版本更新了后，更新缓存的基础html
    if (hasFcp && basicString) {
      const cacheVersion = getVersionFromHtml(basicString);
      const parseVersion = getVersionFromHtml(ssrString);
      ctx.log.add('checkBasic', {
        cacheVersion,
        parseVersion
      })
      if (cacheVersion !== parseVersion || ctx.isForceUpdateBasic) {
        await ctx.setBasicHtml(getFirstScreenBaseHtml());
      }
      return
    }
    await ctx.setBasicHtml(getFirstScreenBaseHtml());

     // 获取一屏拼接所需的基础html，添加对css和js的preload
     function getFirstScreenBaseHtml() {
      const [rootTag] = ssrString.match(ctx.ROOT_START_REG) || [];
      if (!rootTag) {
        return '';
      }
      const contentStartPosition = ssrString.indexOf(rootTag);
      if (contentStartPosition == -1) {
        return '';
      }
      const topContent = ssrString.slice(0, contentStartPosition);
      const bottomContent = ssrString.slice(contentStartPosition);
      // 基于浏览器标签对后续的js和css做预加载
      const scripts = [...bottomContent.matchAll(/<script.+?src='((?:https:)?\/\/g\.alicdn\.com[^']+)'/g)];
      const preloadTags = scripts.reduce((acc, cur) => {
        if (cur && cur[1]) {
          acc += `<link rel="preload" href="${cur[1]}" as="script" crossorigin="anonymous" />`;
        }
        return acc;
      }, '')

      const group = [];
      if (preloadTags) {
        const headEndPosition = topContent.indexOf('</head>');
        // 兜兜兜..
        if (headEndPosition > -1) {
          group.push(...[
            topContent.slice(0, headEndPosition),
            preloadTags,
            topContent.slice(headEndPosition)
          ])
        }
      } else {
        group.push(topContent)
      }

      return group.join('');
    }
  }

  // 将html内的远程css全部加载完备好
  async function makeResourceInline(htmlText) {
    const logData = {
      list:[]
    };
    try {
      const list = await Promise.all([
        // ...getScriptTarget(htmlText),js太大了
        ...getCssTarget(htmlText)
      ]);
      if (!list.length) {
        return htmlText
      }
      ctx.log.add('makeResourceInline', logData)
      return list.reduce((currentHtml, config) => currentHtml.replace(config.origin, config.next),htmlText)
    } catch (e) {
      ctx.log.add('makeResourceInlineError', e.message)
      return htmlText
    }

    function wrapHttps(url) {
      return url[0] == '/' && url[1] == '/' ? `https:${url}`: url;
    }

    function getCssTarget(e){
      return (e.match(/\<link[^\>]+>/g) || []).filter(l => /rel=['"]+stylesheet/.test(l)).reduce((memo, origin) => {
        const matched = origin.match(/\s*href=['"](.*?)['"]\s*/);
        if (matched) {
          const url = matched[1];
          if (url) {
            logData.list.push(url)
            const task = fetch(wrapHttps(url), { cdnProxy: true }).then(res => res.text()).then(text => {
              return {
                origin,
                next: `<style data-url="${url}">${text}</style>`
              }
            })
            memo.push(task)
          }
        }
        return memo;
      }, [])
    }
  }
}

function getBeiJingDateNow() {
  // 时差处理，若当前环境不是北京时间，则调整时间到其对应时区时间显示
  const bjOffset = -480;
  // 获取当前环境与0时区的时差 单位是分
  const timeOffset = new Date().getTimezoneOffset() - bjOffset;
  const date = new Date();
  date.setMinutes(date.getMinutes() + timeOffset);
  return date.valueOf();
}


// ---------- 度假流式结束 ----

/**
 * 判断用户是否在流式渲染的灰度中
 * */
function checkIsGray(pageErConfig, request) {
  const grayRatio = pageErConfig && pageErConfig.grayRatio || 0;
  const grayTargetUserList = (pageErConfig && pageErConfig.grayUserList) ? pageErConfig.grayUserList.split(',') : [];

  const cookies = request.headers && request.headers.get('cookie');
  const unbMatches = cookies.match(/unb=(\d+);/);
  const munbMatches = cookies.match(/munb=(\d+);/);
  const unsafeUserId = (unbMatches && unbMatches[1]) || (munbMatches && munbMatches[1]) || '';
  const unsafeUserIdSuf = unsafeUserId.slice(-2);

  return grayTargetUserList.includes(unsafeUserId) || (parseInt(unsafeUserIdSuf) < parseInt(grayRatio));
}

/**
 * 从 URL 参数和 cookie 中获取参数转换成字符串
 * @param keyArr
 * @param headers
 * @param searchParams
 * @returns {string}
 */
async function getCacheParams(keyArr, { headers, searchParams, isYT }) {
  if (!keyArr || keyArr.length === 0 || !searchParams || !headers) {
    return '';
  }

  const newSearchParams = new URLSearchParams();
  // const res = [];
  if (keyArr.includes('cityCode')) {
    let value;
    if (searchParams.get('cityCode')) {
      value = searchParams.get('cityCode');
    } else if (headers.get('s-location-info')) {
      const clientLocationInfo = JSON.parse(headers.get('s-location-info'));
      value = clientLocationInfo.cityCode;
    } else if (isYT) {
      value = await getIpCityCode(headers);
    } else if (headers.get('cookie')) {
      const matches = headers.get('cookie').match(/_fli_cityCode=(\d+);/);
      if (matches && matches[1]) {
        value = matches[1];
      }
    } else {
      value = await getIpCityCode(headers);
    }
    if (/^\d{6}$/.test(value)) {
      newSearchParams.set('cityCode', value);
    }
  }

  keyArr.forEach((key) => {
    if (key === 'cityCode') return;
    const value = searchParams.get(key)
    if (value) {
      newSearchParams.set(key, value);
    }
  });

  if (isYT) {
    newSearchParams.set('yt', '1');
  }

  return newSearchParams;
}

async function getIpCityCode(headers) {
  const ts = Date.now();
  try {
    headers.set('fli-bk', '1');
    headers.set('fli-ts', ts.toString());
    const res = await fetch(`https://fliggyrax.taobao.com/city-info?token=${Math.sin(ts).toString().slice(-9, -1)}&source=er`, {
      headers
    });
    const cityInfo = await res.json();
    return cityInfo.cityCode || '';
  } catch (e) {
    return '';
  }
}

/**
 * 判断系统类型 from rxpi-env
 * @param ua
 * @returns {string}
 * getOSName(request.headers.get('user-agent') || '');
 */
function getOSName(ua) {
  if (!ua) return '';

  if (/(Android);?[\s/]+([\d.]+)?/.test(ua)) {
    return 'android'
  }

  if (/(iPhone\sOS)\s([\d_]+)/.test(ua)) {
    return 'ios';
  }

  return 'unknown';
}

/**
 * 判断客户端
 * @param ua
 * @returns {string|*}
 */
function getAppName(ua) {
  if (!ua) return '';
  const appInfo = ua.match(/AliApp\(([^)]+)\)/) || {};
  let appName = appInfo[1] && appInfo[1].split('/')[0];

  if (appName === 'TB' ||
    appName === 'taobao' ||
    appName === 'com.taobao.taobao') {
    appName = 'TB';
  }

  return appName;
}


/**
 * 获取titlebar默认高度
 * */
function getTitleBarDefaultHeight(request, searchParams) {
  // titlebar数据
  let statusBarHeight = 0;
  let totalBarHeight = 44;
  let hasImmersiveQuery = false;
  let canUseImmersive = false;
  try {
    const cookie = request.headers.get('cookie') || '';
    const statusBarHeightMatches = cookie.match(/_fli_barHeight=(\d+(\.\d+)?);/);
    const totalBarHeightMatches = cookie.match(/_fli_titleHeight=(\d+(\.\d+)?);/);
    const ua = request.headers.get('user-agent') || '';
    const isTaobaoLiteApp = /TinyApp/i.test(ua);
    const isAndroid = getOSName(ua) === 'android';

    // 链接上是否有沉浸式参数
    hasImmersiveQuery = searchParams.get('disableNav') === 'YES' && searchParams.get('titleBarHidden') === '2';
    // 当前端是否支持沉浸式
    const appName = getAppName(ua);
    canUseImmersive = ['LX', 'LX-MINI', 'TB', 'LT', 'TM', 'BLX'].includes(appName) || isTaobaoLiteApp;

    // 状态栏高度
    statusBarHeight = Number(statusBarHeightMatches && statusBarHeightMatches[1]) || 0;
    if (isTaobaoLiteApp && searchParams.get('titleBarHidden') === '2') {
      statusBarHeight = statusBarHeight || (isAndroid ? 28 : 44);
    }

    // titlebar整体高度
    totalBarHeight = Number(totalBarHeightMatches && totalBarHeightMatches[1]) || (44 + statusBarHeight);
  } catch (e) {}

  return {
    statusBarHeight,
    totalBarHeight,
    hasImmersiveQuery,
    canUseImmersive,
  }
}

function removeHtmlCacheHeaders(headers) {
  const keys = ['cache-control', 'age', 'etag', 'vary'];
  for (const _k of keys) {
    headers.delete(_k);
  }
}


function removeErCacheHeaders(headers) {
  const keys = [
    'ali-proxy-consistent-hash',
    'ali-cdn-namespace',
    'ali-cdn-real-ip',
    'ali-swift-edge-dns-lookup',
    'ali-swift-global-savetime',
    'ali-swift-kv',
    'ali-swift-log-host',
    'ali-swift-stat-host',
    'x-client-scheme',
    'x-forwarded-for',
    'x-swift-savetime',
    'x-tworker-via',
    'x-tworker-fetch-uuid'
  ];
  for (const _k of keys) {
    headers.delete(_k);
  }
}

function removeFaasHeader(headers) {
  const keys = [
    'access-control-expose-headers',
    'x-fc-code-checksum',
    'x-fc-invocation-duration',
    'x-fc-invocation-service-version',
    'x-fc-max-memory-usage',
    'x-fc-request-id',
  ];
  for (const _k of keys) {
    headers.delete(_k);
  }
}

// --- 工具函数 ---
function parseTemplate(str, data) {
  try {
    data = data || {};
    return {
      data: (str || '').replace(/\{\{([a-zA-Z0-9]+)\}\}/g, function (matched, group) {
        return data[group] || ''
      }) || '',
      errorMessage: ''
    };
  } catch (err) {
    return {
      data: '',
      errorMessage: err.message
    }
  }
}
function getMetaData(htmlString, metaName) {
  try {
    const reg = new RegExp(`<meta\\s+name="?'?${metaName}"?'?\\s+content="?'?([^"'>]*)'?"?\/?>`);
    const result = htmlString.match(reg);
    if (result) {
      return result[1] || ''
    }
  } catch (err) {
  }
  return ''
}

function getVersionFromHtml(htmlText) {
  return getMetaData(htmlText || '', 'alitrip-project-version');
}

async function getJsonCache(cacheKey, isPre = false) {
  try {
    const data = await cache.get(`http://${isPre ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}${cacheKey}`)
    const jsonData = await data.json();
    return {
      cacheKey,
      errorMessage: '',
      data: jsonData
    }
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
      data: null
    }
  }
}

async function setJsonCache(cacheKey, jsonData, seconds = 60 * 60, isPre = false) {
  try {
    const jsonString = JSON.stringify(jsonData);
    const response = new Response(jsonString)
    response.headers.set('cache-control', `max-age=${seconds}`)
    const result = await cache.put(`http://${isPre ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}${cacheKey}`, response);
    return {
      cacheKey,
      errorMessage: result || '',
    }
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
    }
  }
}

async function getKvCache(namespace, key) {
  try {
    const edgeKv = new EdgeKV({ namespace });
    const data = await edgeKv.get(key, {
      type: 'text'
    })
    return {
      errorMessage: '',
      data
    }
  } catch (err) {
    return {
      errorMessage: err.message,
      data: null
    }
  }
}


// -------------预加载相关--------------
function getPreloadCacheKey(request, reqUrl, kvConfig) {
  try {
    const { hostname, pathname, searchParams } = reqUrl;

    const isPre = !(reqUrl.hostname === 'outfliggys.m.taobao.com' ||
      (WX_HOST.includes(reqUrl.hostname) && !reqUrl.hostname.includes('pre-')));
    const envTag = isPre ? 'envTag=pre' : '';

    // 静态SSR
    if (kvConfig) {
      if (kvConfig.key && kvConfig.key.length) {
        let searchStr = '';
        kvConfig.key.forEach(item => {
          const keyValue = searchParams.get(item) || '';
          searchStr = searchStr ? `${searchStr}&${item}=${keyValue}` : `${item}=${keyValue}`
        })
        return `http://${hostname}${pathname}?${searchStr}${(searchStr && envTag)? `&${envTag}` : envTag}`;
      } else {
        return `http://${hostname}${pathname}${envTag ? `?${envTag}`: ''}`;
      }
    }

    // 获取IP，未登录时使用
    const clientIP = request.headers.get("Ali-Cdn-Real-Ip");
    // 获取APPName
    const ua = request.headers.get('user-agent') || '';
    const appName = getAppName(ua) || 'H5';
    // 获取userid
    const cookie = request.headers.get('cookie') || '';
    const matches = cookie.match(/unb=(\d+);/);
    let cookieUserId = '';
    if (matches && matches.length > 1) {
      cookieUserId = (matches[1])
    }


    const searchStr = searchParams.toString().split('&');
    const searchObj = {};
    searchStr.forEach((item) => {
      const itemObj = item.split('=');
      searchObj[itemObj[0]] = itemObj[1];
    })
    if (cookieUserId) {
      searchObj.erUserId = cookieUserId;
    } else if (!searchObj['_fz_web_base_cache_key']){
      // 代理服务机器有多台IP不一致，代理场景统一走_fz_web_base_cache_key
      searchObj.erClientIP = clientIP;
    }
    searchObj.erAppName = appName;

    // 排序，避免生成的key不一致
    let keyMap = Object.keys(searchObj).sort();
    // 需要过滤的业务参数
    if (searchObj['_cache_ignore_key']) {
      keyMap = keyMap.filter(key => !searchObj['_cache_ignore_key'].includes(key))
    }
    // 过滤,转小写
    const KEY_MAP_FILTER = {
      'scm': true,
      'spm': true,
      'spmurl': true,
      'ttid': true,
      'fpt': true,
      'deviceid': true,
      'client_version': true,
      'client_type': true,
      'frm': true,
      'needlogin': true,
      '_fli_request_code': true,
      'hasback': true,
      'nsts': true,
      'imei': true,
      'nsns': true,
      '_fli_delay_init': true,
      '_fli_anim_type': true,
      'pre_pageversion': true,
      '_projver': true,
      '_fm_webview_first_': true,
      '_fm_real_host_': true,
      'page_env': true,
      'aplus_track_debug_id': true,
      '_pressr': true,
      '_premaxage': true,
      '_prenotclear': true,
      '_use_stream': true,
      '_fli_online': true,
      '_use_three_part_stream': true,
      '_force_cache':true,
      '_cachetest': true,
      '__back_url': true,
      '_ariver_appid': true,
      'aio_x_id': true,
      'utparam': true,
      'source': true,
      'enableloadingview': true,
      '_cache_ignore_key': true,
      'alipayhomepage': true,
      'alipaylayertired': true,
      'openwindowtime': true,
      '_er_cache': true,
      '_fli_no_cache': true,
      '__notify_name': true
    }
    const keyMapList = keyMap.filter(key => !KEY_MAP_FILTER[key.toLocaleLowerCase()]).map((key) => {
      return `${key}=${searchObj[key]}`;
    });
    if(isPre){keyMapList.push(envTag)}
    const keyStr = keyMapList.join('&');
    return `http://${hostname}${pathname}?${keyStr}`;
  } catch (e) {
    return '';
  }
}

// -------------检测降级地址--------------
function checkRaxRedirectUrl(origin, hostname) {
  try {
    // 参数携带csrSamesite走同域名降级
    const isSameSite = origin.search.includes('csrSamesite');
    if (isSameSite) {
      origin.host = hostname;
      origin.pathname = `/csr${origin.pathname}`;
    } else if (WX_HOST.includes(hostname)) {
      // 微信套壳降级域名替换
      origin.host = hostname.replace('proxy-er', 'proxy-h5');
      if (origin.search) {
      // _fm_real_host_替换
      origin.search = origin.search.replace('_fm_real_host_=pre-fliggyrax.wapa.taobao.com', '_fm_real_host_=market.wapa.taobao.com');
      origin.search = origin.search.replace('_fm_real_host_=outfliggys.m.taobao.com', '_fm_real_host_=market.m.taobao.com');
      }
    }else if(GD_HOST.includes(hostname)){
      origin.host = hostname.replace('front-traffic-fliggy-er', 'front-traffic-fliggy')
    }
  } catch (e) {}
}


// -------------耗时日志上报--------------
async function ssrLog(headers, reqUrl, logConfig) {
  const {
    logName,
    logType,
    logData
  } = logConfig;
  let groupName = '';
  let serviceName = '';
  let userId = '';
  let ip = '';
  let appName = '';
  let osName = '';

  try {
    const reqPathArr = reqUrl.pathname.split('/');
    groupName = reqPathArr[3];
    serviceName = reqPathArr[5];

    // 获取IP，未登录时使用
    ip = headers.get("Ali-Cdn-Real-Ip") || '';
    // 获取userid
    const headerCookie = headers.get('cookie') || '';
    const matchesReg = headerCookie.match(/unb=(\d+);/);
    if (matchesReg && matchesReg.length > 1) {
      userId = matchesReg[1] || '';
    };
    // 操作系统
    const userAgent = headers.get('user-agent') || '';
    osName = getOSName(userAgent);
    // 端环境
    appName = getAppName(userAgent) || 'OTHER';
  } catch { }

  Object.keys(logData).map(item => {
    logData[item] = JSON.stringify(logData[item]);
  })
  const data = {
    groupName,
    serviceName,
    logData: {
      ...logData,
      userId,
      ip,
      osName,
      appName,
    },
    logName,
    logType
  };

  await fetch(SLS_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({param: data}),
    mode: 'cors',
  });
}

// 流式首屏渲染器v2
function initTokenRender() {
  const U = initUtil();
  const E = initEnum();
  const NODE_HANDLER = initNodeHandler();
  // 全局上下文
  const CONTEXT = {
    // 注册的组件
    component: {},
  };
  return {
    renderHtmlString,
  };
  function renderHtmlString(arr, data) {
    return parseNodeList(arr, { data: data || {} }).join('');
  }
  function parseNodeList(list, options) {
    if (!list || !list.length) {
      return [];
    }
    // 纠错，可能把node当List传入了
    if (typeof list[0] === 'string') {
      const result = parseSingleNode(list, options);
      if (!result || !result.length) {
        return [];
      }
      return [result];
    }
    const groupState = {};
    return list.reduce((memo, child, index) => {
      const result = parseSingleNode(child, { ...options, index, groupState });
      if (result && result.length) {
        memo.push(...result);
      }
      return memo;
    }, []);

    function parseSingleNode(node, opt) {
      const nodeType = node[0];
      if (NODE_HANDLER[nodeType]) {
        return NODE_HANDLER[nodeType](node, opt);
      }
      return [];
    }
  }
  // 避免污染token，复制一份
  function copyNode(node, replaceState) {
    if (!node) {
      return [];
    }
    replaceState = replaceState || { tagAttr: {}, tagName: E.COMP.Fragment };
    const pureNode = [];
    pureNode[E.TOKEN_VALUE.type] = node[E.TOKEN_VALUE.type];
    pureNode[E.TOKEN_VALUE.children] = replaceState.children || node[E.TOKEN_VALUE.children];
    pureNode[E.TOKEN_VALUE.tagName] = replaceState.tagName || node[E.TOKEN_VALUE.tagName];
    pureNode[E.TOKEN_VALUE.tagAttr] = {
      ...(node[E.TOKEN_VALUE.tagAttr] || {}),
      ...(replaceState.tagAttr || {}),
    };
    return pureNode;
  }
  function readNode(node) {
    // 子标签集合
    const children = node[E.TOKEN_VALUE.children] || [];
    // 标签名，组件得是大写首字母
    const tagName = node[E.TOKEN_VALUE.tagName];
    // 标签属性，复制一份
    const tagAttr = node[E.TOKEN_VALUE.tagAttr] || {};
    return {
      children,
      tagName,
      tagAttr,
    };
  }

  // ----- 节点处理模块 -----
  function initNodeHandler() {
    return {
      text(node, { data }) {
        if (node[1] && node[1].length) {
          return node[1].map(txt => U.parseTemplate(txt, data));
        }
      },
      tag: parseTag,
    };
    function parseTag(node, opt) {
      const { tagName, tagAttr, children } = readNode(node);
      if (!tagName) {
        return;
      }
      // ---- 「标签」Template注册 ----
      if (tagName == E.COMP.template) {
        if (!tagAttr[E.DIRECT.xName]) {
          return;
        }
        CONTEXT.component[tagAttr[E.DIRECT.xName]] = children;
        return;
      }
      // ---- 「指令」 x-if/x-else ----
      if (tagAttr[E.DIRECT.xIf]) {
        const key = tagAttr[E.DIRECT.xIf];
        const revert = key[0] == '!';
        const data = U.getData(opt.data, revert ? key.slice(1) : key);
        const enable = revert ? !data : !!data;
        if (!enable) {
          // 记录最近一次xif判断为false的序号
          opt.groupState.xIfFalseIndex = opt.index;
          return;
        }
      } else if (!U.inValid(tagAttr[E.DIRECT.xElse])) {
        // ---- 「指令」x-else ----
        // 只有上一个xif判断为false，这里才展示
        const enableElse = opt.groupState.xIfFalseIndex > -1 && opt.groupState.xIfFalseIndex === opt.index - 1;
        if (!enableElse) {
          return;
        }
      }

      // ---- 「指令」x-for ----
      if (tagAttr[E.DIRECT.xFor]) {
        const dataList = U.getData(opt.data, tagAttr[E.DIRECT.xFor]);
        if (!dataList || !dataList.length) {
          return;
        }
        // ---- 「指令」x-item ---- 默认$取遍历出的item，可通过x-item设置别名
        const itemKey = tagAttr[E.DIRECT.xItem] || '$';
        return dataList
          .reduce((memo, item, index) => {
            // 除特殊属性外继续解析
            const newNode = copyNode(node, {
              tagAttr: {
                [E.DIRECT.xFor]: null,
                [E.DIRECT.xItem]: null,
              },
            });
            const isArr = U.isArray(item);
            // eslint-disable-next-line no-nested-ternary
            const itemVal = isArr
              ? [...item]
              : typeof item === 'string'
                ? { text: item }
                : {
                  ...(item || {}),
                };
            itemVal.index = index;
            const context = {
              ...opt,
              data: {
                ...opt.data,
                // 约定的特殊字段
                [itemKey]: itemVal,
              },
            };
            checkAttr(readNode(newNode).tagAttr, context.data);
            const groupList = parseNodeList([newNode], context);
            if (groupList.length) {
              memo.push(...groupList);
            }
            return memo;
          }, [])
          .join('');
      }


      checkAttr(tagAttr, opt.data);

      // ---- 「标签」Template使用 ----
      if (CONTEXT.component[tagName]) {
        // 组件本质是一个特殊的Fragement节点，仅渲染内部组件，且仅根据props包含必要的状态
        const newNodeInfo = readNode(
          copyNode(node, {
            tagName: E.COMP.fragment,
            children: CONTEXT.component[tagName],
          })
        );
        // 透传所有状态给组件内部，组件上的props可以改写别名
        const componentState = {
          ...opt.data,
        };
        Object.keys(tagAttr).forEach((key) => {
          // 非特殊指令，是属性传递
          if (!E.DIRECT[key]) {
            // 组件外的上下文数据搬运到组件内
            componentState[key] = U.getData(opt.data, tagAttr[key]);
          }
        });
        // ---- 组件插槽 ----
        if (children.length) {
          // ---- 关键字children ----
          componentState.children = renderChildren();
        }

        return renderTag({
          tagName: newNodeInfo.tagName,
          tagAttr: newNodeInfo.tagAttr,
          data: componentState,
          renderChildren: () => renderChildren(newNodeInfo.children, { data: componentState }),
        });
      }
      return renderTag({
        tagName,
        tagAttr,
        data: opt.data,
        renderChildren,
      });
      // 渲染子节点，支持更改子节点上下文
      function renderChildren(n = children, contextOption = opt) {
        return parseNodeList(n, contextOption).join('');
      }
    }

    function checkAttr(attrData, context) {
      // 处理动态赋值class名
      if (attrData[E.DIRECT.xC]) {
        let data = U.getData(context, attrData[E.DIRECT.xValue]);
        // 没有上下文取值，则以该字符为class名
        if (!data) {
          data = attrData[E.DIRECT.xValue];
        }
        const key = attrData[E.DIRECT.xC];
        const revert = key[0] == '!';
        const result = U.getData(context, revert ? key.slice(1) : key);
        const enable = revert ? !result : !!result;
        if (enable) {
          attrData.class = `${attrData.class || ''} ${data}`;
        }
      }
    }

    // 执行节点渲染
    function renderTag({ tagName, tagAttr, data, renderChildren }) {
      let attrString = getAttrString(tagAttr, data);
      attrString = attrString ? ` ${attrString}` : '';
      // ---- 「标签」单闭合html ----
      if (E.VOID_ELEMENT[tagName]) {
        return [`<${tagName}${attrString}/>`];
      }
      const childrenString = renderChildren();
      // ---- 「标签」Fragment ----
      if (tagName === E.COMP.fragment) {
        return [childrenString];
      }
      return [`<${tagName}${attrString}>${childrenString}</${tagName}>`];
      // 序列化标签属性
      function getAttrString(attrs, context) {
        return Object.keys(attrs)
          .reduce((memo, key) => {
            const val = U.parseTemplate(attrs[key], context);
            if (!E.DIRECT[key] && !U.inValid(val)) {
              memo.push(`${key}="${val}" `);
            }
            return memo;
          }, [])
          .join('');
      }
    }
  }
  // ----- 常量模块 -----
  function initEnum() {
    // 支持的特殊标签
    const DIRECT = {
      xIf: 'x-if',
      xC: 'x-c',
      xValue: 'x-value',
      xElse: 'x-else',
      xFor: 'x-for',
      xItem: 'x-item',
      xName: 'x-name',
      'x-if': 1,
      'x-for': 1,
      'x-else': 1,
      'x-item': 1,
      'x-name': 1,
      'x-c': 1,
      'x-value': 1,
    };
    // token中对应节点属性的位置
    const TOKEN_VALUE = {
      type: 0,
      children: 1,
      tagName: 2,
      tagAttr: 3,
    };
    // 特殊标签，区分Html标签，都要是大写
    const COMP = {
      fragment: 'Fragment',
      template: 'Template',
      Fragment: 1,
      Template: 1,
    };
    // 原生html中单闭合标签
    const VOID_ELEMENT = {
      area: 1,
      base: 1,
      basefont: 1,
      br: 1,
      col: 1,
      command: 1,
      embed: 1,
      frame: 1,
      hr: 1,
      img: 1,
      input: 1,
      isindex: 1,
      keygen: 1,
      link: 1,
      meta: 1,
      param: 1,
      source: 1,
      track: 1,
      wbr: 1,
    };
    return {
      DIRECT,
      COMP,
      TOKEN_VALUE,
      VOID_ELEMENT,
    };
  }
  // ----- 工具模块 -----
  function initUtil() {
    return {
      getData,
      inValid,
      isArray,
      parseTemplate,
    };
    function getData(data, path, defaultValue) {
      if (data && path) {
        // eslint-disable-next-line no-useless-escape
        const keys = path.match(/([^\.\[\]"']+)/g);

        for (let i = 0, len = keys.length; i < len; i++) {
          data = data[keys[i]];
          if (inValid(data)) {
            return defaultValue;
          }
        }
        return data;
      }
      return defaultValue;
    }
    function inValid(e) {
      return typeof e === 'undefined' || e === null;
    }
    function isArray(e) {
      return Object.prototype.toString.call(e) === '[object Array]';
    }
    function parseTemplate(str, data) {
      try {
        data = data || {};
        return (
          (str || '').trim().replace(/\{\{([^{}]+)\}\}/g, (matched, group) => {
            const d = getData(data, group.trim());
            return inValid(d) ? '' : d;
          }) || ''
        );
      } catch (err) {
        console.error(err);
      }
      return '';
    }
  }
}
