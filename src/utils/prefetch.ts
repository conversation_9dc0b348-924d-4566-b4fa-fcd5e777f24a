import { getAppName } from './device';
import { RAX_CACHE_PATH_QUERY_WHITE } from '../constant/rax';
import { checkIsPre } from './rax';

export function getPreloadCacheKey(request, reqUrl: URL, kvConfig?) {
  try {
    const { hostname, pathname, searchParams } = reqUrl;

    const isPre = checkIsPre(reqUrl);
    const envTag = isPre ? 'envTag=pre' : '';

    // 静态SSR
    if (kvConfig) {
      // 不包含指定参数才生效
      if (kvConfig.excludeKey) {
        let urlTag = false;
        kvConfig.excludeKey.forEach(item => {
          const keyValue = searchParams.get(item) || '';
          if (keyValue) {
            urlTag = true;
          }
        })
        if (urlTag) {
          return '';
        }
      }
      if (kvConfig.key && kvConfig.key.length) {
        let searchStr = '';
        kvConfig.key.forEach(item => {
          const keyValue = searchParams.get(item) || '';
          searchStr = searchStr ? `${searchStr}&${item}=${keyValue}` : `${item}=${keyValue}`
        })
        return `http://${hostname}${pathname}?${searchStr}${(searchStr && envTag)? `&${envTag}` : envTag}`;
      } else {
        return `http://${hostname}${pathname}?${envTag ? `?${envTag}`: ''}`;
      }
    }

    // 获取IP，未登录时使用
    const clientIP = request.headers.get("Ali-Cdn-Real-Ip");
    // 获取APPName
    const ua = request.headers.get('user-agent') || '';
    const appName = getAppName(ua) || 'H5';
    // 获取userid
    const cookie = request.headers.get('cookie') || '';
    const matches = cookie.match(/unb=(\d+);/);
    let cookieUserId = '';
    if (matches && matches.length > 1) {
      cookieUserId = (matches[1])
    }


    const searchStr = searchParams.toString().split('&');
    let searchObj: any = {};

    searchStr.forEach((item) => {
      const itemObj = item.split('=');
      searchObj[itemObj[0]] = itemObj[1];
    })

    if (searchObj['_fz_cli_cache_key']) {
      searchObj = {
        '_fz_cli_cache_key': searchObj['_fz_cli_cache_key']
      }
    } else if (searchObj['_preKeyParams'] || RAX_CACHE_PATH_QUERY_WHITE[pathname]) {
      const queryList = searchObj['_preKeyParams'] ? 
      decodeURIComponent(searchObj['_preKeyParams']).split(',') :
      RAX_CACHE_PATH_QUERY_WHITE[pathname];
      if (queryList.length > 0) {
        let newSearchObj = {};
        queryList.forEach(item => {
          if (searchObj[item]) {
            newSearchObj[item] = searchObj[item];
          }
        })
        searchObj = {
          ...newSearchObj
        }
      }
    }

    if (cookieUserId) {
      searchObj.erUserId = cookieUserId;
    } else if (!searchObj['_fz_web_base_cache_key']){
      // 代理服务机器有多台IP不一致，代理场景统一走_fz_web_base_cache_key
      searchObj.erClientIP = clientIP;
    }
    searchObj.erAppName = appName;

    // 排序，避免生成的key不一致
    let keyMap = Object.keys(searchObj).sort();
    // 需要过滤的业务参数
    if (searchObj['_cache_ignore_key']) {
      keyMap = keyMap.filter(key => !searchObj['_cache_ignore_key'].includes(key))
    }
    // 过滤,转小写
    const KEY_MAP_FILTER = {
      'scm': true,
      'spm': true,
      'spmurl': true,
      'ttid': true,
      'fpt': true,
      'deviceid': true,
      'client_version': true,
      'client_type': true,
      'frm': true,
      'needlogin': true,
      '_fli_request_code': true,
      'hasback': true,
      'nsts': true,
      'imei': true,
      'nsns': true,
      '_fli_delay_init': true,
      '_fli_anim_type': true,
      'pre_pageversion': true,
      '_projver': true,
      '_fm_webview_first_': true,
      '_fm_real_host_': true,
      'page_env': true,
      'aplus_track_debug_id': true,
      '_pressr': true,
      '_premaxage': true,
      '_prenotclear': true,
      '_use_stream': true,
      '_fli_online': true,
      '_use_three_part_stream': true,
      '_force_cache':true,
      '_cachetest': true,
      '__back_url': true,
      '_ariver_appid': true,
      'aio_x_id': true,
      'utparam': true,
      'source': true,
      'enableloadingview': true,
      '_cache_ignore_key': true,
      'alipayhomepage': true,
      'alipaylayertired': true,
      'openwindowtime': true,
      '_er_cache': true,
      '_bx-m': true,
      'atom_params': true,
      '__fz_uid__': true,
      '_wx_user_info_': true,
      '_fz_web_base_cache_key': true,
      '_deletepre': true,
      '_fli_interceptor_start_time': true,
      '_prereage': true,
      "_fli_router_spm": true,
      "linkkey": true,
      "_css_inline": true,
      "_inline_css": true,
      "fromhome": true,
      "extttid": true,
      "extfpt": true,
      "openstarttime": true,
      "webviewloadtime": true,
      '_sw_prefetch': true,
      '_http_cache': true,
      '_http_etag': true,
      'openid': true,
      'unionid': true,
      "atom_show": true,
      "wx_er_stream": true,
      "webview_transparent_title": true,
      "trainExParams": true
    }
    const keyMapList = keyMap.filter(key => !KEY_MAP_FILTER[key.toLocaleLowerCase()]).map((key) => {
      return `${key}=${searchObj[key]}`;
    });
    if(isPre){keyMapList.push(envTag)}
    const keyStr = keyMapList.join('&');
    return `http://${hostname}${pathname}?${keyStr}`;
  } catch (e) {
    return ''
  }
}