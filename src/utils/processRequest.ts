export function addGzipHeader(headers: Headers) {
  try {
    headers.set('Accept-Encoding', 'gzip');
  } catch(err) {

  }
}

export function reduceHeaders(headers: Headers) {
  try {
    [
      'ali-origin-real-url',
      'ali-swift-private-hash-key',
      'ali-proxy-consistent-hash',
      'ali-tengine-hash-key',
      'host',
      'x-client-scheme',
    ].forEach(key => {
      headers.delete(key);
    });
  } catch {}
}

/**
 * 处理手淘首猜参数
 * @param {String} searchString
 */
export function processTBSeachParams(searchString: string): string {
  try {
    let result = searchString;
    if (searchString) {
      result = result.replace('?', '');
      const resultArr = result.split('&').filter((item) => {
        return (
          item.indexOf('utparam=') < 0 &&
          item.indexOf('_fli_nav_dynamic_router_start_time=') < 0 &&
          item.indexOf('_fli_nav_dynamic_router_end_time=') < 0 &&
          item.indexOf('imei=') < 0
        );
      });
      if (result.length > 0) {
        result = `?${resultArr.join('&')}`;
      } else {
        result = '';
      }
    }
    return result;
  } catch (err) {
    return searchString;
  }
}
