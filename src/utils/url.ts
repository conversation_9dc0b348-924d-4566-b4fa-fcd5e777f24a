const defaultOption = {
  // 重要参数靠前放
  queryOrder: [],
};

export function simpleParseUrl (url = '', options = {}): SimpleURL {
  return new SimpleURL(url, options);
}
export function addUrlQuery (urlString: string, paramsObject) {
  if (!paramsObject || !urlString) {
    return urlString;
  }
  const queryUnit = urlString.indexOf('?') > -1 ? '&' : '?';
  const paramsString = Object.keys(paramsObject).reduce((memo, key) => {
    if (typeof paramsObject[key] === 'undefined' || paramsObject[key] === null) {
      return memo;
    }
    memo.push(`${key}=${paramsObject[key]}`);
    return memo;
  }, []);
  return `${urlString}${queryUnit}${paramsString.join('&')}`;
}

class SimpleURL {
  public protocol: string = '';
  public host: string = '';
  public pathname: string = '';
  public query: any = {};
  private options: any = {
    queryOrder: []
  };

  constructor(urlString, options) {
    this._init(urlString, options);
  }

  _init (urlString, options?: any) {
    const [urlstring = '', searchString = ''] = urlString.split('?');
    let [protocol = '', urlName = ''] = urlstring.split('://');
    // 无协议情况
    if (!urlName && protocol) {
      urlName = protocol;
      protocol = '';
    }
    const [host = '', ...paths] = urlName.split('/');
    this.options = {
      ...defaultOption,
      ...(this.options || {}),
      ...(options || {}),
    };
    this.search = searchString;
    this.protocol = protocol;
    this.host = host;
    this.pathname = paths.length ? `/${paths.join('/')}` : '';
  }

  get href () {
    return `${this.protocol}://${this.host}${this.pathname}${this.search}`
  }

  set href (urlString) {
    this._init(urlString);
  }

  get search () {
    const { options: { queryOrder = [] } = {}, query = {} } = this;
    const searchList = [];
    const temp = {};
    // 优先前置重要参数
    queryOrder.forEach(key => addKey(key));
    Object.keys(query).forEach(key => addKey(key));
    return searchList.length ? `?${searchList.join('&')}` : '';

    function addKey (key) {
      try {
        if (temp[key]) {
          return
        }
        const formated = format(query[key]);
        if (!formated) {
          return
        }
        searchList.push(`${key}=${formated}`);
        temp[key] = 1;
      } catch (err) { }
    }

    function format (e) {
      switch (typeof e) {
        case 'number':
          return e === e ? `${e}` : '';
        case 'string':
          return e && e !== 'null' ? encodeURIComponent(e) : '';
        case 'undefined':
          return ''
        case 'boolean':
          return `${e}`;
        case 'object':
          try {
            return encodeURIComponent(JSON.stringify(e))
          } catch (e) {
            return ''
          }
      }
      return '';
    }
  }

  set search (searchString) {
    this.query = toQuery(searchString);
  }

  /**
   *
   * @param key 对象 或 字符串
   * @param value
   */
  setQuery (key, value?: any) {
    switch (typeof key) {
      case 'object':
        this.query = {
          ...this.query,
          ...(key || {}),
        }
        return;
      case 'string':
        if (typeof value !== 'undefined') {
          this.query[key] = value;
        }
        return;
    }
  }

  getQuery (key, defaultVal?) {
    return this.query[key] || defaultVal;
  }

  filterQuery (filter) {
    if (typeof filter !== 'function') {
      return;
    }
    const query = this.query;
    Object.keys(query).forEach(key => {
      if (!filter(query[key], key)) {
        query[key] = '';
      }
    })
  }

  toString () {
    return this.href;
  }

  clone () {
    return new SimpleURL(this.toString(), this.options);
  }
}


function toQuery (searchString: string) {
  if (!searchString) {
    return {};
  }
  if (searchString[0] == '?') {
    searchString = searchString.slice(1);
  }
  const query = {};
  const temp = searchString.split('&');
  for (let i = 0, len = temp.length; i < len; i++) {
    const text = temp[i];
    const [key, val] = text.split('=');
    try {
      if (key && val) {
        query[key] = decodeURIComponent(val);
      }
    } catch (err) {
      // decodeURIComponent也会报错的，试试 decodeURIComponent("%aaa")
    }
  }
  return query;
}
