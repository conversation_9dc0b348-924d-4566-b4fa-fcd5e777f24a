import { Iterminal } from '../interface';

/**
 * 判断客户端
 * @param ua
 * @returns {string|*}
 */
export function getAppName(ua: string): string {
  if (!ua) return '';
  const appInfo = ua.match(/AliApp\((.*)\)/) || {};
  let appName = appInfo[1] && appInfo[1].split('/')[0];
  if (
    appName === 'TB' ||
    appName === 'taobao' ||
    appName === 'com.taobao.taobao'
  ) {
    appName = 'TB';
  }
  // 增加对支付宝发起service worker请求的兼容
  if (!appName && ua.match(/\sMYWeb\//)) {
    return 'AP'
  }
  return appName;
}

/**
 * 根据UA返回是PC还是mobile
 * 暂时只有mobile，保持和渲染服务一致
 * @param ua UA
 * @returns terminal
 */
export function getTerminal(ua: string): Iterminal {
  return 'mobile';
}


/**
 * 判断系统类型 from rxpi-env
 * @param ua
 * @returns {string}
 * getOSName(request.headers.get('user-agent') || '');
 */
export function getOSName(ua:string): string {
  if (!ua) return '';

  if (/(Android);?[\s/]+([\d.]+)?/.test(ua)) {
    return 'android'
  }

  if (/(iPhone\sOS)\s([\d_]+)/.test(ua)) {
    return 'ios';
  }

  return 'unknown';
}

/**
 * 获取titlebar默认高度
 * */
export function getTitleBarDefaultHeight(request, searchParams) {
  // titlebar数据
  let statusBarHeight = 0;
  let totalBarHeight = 44;
  let hasImmersiveQuery = false;
  let canUseImmersive = false;
  try {
    const cookie = request.headers.get('cookie') || '';
    const statusBarHeightMatches = cookie.match(/_fli_barHeight=(\d+(\.\d+)?);/);
    const totalBarHeightMatches = cookie.match(/_fli_titleHeight=(\d+(\.\d+)?);/);
    const ua = request.headers.get('user-agent') || '';
    const isTaobaoLiteApp = /TinyApp/i.test(ua);
    const isAndroid = getOSName(ua) === 'android';

    // 链接上是否有沉浸式参数
    hasImmersiveQuery = searchParams.get('disableNav') === 'YES' && searchParams.get('titleBarHidden') === '2';
    // 当前端是否支持沉浸式
    const appName = getAppName(ua);
    canUseImmersive = ['LX', 'LX-MINI', 'TB', 'LT', 'TM', 'BLX'].includes(appName) || isTaobaoLiteApp;

    // 状态栏高度
    statusBarHeight = Number(statusBarHeightMatches && statusBarHeightMatches[1]) || 0;
    if (isTaobaoLiteApp && searchParams.get('titleBarHidden') === '2') {
      statusBarHeight = statusBarHeight || (isAndroid ? 28 : 44);
    }

    // titlebar整体高度
    totalBarHeight = Number(totalBarHeightMatches && totalBarHeightMatches[1]) || (44 + statusBarHeight);
  } catch (e) {}

  return {
    statusBarHeight,
    totalBarHeight,
    hasImmersiveQuery,
    canUseImmersive,
  }
}


/**
 * 是否微信端
 */
export function isWechat(ua) {
  return /MicroMessenger/i.test(ua);
}