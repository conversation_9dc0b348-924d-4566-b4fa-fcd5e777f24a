import { simpleParseUrl } from './url';

describe('基础URL解析 ', () => {
  const protocol = 'https';
  const host = 'outfliggys.m.taobao.com';
  const pathname = '/app/trip/rx-travel-detail/pages/index';

  test('基本信息一致', function () {
    const search = '?a=1&b=2';
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}${search}`);
    expect(parsedUrl.host).toBe(host);
    expect(parsedUrl.pathname).toBe(pathname);
    expect(parsedUrl.protocol).toBe(protocol);
    expect(parsedUrl.href).toBe(`${protocol}://${host}${pathname}${search}`);
    expect(parsedUrl.toString()).toBe(`${protocol}://${host}${pathname}${search}`);
  });

  test('path/host修改', function () {
    const search = '?1=a&2=b';
    const nextHost = 'market.m.taobao.com';
    const nextPath = `detail${pathname}`;
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}${search}`);
    parsedUrl.host = nextHost;
    expect(parsedUrl.href).toBe(`${protocol}://${nextHost}${pathname}${search}`);
    parsedUrl.pathname = nextPath;
    expect(parsedUrl.href).toBe(`${protocol}://${nextHost}${nextPath}${search}`);
  });

  test('关键key前置', function () {
    const search = '?a=1&b=2&c=3&id=123&shopId=456';
    const nextSearch = '?id=123&shopId=456&a=1&b=2&c=3';
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}${search}`, {
      queryOrder: ['id', 'shopId'],
    });
    expect(parsedUrl.search).toBe(nextSearch);
  });

  test('增删query', function () {
    const search = '?a=1&b=2&c=3&id=123&shopId=456';
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}${search}`, {
      queryOrder: ['id', 'shopId'],
    });
    // 删除某个query
    parsedUrl.query.c = '';
    expect(parsedUrl.search).toBe('?id=123&shopId=456&a=1&b=2');
    // 新增某个query
    parsedUrl.query.d = '123';
    expect(parsedUrl.search).toBe('?id=123&shopId=456&a=1&b=2&d=123');
    expect(parsedUrl.href).toBe(`${protocol}://${host}${pathname}?id=123&shopId=456&a=1&b=2&d=123`);
    // 重置query
    parsedUrl.query = {
      test: '123',
      id: '456',
    };
    expect(parsedUrl.href).toBe(`${protocol}://${host}${pathname}?id=456&test=123`);

    parsedUrl.setQuery({
      _fli_online: true,
      _fli_webview: true,
      hybrid: true,
    });
    expect(parsedUrl.href).toBe(`${protocol}://${host}${pathname}?id=456&test=123&_fli_online=true&_fli_webview=true&hybrid=true`);
  });

  test('异常query容错', function () {
    const errorValue = '%aaa';
    const search = `?a=1&b=2&c=3&id=123&shopId=456&d=${errorValue}`;
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}${search}`, {
      queryOrder: ['id', 'shopId'],
    });
    expect(parsedUrl.href).toBe(`${protocol}://${host}${pathname}?id=123&shopId=456&a=1&b=2&c=3`);
    expect(parsedUrl.query.d).toBe(undefined);
  });

  test('直接设置search', function () {
    const search = '?a=1&b=2&c=3&id=123&shopId=456';
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}${search}`, {
      queryOrder: ['id', 'shopId'],
    });
    const nextSearch = '?d=456&id=999&m=222&shopId=1110';
    parsedUrl.search = nextSearch;
    expect(parsedUrl.search).toBe('?id=999&shopId=1110&d=456&m=222');
    expect(parsedUrl.href).toBe(`${protocol}://${host}${pathname}?id=999&shopId=1110&d=456&m=222`);
  });

  test('直接设置href', function () {
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}?test=1&id=1`, {
      queryOrder: ['id', 'shopId'],
    });
    const nextProtocol = 'http';
    const nextHost = 'm.taobao.com';
    const nextPathName = '';
    const nextSearch = '?test=2&shopId=22';
    const nextSortedSearch = '?shopId=22&test=2';
    parsedUrl.href = `${nextProtocol}://${nextHost}${nextPathName}${nextSearch}`;
    expect(parsedUrl.host).toBe(nextHost);
    expect(parsedUrl.pathname).toBe(nextPathName);
    expect(parsedUrl.protocol).toBe(nextProtocol);
    expect(parsedUrl.search).toBe(nextSortedSearch);
    expect(parsedUrl.href).toBe(`${nextProtocol}://${nextHost}${nextPathName}${nextSortedSearch}`);
  });

  test('clone后改值', function () {
    const parsedUrl = simpleParseUrl(`${protocol}://${host}${pathname}?test=1&id=1`, {
      queryOrder: ['id', 'shopId'],
    });
    const cloned = parsedUrl.clone();
    const nextHost = 'm.taobao.com';
    cloned.host = nextHost;
    // 原始未变
    expect(parsedUrl.host).toBe(host);
    // 克隆的改变
    expect(cloned.host).toBe(nextHost);
    // 克隆后保有原始期望的query顺序
    expect(cloned.toString()).toBe(`${protocol}://${nextHost}${pathname}?id=1&test=1`);
  });
});
