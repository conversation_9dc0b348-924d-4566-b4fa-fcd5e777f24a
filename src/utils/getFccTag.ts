function compareVersion(clientVersion, targetVersion) {
  if (!clientVersion) return;

  const cv = clientVersion.split(".");
  const tv = targetVersion.split(".");
  const d = cv.length;
  let i = -1;

  while (++i < d) {
    const _cv = +cv[i] || 0;
    const _tv = +tv[i] || 0;

    if (_cv > _tv) {
      return 1;
    } else if (_cv < _tv) {
      return -1;
    }
  }
  return 0;
}

export function getFccTag(request, isTest = false) {
  const userAgent = request.headers.get('user-agent') || '';
  const appInfo = userAgent.match(/AliApp\(([^)]+)\)/) || {};
  const appName = appInfo[1] && appInfo[1].split('/')[0];
  const appVersion = appInfo[1] && appInfo[1].split('/')[1];
  const isTaobao = appName === 'TB' || appName === 'taobao' || appName === 'com.taobao.taobao';
  const cookie = request.headers.get('cookie') || '';
  const matches = cookie.match(/_fz_fcc_enable=(\d+);/);
  const _fz_fcc_enable = matches && matches.length > 1 && matches[1] || '';
  const fccEnable = String(_fz_fcc_enable) === '1';

  return isTest || (isTaobao && compareVersion(appVersion, '10.43.40') >= 0 && fccEnable);
}