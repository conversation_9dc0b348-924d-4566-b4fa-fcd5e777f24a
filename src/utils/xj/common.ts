import { logger } from './logger';
import { XUANJI_PATH_SPLIT_REG } from '../../constant';

/**
 * 获取静态配置
*/
export const getKvCache = async ({ namespace = 'fliggy_xuanji', key, context }) => {
  try {
    const edgeKv = new EdgeKV({ namespace });
    const data = await edgeKv.get(key, { type: 'json' });
    return data;
  } catch (e) {
    await logger.record({
      logKey: 'getEdgeKVError',
      logName: '读取静态KV配置异常',
      content: { tracker: e.message },
      context
    });
    return null;
  }
}

/**
 * 路径拆分
*/
export const splitPath = (pathname: string) => {
  const match = pathname.match(XUANJI_PATH_SPLIT_REG);
  if (match) {
    const scenario = match[1]; //业务场景
    const site = match[2]; //站点
    const group = match[3]; //分组
    const page = match[4]; //页面
    return { scenario, site, group, page };
  }
  return {};
}