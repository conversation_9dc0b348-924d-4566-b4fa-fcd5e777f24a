/**
 * SLS日志
 */

class Logger {
  /**
   * 上报日志
   * @param source 场景，需要 @兆兆 注册
   * @param logKey 日志 key
   * @param logName 日志中文名称
   * @param content 日志内容
   * @param topic sls topic
   */
  async record({ logKey, logName, content, source = 'pcraft', topic = 'stability', context = {} }: any) {
    if (!content) return;
    try {
      const { isPre, appName, osName, ip, userId, cookie, url, ua } = context;
      const SLS_LOGGER_DOMAIN = isPre ? 'https://pre-log.fliggy.com' : 'https://log.fliggy.com'; //请求链接

      //发送日志
      await fetch(`${SLS_LOGGER_DOMAIN}/pcraft/er/record`, {
        method: 'POST',
        body: JSON.stringify({ source, topic, content: { logKey, logName, appName, osName, ip, userId, cookie, url, ua, ...content } }),
      });
    } catch (e) { }
  }
}

/**
 * SLS日志实例
 */
export const logger = new Logger();