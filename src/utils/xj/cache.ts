import { Iterminal } from "../../interface";

/**
 * 使用url作为页面缓存的key
 * url参数只保留会对页面渲染结果产生影响的参数，并给出统一的排序，以保障页面缓存的利用率，减少回源次数
 * @param {String} params
 */
export function getCacheUrl(host: string, pathname: string, params: URLSearchParams, terminal: Iterminal): string {
  const wh_pid = params.get('wh_pid');
  const itemIds = params.get('itemIds');
  const rid = params.get('rid');
  const topTabId = params.get('topTabId');
  const source_name = params.get('source_name');
  const ttid = params.get('ttid');
  const titleBarHidden = params.get('titleBarHidden');
  const disableNav = params.get('disableNav');

  const paramsObj = {
    wh_pid,
    itemIds,
    rid,
    topTabId,
    source_name,
    ttid,
    titleBarHidden,
    disableNav,
  };
  //保证顺序一致用于静态缓存
  const paramsList = [
    'wh_pid',
    'itemIds',
    'rid',
    'topTabId',
    'source_name',
    'ttid',
    'titleBarHidden',
    'disableNav',
  ];
  let search = '?';
  paramsList.forEach((item) => {
    if (paramsObj[item]) {
      search = `${search}${item}=${paramsObj[item]}&`;
    }
  });

  //terminal不同响应不同
  return `http://${host}${pathname}/${terminal}${search}`;
}