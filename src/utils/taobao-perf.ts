export const TAOBAO_PERF_SCRIPT = {
  fspEnd(property?) {
    let exParams ='{}';
    try {
      exParams = JSON.stringify(property || {})
    }catch (err){
    }
    return format(`
try {streamEnd();} catch(err) {}

function streamEnd() {
  if (!performance || typeof performance.mark !== 'function') {
    return;
  }
  window.dispatchEvent(new Event('ssr-first-chunk-end'));
  performance.mark('stage-first-screen');
  performance.mark('ssr-end');
  if (document.readyState == 'complete') {
    setTimeout(logger, 0);
  } else {
    document.addEventListener('DOMContentLoaded', function () {
      setTimeout(logger, 0);
    });
  }

  function logger() {
    var property = {
      renderMode: 'SSR',
      renderStrategy: 'ssr'
    };
    var stages = {};
    var timeDiff = Date.now() - performance.now();
    if (!performance.getEntriesByType) {
      return;
    }
    var resources = performance.getEntriesByType('mark');
    var keys = ['stage', 'ssr-end'];
    resources.forEach(function (entry) {
      var { name } = entry;
      if (keys.indexOf(name) > -1) {
        stages[name] = entry.startTime + timeDiff;
      }
    });

    var navigationEntry = performance.getEntriesByType('navigation');
    var pageNav = navigationEntry[0];
    if (pageNav) {
      stages['ttfb'] = pageNav.responseStart + timeDiff;
    }

    var fcpEntry = performance.getEntriesByName('first-contentful-paint');
    var fcp = fcpEntry[0];
    if (fcp) {
      stages['fcp'] = fcp.startTime + timeDiff;
    }
    try {
      var exParams = ${exParams};
      var renderInfo = window._$renderInfo || {};
      Object.assign(property, renderInfo);
      Object.assign(property, exParams);

      if (window._er_perf) {
        var fliggyEr = "";
        try {
          fliggyEr = JSON.stringify(window._er_perf);
        }catch(err) {
          fliggyEr = err.message;
        }
        property.fliggyEr = fliggyEr;
        window._er_perf.taobao = {
          property: property,
          stages: stages,
        };
      }
      report();
    }catch(err){
      console.error(err);
    }


    function report() {
      var noop = function (){};
      if (typeof window !== 'undefined' && window.__megability_bridge__ && window.themis) {
        Object.keys(property).forEach(function (key) {
          window.__megability_bridge__.asyncCall(
            'APM',
            'addPageProperty',
            {
              key: key,
              value: property[key],
            },
            noop,
          );
        });
        Object.keys(stages).forEach(function (key) {
          window.__megability_bridge__.asyncCall(
            'APM',
            'addPageStage',
            {
              key: key,
              value: stages[key],
            },
            noop,
          );
        });
      } else if (typeof window.__windvane__ !== 'undefined') {
        window.__windvane__.call(
          'WVPerformance.onProperty',
          {
            property: property,
          },
          noop,
          noop,
        );
        window.__windvane__.call(
          'WVPerformance.onStage',
          {
            stage: stages,
          },
          noop,
          noop,
        );
      }
    }
  }
}
  `)
  }
}


function format(scriptString) {
  return scriptString
    .split('\n')
    .map(n => n.trim())
    .join('');
}
