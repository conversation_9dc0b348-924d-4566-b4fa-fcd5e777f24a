import { PRE_RAX_SSR_HOST, RAX_SSR_HOST, WX_HOST, STREAM_FORCE_MAP, STREAM_FORCE_CLOSE_ARR } from "../../constant/rax";
import { isWechat } from '../device';

// 获取第一屏数据缓存Key
export function getfirstPartCacheKey(reqUrl, keys = [], isGray = false) {
  for (let i = 0; i < keys.length; i++) {
    const data = reqUrl.searchParams.get(keys[i]);
    if (data) {
      return `${reqUrl.pathname}?key=${keys[i]}_${data}${isGray ? '&is_gray=1' : ''}`;
    }
  }

  return `${reqUrl.pathname}${isGray ? '?is_gray=1' : ''}`;
}

// 获取缓存
export async function getJsonCache(cacheKey, isPre = false) {
  try {
    const envPreTag = cacheKey.indexOf("?") === -1 ? `?envTag=pre` : `&envTag=pre`
    const data = await cache.get(`http://${isPre ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}${cacheKey}${isPre ? envPreTag : ''}`)
    const jsonData = await data.json();
    return {
      cacheKey,
      errorMessage: '',
      data: jsonData
    }
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
      data: null
    }
  }
}

// 设置缓存
export async function setJsonCache(cacheKey, jsonData, seconds = 60 * 60, isPre = false) {
  try {
    const jsonString = JSON.stringify(jsonData);
    const response = new Response(jsonString)
    response.headers.set('cache-control', `max-age=${seconds}`)
    const envPreTag = cacheKey.indexOf("?") === -1 ? `?envTag=pre` : `&envTag=pre`
    const result:any = await cache.put(`http://${isPre ? PRE_RAX_SSR_HOST : RAX_SSR_HOST}${cacheKey}${isPre ? envPreTag : ''}`, response);
    return {
      cacheKey,
      errorMessage: result || '',
    }
  } catch (err) {
    return {
      cacheKey,
      errorMessage: err.message,
    }
  }
}

export function getMetaData(htmlString, metaName) {
  try {
    const reg = new RegExp(`<meta\\s+name="?'?${metaName}"?'?\\s+content="?'?([^"'>]*)'?"?\/?>`);
    const result = htmlString.match(reg);
    if (result) {
      return result[1] || ''
    }
  } catch (err) {
  }
  return ''
}

export function parseTemplate(str, data) {
  try {
    data = data || {};
    return {
      data: (str || '').replace(/\{\{([a-zA-Z0-9]+)\}\}/g, function (matched, group) {
        return data[group] || ''
      }) || '',
      errorMessage: ''
    };
  } catch (err) {
    return {
      data: '',
      errorMessage: err.message
    }
  }
}

// 判断是否流式
export function judgeIsStream(reqUrl: URL, reqHeaders: Headers) {
  const { searchParams, hostname, pathname } = reqUrl;
  const isWxEr = WX_HOST.includes(hostname)
  const proxyDisable = isWechat(reqHeaders.get('user-agent') || '') && !searchParams.has('wx_er_stream') && !isWxEr;
  return !proxyDisable && !searchParams.has('_pressr') && !searchParams.has('_up_snapshot') && !searchParams.has('_force_cache') && (searchParams.has('_use_stream') || STREAM_FORCE_MAP[pathname]) && !STREAM_FORCE_CLOSE_ARR.includes(pathname);
}
