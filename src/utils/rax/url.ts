import {
  WX_HOST,
  GD_HOST,
  RAX_SSR_HOST,
  PRE_RAX_SSR_HOST,
  NEW_RAX_SSR_HOST,
  NEW_PRE_RAX_SSR_HOST,
  VITE_SSR_HOST,
  VITE_PRE_SSR_HOST
} from '../../constant/rax';

export function checkRaxRedirectUrl(origin, hostname, request, isStream = false, isPre = true) {
  try {
    // 参数携带csrSamesite走同域名降级
    const isSameSite = origin.search.includes('csrSamesite') || request.headers.get('x-csr-same-site') === 'true';

    if (WX_HOST.includes(hostname)) {
      // 微信套壳降级域名用当前域名，降级成csr链路
      origin.host = hostname;
      if (origin.search) {
        // _fm_real_host_替换
        origin.search = origin.search
          .replace('_fm_real_host_=pre-fliggyrax.wapa.taobao.com', '_fm_real_host_=market.wapa.taobao.com')
          .replace('_fm_real_host_=outfliggys.wapa.taobao.com', '_fm_real_host_=market.wapa.taobao.com')
          .replace('_fm_real_host_=outfliggys.m.taobao.com', '_fm_real_host_=market.m.taobao.com');
      }
    } else if (isSameSite) {
      origin.host = hostname;
      origin.pathname = `/csr${origin.pathname}`;
    } else if(GD_HOST.includes(hostname)){
      origin.host = hostname.replace('front-traffic-fliggy-er', 'front-traffic-fliggy')
    }else if(isStream){
      origin.host = isPre ? 'market.wapa.taobao.com' : 'market.m.taobao.com'
    }
  } catch (e) { }
}

export function checkIsPre(reqUrl, isUrlObj = true) {
  const urlObj = isUrlObj ? reqUrl : new URL(reqUrl);
  const isPre = !(urlObj.hostname === 'outfliggys.m.taobao.com' ||
    urlObj.hostname === 'h5-er.fliggy.cn' ||
    urlObj.hostname === 'front-traffic-fliggy-er.amap.com' ||
    (WX_HOST.includes(urlObj.hostname) && !urlObj.hostname.includes('pre-')));
  return isPre
}

// V:判断是否是vite
export function checkIsVite(reqUrl, isUrlObj = true){
  const urlObj = isUrlObj ? reqUrl : new URL(reqUrl);
  const { pathname } = urlObj;
  const isVite = pathname.match(/\/vite\/app\/trip\/(\S*)\/pages\//) && pathname.match(/\/vite\/app\/trip\/(\S*)\/pages\//)[1];
  return !!isVite;
}


export function getHost(reqUrl) {
  const { searchParams } = reqUrl;
  const isPre = checkIsPre(reqUrl) && !searchParams.has('_faas_online');
  if(checkIsVite(reqUrl)){
    // V:判断是否是vite
    if (isPre) {return VITE_PRE_SSR_HOST}
    return VITE_SSR_HOST;
  }else{
    if (isPre) {return NEW_PRE_RAX_SSR_HOST}
    return NEW_RAX_SSR_HOST;
  }
}

export function getPageName (url){
  try {
    const groupName = (url.match(/\/app\/trip\/(\S*)\/pages\//) && url.match(/\/app\/trip\/(\S*)\/pages\//)[1]) ||
    (url.match(/\/app\/alitriprx\/(\S*)\/pages\//) && url.match(/\/app\/alitriprx\/(\S*)\/pages\//)[1]) ||
    (url.match(/\/app\/btrip-fe\/(\S*)\/pages\//) && url.match(/\/app\/btrip-fe\/(\S*)\/pages\//)[1]);
    const serviceName = url.includes('?') ? (url.match(/\/pages\/(\S*)\?/) && url.match(/\/pages\/(\S*)\?/)[1]) :
    (url.match(/\/pages\/(\S*)/) && url.match(/\/pages\/(\S*)/)[1]);
    return {
      groupName,
      serviceName
    }
  } catch(e) {
    return {
      groupName: '',
      serviceName: ''
    }
  }
}
