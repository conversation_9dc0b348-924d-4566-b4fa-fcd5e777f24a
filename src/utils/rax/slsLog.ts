import {
  getAppName,
  getOSName
} from '../../utils';

type ITimeLog = {
  timeStamp?: object;
  url?: string;
  userId?: string;
  preCacheUrl?: string;
}

type ILogReqParam = {
  event: FetchEvent;
  reqUrl?: URL;
  logConfig: ILogConfig;
  pathname?: string;
}

type ILogParam = {
  headers: Headers;
  reqUrl: URL;
  logConfig: ILogConfig;
  pathname?: string;
  isPre: boolean;
}

type ILogConfig = {
  logName?: string;
  logType?: string;
  logData: object;
}

export class SlsLog {
  static timeLog: any = {}

  static setTimeLog (val: ITimeLog) {
    this.timeLog = {
      ...this.timeLog,
      ...val
    };
  }

  static getTimeLog () {
    return this.timeLog;
  }

  static clearTimeLog () {
    this.timeLog = {};
  }

  static async logFunc (params: ILogParam) {
    const { headers, reqUrl, pathname, logConfig, isPre } = params;
    const {
      logName = isPre ? 'ssr_pre_server_log' : 'ssr_server_log',
      logType = 'server',
      logData
    } = logConfig;
    let groupName = '';
    let serviceName = '';
    let userId = '';
    let ip = '';
    let appname = '';
    let osname = '';

    try {
      const reqPathArr = (pathname || reqUrl && reqUrl.pathname || '').split('/');
      groupName = reqPathArr[3];
      serviceName = reqPathArr[5];

      // 获取IP，未登录时使用
      ip = headers.get("Ali-Cdn-Real-Ip") || '';
      // 获取userid
      const headerCookie = headers.get('cookie') || '';
      const matchesReg = headerCookie.match(/unb=(\d+);/);
      if (matchesReg && matchesReg.length > 1) {
        userId = matchesReg[1] || '';
      };
      // 操作系统
      const userAgent = headers.get('user-agent') || '';
      osname = getOSName(userAgent);
      // 端环境
      appname = getAppName(userAgent) || 'OTHER';
    } catch { }

    Object.keys(logData).map(item => {
      logData[item] = JSON.stringify(logData[item]);
    })
    const data = {
      groupName,
      serviceName,
      logData: {
        ...logData,
        userId,
        ip,
        appname,
        appName: appname,
        osname,
        osName: osname,
      },
      logName,
      logType
    };

    await fetch("https://log.fliggy.com/api/rax/record", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ param: data }),
      mode: 'cors',
    });
  }

  static sendLog (params: ILogReqParam) {
    const {
      event,
      reqUrl,
      logConfig,
      pathname,
    } = params;
    const { request } = event;
    const isPre = !(request.url.indexOf('outfliggys.m.taobao.com') >= 0 ||
      request.url.indexOf('proxy-er.feizhu.com') >= 0 ||
      request.url.indexOf('proxy-er.fzwxxcx.com') >= 0 ||
      request.url.indexOf('proxy-er.fzwxxcx.cn') >= 0 ||
      request.url.indexOf('h5-er.fliggy.cn') >= 0 ||
      request.url.indexOf('front-traffic-fliggy-er.amap.com') >= 0); 
    try {
      event.waitUntil(this.logFunc({
        headers: request.headers,
        reqUrl,
        pathname,
        logConfig,
        isPre
      }))
    } catch (e) {
    }
  }

}
