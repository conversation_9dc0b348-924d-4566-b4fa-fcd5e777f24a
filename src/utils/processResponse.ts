/**
 * 判断是否是重定向
 * 如果是重定向返回重定向的目标地址，否则返回空
 */
export function getRedirectUrl(res: Response): string | false  {
  let redirectUrl = '';
  if (res.status === 302) {
    redirectUrl = res.headers.get('location') || '';
    // 多个只取第一个;
    redirectUrl = redirectUrl.split(',')[0];
  }
  return redirectUrl;
}

/**
 * 缩减缓存的大小
 */
export function removeErCacheHeaders(headers: Headers) {
  const keys = [
    'ali-proxy-consistent-hash',
    'ali-cdn-namespace',
    'ali-cdn-real-ip',
    'ali-swift-edge-dns-lookup',
    'ali-swift-global-savetime',
    'ali-swift-kv',
    'ali-swift-log-host',
    'ali-swift-stat-host',
    'x-client-scheme',
    'x-forwarded-for',
    'x-swift-savetime',
    'x-tworker-via',
    'x-tworker-fetch-uuid'
  ];
  for (const _k of keys) {
    headers.delete(_k);
  }
}

/**
 * 清除文档的缓存控制
 */
export function removeHtmlCacheHeaders(headers: Headers) {
  const keys = ['cache-control', 'age', 'etag', 'vary'];
  for (const _k of keys) {
    headers.delete(_k);
  }
}
