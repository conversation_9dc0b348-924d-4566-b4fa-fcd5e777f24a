export function parseRequestEnv(headers) {
  try {
    const headerCookie = headers.get('cookie') || '';
    const userAgent = headers.get('user-agent') || headers.get('User-Agent') || '';
    const userIdReg = headerCookie.match(/unb=(\d+);/);
    const cookieUserId = (userIdReg && userIdReg[1]) || '';

    const osName = checkSystem(userAgent);
    const appName = (getAppName(userAgent) || '').toLowerCase();
    const env: any = {
      userId: cookieUserId,
      osName,
      appName,
      isIOS: osName == 'ios' ? 1 : 0,
      isAndroid: osName == 'android' ? 1 : 0,
      isHarmony: osName == 'harmony' ? 1 : 0,
      isXhsMiniAppH5: appName == 'xhsmini' ? 1 : 0,
      isWeChatMiniProgramH5: appName == 'wxmini' ? 1 : 0,
      isByteDanceMicroApp: appName == 'douyinmini' ? 1 : 0,
      isTaobao: appName === 'tb' || appName === 'tb_lite' ? 1 : 0,
      isTaobaoLiteApp: appName === 'tb_lite' ? 1 : 0,
      isTaobaoTJ: appName == 'lt' ? 1 : 0,
      isTaobaoLive: appName == 'taobaoliveapp',
      isTmall: appName == 'tm' ? 1 : 0,
      isBTrip: appName == 'blx' ? 1 : 0,
      isTrip: appName === 'lx' ? 1 : 0,
      isAlipay: appName === 'ap' ? 1 : 0,
    };

    // 特殊定制，支付宝安卓不支持流式
    if (env.isAlipay && env.isAndroid) {
      env.isAlipayAndroid = 1;
    }

    env.clientType = `${appName || 'h5'}_${osName}`;

    // 应仅包含有值/当前环境数据，便于多个开关配置交叉命中
    return Object.keys(env).reduce((memo, key) => {
      if (env[key]) {
        memo[key] = env[key];
      }
      return memo;
    }, {});
  } catch (err) {
    return {
      errorMessage: err.message,
    };
  }
}

function checkSystem(ua) {
  if (/(Android);?[\s/]+([\d.]+)?/.test(ua)) {
    return 'android';
  }

  if (/(iPhone\sOS)\s([\d_]+)/.test(ua)) {
    return 'ios';
  }

  if (/openHarmony/i.test(ua)) {
    return 'harmony';
  }

  return 'unknown';
}

function getAppName(ua) {
  const appInfo = ua.match(/AliApp\((.*)\)/);
  let appName = '';
  if (appInfo && appInfo[1]) {
    appName = appInfo[1].split('/')[0] || '';
  }

  if (appName) {
    appName = appName.toLowerCase();
    const alias = {
      alipay: 'ap',
      'com.taobao.taobao': 'tb',
      'tb-pd': 'tb',
      taobao: 'tb',
    };
    appName = alias[appName] || appName;
    if (appName == 'tb' && /TinyApp/i.test(ua)) {
      return 'tb_lite';
    }
    return appName;
  }

  if (ua.match(/\sMYWeb\//)) {
    return 'ap';
  }
  const lowUA = ua.toLowerCase();
  if (lowUA.indexOf('miniprogram') > -1 && lowUA.indexOf('micromessenger') > -1) {
    return 'wxmini';
  }
  if (lowUA.indexOf('xhsminiapp') > -1) {
    return 'xhsmini';
  }
  if (lowUA.indexOf('toutiaomicroapp') > -1) {
    return 'douyinmini';
  }

  return 'h5';
}

/**
 *
 * @param props 根据 query(访问url参数)、env(根据userAgent解析的环境识别)、cookie(ssr基于cookie传递沉侵参数)、forceEnable(页面基于meta和bridge强制开启沉侵)
 * @returns
 */
export function parseImmersiveState(props) {
  const { query = {}, env = {}, cookie = {}, forceEnable = false } = props;
  // titlebar数据
  const result = {
    enable: false,
    statusBarHeight: 0,
  };
  try {
    const fliggyImTag = query.titleBarHidden === '2';
    const taobaoImTag = query.disableNav === 'YES';
    const isFliggyEnv = env.isTrip || env.isBTrip;

    const specialStatus = checkStatus();
    let statusBarHeight = specialStatus > -1 ? specialStatus : cookie._fli_barHeight;
    statusBarHeight = +statusBarHeight || 0;
    result.statusBarHeight = statusBarHeight;

    if (isFliggyEnv && (fliggyImTag || forceEnable)) {
      result.enable = true;
    } else if (env.isTaobaoLiteApp && fliggyImTag) {
      result.enable = true;
      // 轻应用内，没取到也按环境给个默认值防抖
      result.statusBarHeight = statusBarHeight || (env.isAndroid ? 28 : 44);
    } else if (env.isTaobaoLive || env.isTmall) {
      result.enable = !!taobaoImTag;
    } else if (env.isTaobaoTJ || env.isTaobao) {
      result.enable = taobaoImTag || forceEnable;
    }
    // 不启用应该为0
    if (!result.enable) {
      result.statusBarHeight = 0;
    }
  } catch (e) {}

  return result;

  function checkStatus() {
    const STATUS_COOKIE_KEY = {
      taobao: '_fli_status_tb',
      taoLite: '_fli_status_tblite',
      taoAndroidTransparent: '_fli_status_tbandroid_transparent',
    };

    const { isTaobao, isTaobaoLiteApp, isAndroid, isHarmony } = env;
    if (isTaobao) {
      if (isTaobaoLiteApp) {
        return cookie[STATUS_COOKIE_KEY.taoLite];
      }
      if (isAndroid || isHarmony) {
        // 手淘安卓/鸿蒙特殊url参数强制关闭状态栏
        if (query.status_bar_transparent) {
          return cookie[STATUS_COOKIE_KEY.taoAndroidTransparent];
        }
        // 安卓手淘非额外参数控制一定是0
        return 0;
      }
      return cookie[STATUS_COOKIE_KEY.taobao];
    }
  }
}
